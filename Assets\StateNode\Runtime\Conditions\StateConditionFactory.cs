using System;
using System.Collections.Generic;

namespace StateScript
{
    /// <summary>
    /// Factory for creating state conditions based on type discriminator
    /// </summary>
    public static class StateConditionFactory
    {
        private static readonly Dictionary<string, Func<StateCondition>> _typeMap = 
            new Dictionary<string, Func<StateCondition>>
            {
                { "IntCompare", () => new IntCompareCondition() },
                { "AlwaysTrue", () => new AlwaysTrueCondition() },
                { "AlwaysFalse", () => new AlwaysFalseCondition() }
            };
        
        /// <summary>
        /// Create a state condition instance based on type name
        /// </summary>
        public static StateCondition CreateCondition(string typeName)
        {
            if (_typeMap.TryGetValue(typeName, out var factory))
            {
                return factory();
            }
            
            throw new ArgumentException($"Unknown condition type: {typeName}");
        }
        
        /// <summary>
        /// Register a custom condition type
        /// </summary>
        public static void RegisterConditionType<T>(string typeName) where T : StateCondition, new()
        {
            _typeMap[typeName] = () => new T();
        }
        
        /// <summary>
        /// Get all registered type names
        /// </summary>
        public static IEnumerable<string> GetRegisteredTypes()
        {
            return _typeMap.Keys;
        }
        
        /// <summary>
        /// Check if a type is registered
        /// </summary>
        public static bool IsTypeRegistered(string typeName)
        {
            return _typeMap.ContainsKey(typeName);
        }
    }
}

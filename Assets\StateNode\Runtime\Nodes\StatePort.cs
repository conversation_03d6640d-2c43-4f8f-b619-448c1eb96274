namespace StateScript
{
    /// <summary>
    /// Represents a connection point on a state node
    /// </summary>
    public class StatePort : ISerializable
    {
        public PortId Id { get; set; }
        public Fixed32String Name { get; set; }
        public PortType Type { get; set; }
        public NodeId NodeId { get; set; }
        
        public StatePort()
        {
            Id = GenerateUniqueId();
        }
        
        public StatePort(string name, PortType type, NodeId nodeId)
        {
            Id = GenerateUniqueId();
            Name = new Fixed32String(name);
            Type = type;
            NodeId = nodeId;
        }
        
        public void Serialize(ISerializer serializer)
        {
            serializer.WriteInt("id", Id.Value);
            serializer.WriteString("name", Name.ToString());
            serializer.WriteInt("type", (int)Type);
            serializer.WriteInt("nodeId", NodeId.Value);
        }
        
        public void Deserialize(ISerializer serializer)
        {
            Id = serializer.ReadInt("id");
            Name = new Fixed32String(serializer.ReadString("name"));
            Type = (PortType)serializer.ReadInt("type");
            NodeId = serializer.ReadInt("nodeId");
        }
        
        private static PortId GenerateUniqueId()
        {
            return UnityEngine.Random.Range(1, int.MaxValue);
        }
        
        public override string ToString()
        {
            return $"{Name} ({Type})";
        }
    }
    
    /// <summary>
    /// Represents a connection between two ports
    /// </summary>
    public class StateConnection : ISerializable
    {
        public PortId OutputPortId { get; set; }
        public PortId InputPortId { get; set; }
        
        public StateConnection() { }
        
        public StateConnection(PortId outputPortId, PortId inputPortId)
        {
            OutputPortId = outputPortId;
            InputPortId = inputPortId;
        }
        
        public void Serialize(ISerializer serializer)
        {
            serializer.WriteInt("outputPortId", OutputPortId.Value);
            serializer.WriteInt("inputPortId", InputPortId.Value);
        }
        
        public void Deserialize(ISerializer serializer)
        {
            OutputPortId = serializer.ReadInt("outputPortId");
            InputPortId = serializer.ReadInt("inputPortId");
        }
        
        public override string ToString()
        {
            return $"Connection: {OutputPortId} -> {InputPortId}";
        }
    }
}

namespace StateScript
{
    /// <summary>
    /// Base class for all state conditions
    /// </summary>
    public abstract class StateCondition : ISerializable
    {
        /// <summary>
        /// Type discriminator for polymorphic deserialization
        /// </summary>
        public abstract string TypeName { get; }
        
        /// <summary>
        /// Evaluate the condition
        /// </summary>
        public abstract bool Evaluate(StateFlow stateFlow, IStateContext context);
        
        public virtual void Serialize(ISerializer serializer)
        {
            serializer.WriteString("conditionType", TypeName);
        }
        
        public virtual void Deserialize(ISerializer serializer)
        {
            // ConditionType is handled by factory, not here
        }
    }
    
    /// <summary>
    /// Example condition that compares an integer variable to a value
    /// </summary>
    public class IntCompareCondition : StateCondition
    {
        public string VariableName { get; set; } = "";
        public int CompareValue { get; set; }
        public CompareOperator Operator { get; set; } = CompareOperator.Equal;
        
        public override string TypeName => "IntCompare";
        
        public IntCompareCondition() { }
        
        public IntCompareCondition(string variableName, CompareOperator op, int compareValue)
        {
            VariableName = variableName;
            Operator = op;
            CompareValue = compareValue;
        }
        
        public override bool Evaluate(StateFlow stateFlow, IStateContext context)
        {
            var currentValue = stateFlow.GetInt(VariableName);
            
            return Operator switch
            {
                CompareOperator.Equal => currentValue == CompareValue,
                CompareOperator.NotEqual => currentValue != CompareValue,
                CompareOperator.Greater => currentValue > CompareValue,
                CompareOperator.GreaterEqual => currentValue >= CompareValue,
                CompareOperator.Less => currentValue < CompareValue,
                CompareOperator.LessEqual => currentValue <= CompareValue,
                _ => false
            };
        }
        
        public override void Serialize(ISerializer serializer)
        {
            base.Serialize(serializer);
            serializer.WriteString("variableName", VariableName);
            serializer.WriteInt("compareValue", CompareValue);
            serializer.WriteInt("operator", (int)Operator);
        }
        
        public override void Deserialize(ISerializer serializer)
        {
            base.Deserialize(serializer);
            VariableName = serializer.ReadString("variableName");
            CompareValue = serializer.ReadInt("compareValue");
            Operator = (CompareOperator)serializer.ReadInt("operator");
        }
    }
    
    /// <summary>
    /// Example condition that always returns true
    /// </summary>
    public class AlwaysTrueCondition : StateCondition
    {
        public override string TypeName => "AlwaysTrue";
        
        public override bool Evaluate(StateFlow stateFlow, IStateContext context)
        {
            return true;
        }
    }
    
    /// <summary>
    /// Example condition that always returns false
    /// </summary>
    public class AlwaysFalseCondition : StateCondition
    {
        public override string TypeName => "AlwaysFalse";
        
        public override bool Evaluate(StateFlow stateFlow, IStateContext context)
        {
            return false;
        }
    }
    
    /// <summary>
    /// Comparison operators for conditions
    /// </summary>
    public enum CompareOperator
    {
        Equal,
        NotEqual,
        Greater,
        GreaterEqual,
        Less,
        LessEqual
    }
}

using System;
using System.Collections.Generic;

namespace StateScript
{
    /// <summary>
    /// Factory for creating state nodes based on type discriminator
    /// </summary>
    public static class StateNodeFactory
    {
        private static readonly Dictionary<string, Func<StateNode>> _typeMap = 
            new Dictionary<string, Func<StateNode>>
            {
                { "StateActionNode", () => new StateActionNode() },
                { "StateConditionNode", () => new StateConditionNode() },
                { "StateListenerNode", () => new StateListenerNode() }
            };
        
        /// <summary>
        /// Create a state node instance based on type name
        /// </summary>
        public static StateNode CreateNode(string typeName)
        {
            if (_typeMap.TryGetValue(typeName, out var factory))
            {
                return factory();
            }
            
            throw new ArgumentException($"Unknown node type: {typeName}");
        }
        
        /// <summary>
        /// Register a custom node type
        /// </summary>
        public static void RegisterNodeType<T>(string typeName) where T : StateNode, new()
        {
            _typeMap[typeName] = () => new T();
        }
        
        /// <summary>
        /// Get all registered type names
        /// </summary>
        public static IEnumerable<string> GetRegisteredTypes()
        {
            return _typeMap.Keys;
        }
        
        /// <summary>
        /// Check if a type is registered
        /// </summary>
        public static bool IsTypeRegistered(string typeName)
        {
            return _typeMap.ContainsKey(typeName);
        }
        
        /// <summary>
        /// Get the type name for a node instance
        /// </summary>
        public static string GetTypeName(StateNode node)
        {
            return node.GetType().Name;
        }
    }
}

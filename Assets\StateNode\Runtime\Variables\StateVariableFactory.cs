using System;
using System.Collections.Generic;

namespace StateScript
{
    /// <summary>
    /// Factory for creating state variables based on type discriminator
    /// </summary>
    public static class StateVariableFactory
    {
        private static readonly Dictionary<string, Func<StateVariable>> _typeMap = 
            new Dictionary<string, Func<StateVariable>>
            {
                { "Int", () => new StateVariableInt() },
                { "Float", () => new StateVariableFloat() },
                { "Bool", () => new StateVariableBool() },
                { "String", () => new StateVariableString() },
                { "Vector3", () => new StateVariableVector3() }
            };
        
        /// <summary>
        /// Create a state variable instance based on type name
        /// </summary>
        public static StateVariable CreateVariable(string typeName)
        {
            if (_typeMap.TryGetValue(typeName, out var factory))
            {
                return factory();
            }
            
            throw new ArgumentException($"Unknown variable type: {typeName}");
        }
        
        /// <summary>
        /// Register a custom variable type
        /// </summary>
        public static void RegisterVariableType<T>(string typeName) where T : StateVariable, new()
        {
            _typeMap[typeName] = () => new T();
        }
        
        /// <summary>
        /// Get all registered type names
        /// </summary>
        public static IEnumerable<string> GetRegisteredTypes()
        {
            return _typeMap.Keys;
        }
        
        /// <summary>
        /// Check if a type is registered
        /// </summary>
        public static bool IsTypeRegistered(string typeName)
        {
            return _typeMap.ContainsKey(typeName);
        }
    }
}

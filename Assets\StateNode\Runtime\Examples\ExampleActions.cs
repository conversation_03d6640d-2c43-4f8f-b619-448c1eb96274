using UnityEngine;
using StateScript;

namespace StateScript.Examples
{
    /// <summary>
    /// Example action that moves a transform
    /// </summary>
    public class MoveAction : StateAction
    {
        public Vector3 TargetPosition { get; set; }
        public AnimationCurve MoveCurve { get; set; } = AnimationCurve.EaseInOut(0, 0, 1, 1);
        
        private Vector3 _startPosition;
        private Transform _transform;
        private float _elapsedTime;
        
        public override string TypeName => "Move";
        
        public MoveAction() { }
        
        public MoveAction(Vector3 targetPosition, float duration, float delay = 0f)
        {
            TargetPosition = targetPosition;
            Duration = duration;
            Delay = delay;
        }
        
        public override void OnEnter(StateFlow stateFlow, IStateContext context)
        {
            Status = StateActionStatus.Running;
            _elapsedTime = 0f;
            
            // Get transform from context (assuming it's a MonoBehaviour)
            if (context is MonoBehaviour monoBehaviour)
            {
                _transform = monoBehaviour.transform;
                _startPosition = _transform.position;
            }
            else
            {
                Debug.LogWarning("MoveAction requires a MonoBehaviour context with a Transform");
                Status = StateActionStatus.Failed;
            }
        }
        
        public override void OnUpdate(StateFlow stateFlow, IStateContext context, float deltaTime)
        {
            if (_transform == null || Status != StateActionStatus.Running)
                return;
            
            _elapsedTime += deltaTime;
            
            if (Duration > 0f)
            {
                var progress = Mathf.Clamp01(_elapsedTime / Duration);
                var curveValue = MoveCurve.Evaluate(progress);
                _transform.position = Vector3.Lerp(_startPosition, TargetPosition, curveValue);
            }
            else
            {
                // Instant move
                _transform.position = TargetPosition;
            }
        }
        
        public override void OnExit(StateFlow stateFlow, IStateContext context)
        {
            // Ensure we reach the exact target position
            if (_transform != null)
            {
                _transform.position = TargetPosition;
            }
            Status = StateActionStatus.Completed;
        }
        
        public override void Serialize(ISerializer serializer)
        {
            base.Serialize(serializer);
            serializer.WriteVector3("targetPosition", TargetPosition);
            // Note: AnimationCurve serialization would need custom handling
        }
        
        public override void Deserialize(ISerializer serializer)
        {
            base.Deserialize(serializer);
            TargetPosition = serializer.ReadVector3("targetPosition");
        }
    }
    
    /// <summary>
    /// Example action that rotates a transform
    /// </summary>
    public class RotateAction : StateAction
    {
        public Vector3 TargetRotation { get; set; }
        public bool UseLocalRotation { get; set; } = true;
        
        private Vector3 _startRotation;
        private Transform _transform;
        private float _elapsedTime;
        
        public override string TypeName => "Rotate";
        
        public RotateAction() { }
        
        public RotateAction(Vector3 targetRotation, float duration, bool useLocal = true, float delay = 0f)
        {
            TargetRotation = targetRotation;
            Duration = duration;
            UseLocalRotation = useLocal;
            Delay = delay;
        }
        
        public override void OnEnter(StateFlow stateFlow, IStateContext context)
        {
            Status = StateActionStatus.Running;
            _elapsedTime = 0f;
            
            if (context is MonoBehaviour monoBehaviour)
            {
                _transform = monoBehaviour.transform;
                _startRotation = UseLocalRotation ? _transform.localEulerAngles : _transform.eulerAngles;
            }
            else
            {
                Debug.LogWarning("RotateAction requires a MonoBehaviour context with a Transform");
                Status = StateActionStatus.Failed;
            }
        }
        
        public override void OnUpdate(StateFlow stateFlow, IStateContext context, float deltaTime)
        {
            if (_transform == null || Status != StateActionStatus.Running)
                return;
            
            _elapsedTime += deltaTime;
            
            if (Duration > 0f)
            {
                var progress = Mathf.Clamp01(_elapsedTime / Duration);
                var currentRotation = Vector3.Lerp(_startRotation, TargetRotation, progress);
                
                if (UseLocalRotation)
                {
                    _transform.localEulerAngles = currentRotation;
                }
                else
                {
                    _transform.eulerAngles = currentRotation;
                }
            }
            else
            {
                // Instant rotation
                if (UseLocalRotation)
                {
                    _transform.localEulerAngles = TargetRotation;
                }
                else
                {
                    _transform.eulerAngles = TargetRotation;
                }
            }
        }
        
        public override void OnExit(StateFlow stateFlow, IStateContext context)
        {
            if (_transform != null)
            {
                if (UseLocalRotation)
                {
                    _transform.localEulerAngles = TargetRotation;
                }
                else
                {
                    _transform.eulerAngles = TargetRotation;
                }
            }
            Status = StateActionStatus.Completed;
        }
        
        public override void Serialize(ISerializer serializer)
        {
            base.Serialize(serializer);
            serializer.WriteVector3("targetRotation", TargetRotation);
            serializer.WriteBool("useLocalRotation", UseLocalRotation);
        }
        
        public override void Deserialize(ISerializer serializer)
        {
            base.Deserialize(serializer);
            TargetRotation = serializer.ReadVector3("targetRotation");
            UseLocalRotation = serializer.ReadBool("useLocalRotation");
        }
    }
    
    /// <summary>
    /// Example action that modifies a StateFlow variable
    /// </summary>
    public class SetVariableAction : StateAction
    {
        public string VariableName { get; set; } = "";
        public string VariableType { get; set; } = "Int";
        public string Value { get; set; } = "0";
        
        public override string TypeName => "SetVariable";
        
        public SetVariableAction() { }
        
        public SetVariableAction(string variableName, int value, float delay = 0f)
        {
            VariableName = variableName;
            VariableType = "Int";
            Value = value.ToString();
            Duration = 0f; // Instant
            Delay = delay;
        }
        
        public SetVariableAction(string variableName, float value, float delay = 0f)
        {
            VariableName = variableName;
            VariableType = "Float";
            Value = value.ToString();
            Duration = 0f; // Instant
            Delay = delay;
        }
        
        public SetVariableAction(string variableName, bool value, float delay = 0f)
        {
            VariableName = variableName;
            VariableType = "Bool";
            Value = value.ToString();
            Duration = 0f; // Instant
            Delay = delay;
        }
        
        public SetVariableAction(string variableName, string value, float delay = 0f)
        {
            VariableName = variableName;
            VariableType = "String";
            Value = value;
            Duration = 0f; // Instant
            Delay = delay;
        }
        
        public override void OnEnter(StateFlow stateFlow, IStateContext context)
        {
            Status = StateActionStatus.Running;
            
            try
            {
                switch (VariableType)
                {
                    case "Int":
                        if (int.TryParse(Value, out var intValue))
                        {
                            stateFlow.SetInt(VariableName, intValue);
                        }
                        break;
                    case "Float":
                        if (float.TryParse(Value, out var floatValue))
                        {
                            stateFlow.SetFloat(VariableName, floatValue);
                        }
                        break;
                    case "Bool":
                        if (bool.TryParse(Value, out var boolValue))
                        {
                            stateFlow.SetBool(VariableName, boolValue);
                        }
                        break;
                    case "String":
                        stateFlow.SetString(VariableName, Value);
                        break;
                }
                
                Debug.Log($"Set variable {VariableName} to {Value}");
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Failed to set variable {VariableName}: {e.Message}");
                Status = StateActionStatus.Failed;
            }
        }
        
        public override void OnUpdate(StateFlow stateFlow, IStateContext context, float deltaTime)
        {
            // Variable setting is instant
        }
        
        public override void OnExit(StateFlow stateFlow, IStateContext context)
        {
            if (Status == StateActionStatus.Running)
            {
                Status = StateActionStatus.Completed;
            }
        }
        
        public override void Serialize(ISerializer serializer)
        {
            base.Serialize(serializer);
            serializer.WriteString("variableName", VariableName);
            serializer.WriteString("variableType", VariableType);
            serializer.WriteString("value", Value);
        }
        
        public override void Deserialize(ISerializer serializer)
        {
            base.Deserialize(serializer);
            VariableName = serializer.ReadString("variableName");
            VariableType = serializer.ReadString("variableType");
            Value = serializer.ReadString("value");
        }
    }
}

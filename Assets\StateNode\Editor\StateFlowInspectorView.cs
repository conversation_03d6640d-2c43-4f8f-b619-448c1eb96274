using System;
using UnityEngine;
using UnityEditor;
using UnityEngine.UIElements;
using UnityEditor.UIElements;
using StateScript;

namespace StateScript.Editor
{
    /// <summary>
    /// Inspector view for selected nodes in the StateFlow editor
    /// </summary>
    public class StateFlowInspectorView : VisualElement
    {
        private StateNode _selectedNode;
        private VisualElement _contentContainer;
        private Label _titleLabel;
        
        public StateFlowInspectorView()
        {
            CreateUI();
        }
        
        private void CreateUI()
        {
            style.backgroundColor = new Color(0.25f, 0.25f, 0.25f, 1f);
            style.paddingTop = 5;
            style.paddingBottom = 5;
            style.paddingLeft = 5;
            style.paddingRight = 5;
            style.marginTop = 10;
            
            // Title
            _titleLabel = new Label("Inspector");
            _titleLabel.style.fontSize = 14;
            _titleLabel.style.unityFontStyleAndWeight = FontStyle.Bold;
            _titleLabel.style.marginBottom = 5;
            Add(_titleLabel);
            
            // Content container
            _contentContainer = new VisualElement();
            Add(_contentContainer);
            
            ShowNoSelection();
        }
        
        public void InspectNode(StateNode node)
        {
            _selectedNode = node;
            RefreshInspector();
        }
        
        private void RefreshInspector()
        {
            _contentContainer.Clear();
            
            if (_selectedNode == null)
            {
                ShowNoSelection();
                return;
            }
            
            _titleLabel.text = $"Inspector - {_selectedNode.GetType().Name}";
            
            // Basic properties
            CreateBasicProperties();
            
            // Node-specific properties
            CreateNodeSpecificProperties();
        }
        
        private void ShowNoSelection()
        {
            _titleLabel.text = "Inspector";
            _contentContainer.Clear();
            
            var noSelectionLabel = new Label("No node selected");
            noSelectionLabel.style.color = Color.gray;
            noSelectionLabel.style.unityTextAlign = TextAnchor.MiddleCenter;
            noSelectionLabel.style.marginTop = 20;
            _contentContainer.Add(noSelectionLabel);
        }
        
        private void CreateBasicProperties()
        {
            // Node ID
            var idField = new TextField("Node ID");
            idField.value = _selectedNode.Property.Id.ToString();
            idField.SetEnabled(false);
            _contentContainer.Add(idField);
            
            // Name
            var nameField = new TextField("Name");
            nameField.value = _selectedNode.EditorProperty.Name.ToString();
            nameField.RegisterValueChangedCallback(evt =>
            {
                _selectedNode.EditorProperty.Name = new Fixed32String(evt.newValue);
            });
            _contentContainer.Add(nameField);
            
            // Description
            var descriptionField = new TextField("Description");
            descriptionField.value = _selectedNode.EditorProperty.Description.ToString();
            descriptionField.multiline = true;
            descriptionField.style.height = 60;
            descriptionField.RegisterValueChangedCallback(evt =>
            {
                _selectedNode.EditorProperty.Description = new Fixed32String(evt.newValue);
            });
            _contentContainer.Add(descriptionField);
            
            // Color
            var colorField = new ColorField("Color");
            colorField.value = _selectedNode.EditorProperty.NodeColor;
            colorField.RegisterValueChangedCallback(evt =>
            {
                _selectedNode.EditorProperty.NodeColor = evt.newValue;
            });
            _contentContainer.Add(colorField);
            
            // Position
            var positionField = new Vector2Field("Position");
            positionField.value = _selectedNode.EditorProperty.Position;
            positionField.SetEnabled(false);
            _contentContainer.Add(positionField);
            
            // Status
            var statusField = new TextField("Status");
            statusField.value = _selectedNode.Status.ToString();
            statusField.SetEnabled(false);
            _contentContainer.Add(statusField);
            
            // Separator
            var separator = new VisualElement();
            separator.style.height = 1;
            separator.style.backgroundColor = Color.gray;
            separator.style.marginTop = 10;
            separator.style.marginBottom = 10;
            _contentContainer.Add(separator);
        }
        
        private void CreateNodeSpecificProperties()
        {
            switch (_selectedNode)
            {
                case StateActionNode actionNode:
                    CreateActionNodeProperties(actionNode);
                    break;
                case StateConditionNode conditionNode:
                    CreateConditionNodeProperties(conditionNode);
                    break;
                case StateListenerNode listenerNode:
                    CreateListenerNodeProperties(listenerNode);
                    break;
            }
        }
        
        private void CreateActionNodeProperties(StateActionNode actionNode)
        {
            var label = new Label("Actions");
            label.style.fontSize = 12;
            label.style.unityFontStyleAndWeight = FontStyle.Bold;
            label.style.marginBottom = 5;
            _contentContainer.Add(label);
            
            // Actions list
            for (int i = 0; i < actionNode.Actions.Count; i++)
            {
                var action = actionNode.Actions[i];
                var actionContainer = CreateActionElement(action, i, actionNode);
                _contentContainer.Add(actionContainer);
            }
            
            // Add action button
            var addButton = new Button(() => ShowAddActionMenu(actionNode));
            addButton.text = "Add Action";
            addButton.style.marginTop = 5;
            _contentContainer.Add(addButton);
        }
        
        private VisualElement CreateActionElement(StateAction action, int index, StateActionNode actionNode)
        {
            var container = new VisualElement();
            container.style.backgroundColor = new Color(0.3f, 0.3f, 0.3f, 1f);
            container.style.marginBottom = 3;
            container.style.paddingTop = 5;
            container.style.paddingBottom = 5;
            container.style.paddingLeft = 5;
            container.style.paddingRight = 5;
            container.style.borderTopLeftRadius = 3;
            container.style.borderTopRightRadius = 3;
            container.style.borderBottomLeftRadius = 3;
            container.style.borderBottomRightRadius = 3;
            
            // Header
            var header = new VisualElement();
            header.style.flexDirection = FlexDirection.Row;
            header.style.justifyContent = Justify.SpaceBetween;
            
            var typeLabel = new Label($"{action.GetType().Name}");
            typeLabel.style.fontSize = 11;
            typeLabel.style.unityFontStyleAndWeight = FontStyle.Bold;
            
            var deleteButton = new Button(() => {
                actionNode.Actions.RemoveAt(index);
                RefreshInspector();
            });
            deleteButton.text = "×";
            deleteButton.style.width = 20;
            deleteButton.style.height = 20;
            
            header.Add(typeLabel);
            header.Add(deleteButton);
            container.Add(header);
            
            // Duration
            var durationField = new FloatField("Duration");
            durationField.value = action.Duration;
            durationField.RegisterValueChangedCallback(evt => action.Duration = evt.newValue);
            container.Add(durationField);
            
            // Delay
            var delayField = new FloatField("Delay");
            delayField.value = action.Delay;
            delayField.RegisterValueChangedCallback(evt => action.Delay = evt.newValue);
            container.Add(delayField);
            
            // Action-specific properties
            if (action is LogAction logAction)
            {
                var messageField = new TextField("Message");
                messageField.value = logAction.Message;
                messageField.RegisterValueChangedCallback(evt => logAction.Message = evt.newValue);
                container.Add(messageField);
            }
            
            return container;
        }
        
        private void ShowAddActionMenu(StateActionNode actionNode)
        {
            var menu = new GenericMenu();
            
            menu.AddItem(new GUIContent("Wait Action"), false, () => {
                actionNode.Actions.Add(new WaitAction(1.0f));
                RefreshInspector();
            });
            
            menu.AddItem(new GUIContent("Log Action"), false, () => {
                actionNode.Actions.Add(new LogAction("Hello World"));
                RefreshInspector();
            });
            
            menu.ShowAsContext();
        }
        
        private void CreateConditionNodeProperties(StateConditionNode conditionNode)
        {
            var label = new Label("Condition");
            label.style.fontSize = 12;
            label.style.unityFontStyleAndWeight = FontStyle.Bold;
            label.style.marginBottom = 5;
            _contentContainer.Add(label);
            
            // Current condition
            var currentCondition = conditionNode.Condition?.GetType().Name ?? "None";
            var conditionField = new TextField("Current Condition");
            conditionField.value = currentCondition;
            conditionField.SetEnabled(false);
            _contentContainer.Add(conditionField);
            
            // Set condition buttons
            var alwaysTrueButton = new Button(() => {
                conditionNode.SetSimpleCondition(new AlwaysTrueCondition());
                RefreshInspector();
            });
            alwaysTrueButton.text = "Set Always True";
            _contentContainer.Add(alwaysTrueButton);
            
            var alwaysFalseButton = new Button(() => {
                conditionNode.SetSimpleCondition(new AlwaysFalseCondition());
                RefreshInspector();
            });
            alwaysFalseButton.text = "Set Always False";
            _contentContainer.Add(alwaysFalseButton);
        }
        
        private void CreateListenerNodeProperties(StateListenerNode listenerNode)
        {
            var label = new Label("Event Listener");
            label.style.fontSize = 12;
            label.style.unityFontStyleAndWeight = FontStyle.Bold;
            label.style.marginBottom = 5;
            _contentContainer.Add(label);
            
            // Event name
            var eventField = new TextField("Event Name");
            eventField.value = listenerNode.EventType.Name.ToString();
            eventField.RegisterValueChangedCallback(evt =>
            {
                listenerNode.EventType = new EventType(evt.newValue);
            });
            _contentContainer.Add(eventField);
        }
    }
}

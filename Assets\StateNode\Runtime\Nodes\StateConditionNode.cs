using System.Collections.Generic;
using System.Linq;

namespace StateScript
{
    /// <summary>
    /// Node that evaluates a condition and branches to different outputs
    /// Supports multiple output ports for complex branching
    /// </summary>
    public class StateConditionNode : StateNode
    {
        public StateCondition Condition { get; set; }
        public List<string> OutputPortNames { get; set; } = new List<string> { "True", "False" };
        
        // For multi-branch conditions (like switch statements)
        public List<StateCondition> Conditions { get; set; } = new List<StateCondition>();
        
        public StateConditionNode()
        {
            // Default to simple true/false condition
        }
        
        public override void OnEnter(StateFlow stateFlow, IStateContext context)
        {
            Status = StateNodeStatus.Running;
            
            // Evaluate condition immediately and complete
            EvaluateAndComplete(stateFlow, context);
        }
        
        public override void OnUpdate(StateFlow stateFlow, IStateContext context, float deltaTime)
        {
            // Condition nodes complete immediately after evaluation
            if (Status == StateNodeStatus.Running)
            {
                EvaluateAndComplete(stateFlow, context);
            }
        }
        
        public override void OnExit(StateFlow stateFlow, IStateContext context)
        {
            // Nothing to clean up
        }
        
        private void EvaluateAndComplete(StateFlow stateFlow, IStateContext context)
        {
            Status = StateNodeStatus.Completed;
            
            // For simple true/false condition
            if (Condition != null && Conditions.Count == 0)
            {
                bool result = Condition.Evaluate(stateFlow, context);
                var targetPortName = result ? "True" : "False";
                
                // Set a flag or store result for the StateFlow to use when determining next node
                // This is handled by the StateFlow's transition logic
            }
            // For multi-condition branching
            else if (Conditions.Count > 0)
            {
                for (int i = 0; i < Conditions.Count; i++)
                {
                    if (Conditions[i].Evaluate(stateFlow, context))
                    {
                        // Use the corresponding output port
                        break;
                    }
                }
            }
        }
        
        /// <summary>
        /// Get the output port that should be used based on condition evaluation
        /// </summary>
        public StatePort GetActiveOutputPort(StateFlow stateFlow, IStateContext context)
        {
            if (Condition != null && Conditions.Count == 0)
            {
                bool result = Condition.Evaluate(stateFlow, context);
                var targetPortName = result ? "True" : "False";
                return Property.GetOutputPort(targetPortName);
            }
            else if (Conditions.Count > 0)
            {
                for (int i = 0; i < Conditions.Count && i < OutputPortNames.Count; i++)
                {
                    if (Conditions[i].Evaluate(stateFlow, context))
                    {
                        return Property.GetOutputPort(OutputPortNames[i]);
                    }
                }
            }
            
            // Default to first output port if no condition matches
            return Property.GetOutputPorts().FirstOrDefault();
        }
        
        protected override void InitializePorts()
        {
            Property.Ports.Add(new StatePort("Input", PortType.Input, Property.Id));
            
            // Add output ports based on configuration
            foreach (var portName in OutputPortNames)
            {
                Property.Ports.Add(new StatePort(portName, PortType.Output, Property.Id));
            }
        }
        
        /// <summary>
        /// Configure this node for simple true/false branching
        /// </summary>
        public void SetSimpleCondition(StateCondition condition)
        {
            Condition = condition;
            Conditions.Clear();
            OutputPortNames = new List<string> { "True", "False" };
            
            // Rebuild ports
            Property.Ports.Clear();
            InitializePorts();
        }
        
        /// <summary>
        /// Configure this node for multi-branch conditions
        /// </summary>
        public void SetMultiConditions(List<StateCondition> conditions, List<string> portNames)
        {
            Condition = null;
            Conditions = conditions;
            OutputPortNames = portNames;
            
            // Rebuild ports
            Property.Ports.Clear();
            InitializePorts();
        }
        
        public override void Serialize(ISerializer serializer)
        {
            base.Serialize(serializer);
            
            // Serialize simple condition
            if (Condition != null)
            {
                serializer.WriteBool("hasSimpleCondition", true);
                serializer.WriteObject("condition", Condition);
            }
            else
            {
                serializer.WriteBool("hasSimpleCondition", false);
            }
            
            // Serialize multi-conditions
            serializer.WriteInt("conditionCount", Conditions.Count);
            for (int i = 0; i < Conditions.Count; i++)
            {
                serializer.WriteObject($"condition_{i}", Conditions[i]);
            }
            
            // Serialize output port names
            serializer.WriteInt("outputPortCount", OutputPortNames.Count);
            for (int i = 0; i < OutputPortNames.Count; i++)
            {
                serializer.WriteString($"outputPort_{i}", OutputPortNames[i]);
            }
        }
        
        public override void Deserialize(ISerializer serializer)
        {
            base.Deserialize(serializer);
            
            // Deserialize simple condition
            if (serializer.ReadBool("hasSimpleCondition"))
            {
                var conditionData = serializer.GetSubSerializer("condition");
                var conditionType = conditionData.ReadString("conditionType");
                Condition = StateConditionFactory.CreateCondition(conditionType);
                Condition.Deserialize(conditionData);
            }
            
            // Deserialize multi-conditions
            var conditionCount = serializer.ReadInt("conditionCount");
            Conditions.Clear();
            for (int i = 0; i < conditionCount; i++)
            {
                var conditionData = serializer.GetSubSerializer($"condition_{i}");
                var conditionType = conditionData.ReadString("conditionType");
                var condition = StateConditionFactory.CreateCondition(conditionType);
                condition.Deserialize(conditionData);
                Conditions.Add(condition);
            }
            
            // Deserialize output port names
            var outputPortCount = serializer.ReadInt("outputPortCount");
            OutputPortNames.Clear();
            for (int i = 0; i < outputPortCount; i++)
            {
                OutputPortNames.Add(serializer.ReadString($"outputPort_{i}"));
            }
        }
    }
}

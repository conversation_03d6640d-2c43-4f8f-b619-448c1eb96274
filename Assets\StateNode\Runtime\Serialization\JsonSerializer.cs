using System;
using System.Collections.Generic;
using UnityEngine;
using Newtonsoft.Json.Linq;

namespace StateScript
{
    /// <summary>
    /// JSON implementation of ISerializer using Newtonsoft.Json
    /// </summary>
    public class JsonSerializer : ISerializer
    {
        private JObject _jsonObject;
        
        public JsonSerializer()
        {
            _jsonObject = new JObject();
        }
        
        public JsonSerializer(JObject jsonObject)
        {
            _jsonObject = jsonObject ?? new JObject();
        }
        
        public JsonSerializer(string jsonString)
        {
            try
            {
                _jsonObject = JObject.Parse(jsonString);
            }
            catch
            {
                _jsonObject = new JObject();
            }
        }
        
        #region Write Operations
        
        public void WriteInt(string key, int value)
        {
            _jsonObject[key] = value;
        }
        
        public void WriteFloat(string key, float value)
        {
            _jsonObject[key] = value;
        }
        
        public void WriteString(string key, string value)
        {
            _jsonObject[key] = value ?? "";
        }
        
        public void WriteBool(string key, bool value)
        {
            _jsonObject[key] = value;
        }
        
        public void WriteVector2(string key, Vector2 value)
        {
            _jsonObject[key] = new JObject
            {
                ["x"] = value.x,
                ["y"] = value.y
            };
        }
        
        public void WriteVector3(string key, Vector3 value)
        {
            _jsonObject[key] = new JObject
            {
                ["x"] = value.x,
                ["y"] = value.y,
                ["z"] = value.z
            };
        }
        
        public void WriteColor(string key, Color value)
        {
            _jsonObject[key] = new JObject
            {
                ["r"] = value.r,
                ["g"] = value.g,
                ["b"] = value.b,
                ["a"] = value.a
            };
        }
        
        public void WriteObject(string key, ISerializable obj)
        {
            if (obj == null)
            {
                _jsonObject[key] = null;
                return;
            }
            
            var subSerializer = new JsonSerializer();
            obj.Serialize(subSerializer);
            _jsonObject[key] = subSerializer._jsonObject;
        }
        
        public void WriteArray<T>(string key, T[] array) where T : ISerializable
        {
            if (array == null)
            {
                _jsonObject[key] = null;
                return;
            }
            
            var jsonArray = new JArray();
            foreach (var item in array)
            {
                if (item == null)
                {
                    jsonArray.Add(null);
                }
                else
                {
                    var subSerializer = new JsonSerializer();
                    item.Serialize(subSerializer);
                    jsonArray.Add(subSerializer._jsonObject);
                }
            }
            _jsonObject[key] = jsonArray;
        }
        
        #endregion
        
        #region Read Operations
        
        public int ReadInt(string key, int defaultValue = 0)
        {
            return _jsonObject.TryGetValue(key, out var token) && token.Type != JTokenType.Null 
                ? token.Value<int>() 
                : defaultValue;
        }
        
        public float ReadFloat(string key, float defaultValue = 0f)
        {
            return _jsonObject.TryGetValue(key, out var token) && token.Type != JTokenType.Null 
                ? token.Value<float>() 
                : defaultValue;
        }
        
        public string ReadString(string key, string defaultValue = "")
        {
            return _jsonObject.TryGetValue(key, out var token) && token.Type != JTokenType.Null 
                ? token.Value<string>() 
                : defaultValue;
        }
        
        public bool ReadBool(string key, bool defaultValue = false)
        {
            return _jsonObject.TryGetValue(key, out var token) && token.Type != JTokenType.Null 
                ? token.Value<bool>() 
                : defaultValue;
        }
        
        public Vector2 ReadVector2(string key, Vector2 defaultValue = default)
        {
            if (_jsonObject.TryGetValue(key, out var token) && token is JObject vectorObj)
            {
                return new Vector2(
                    vectorObj.Value<float>("x"),
                    vectorObj.Value<float>("y")
                );
            }
            return defaultValue;
        }
        
        public Vector3 ReadVector3(string key, Vector3 defaultValue = default)
        {
            if (_jsonObject.TryGetValue(key, out var token) && token is JObject vectorObj)
            {
                return new Vector3(
                    vectorObj.Value<float>("x"),
                    vectorObj.Value<float>("y"),
                    vectorObj.Value<float>("z")
                );
            }
            return defaultValue;
        }
        
        public Color ReadColor(string key, Color defaultValue = default)
        {
            if (_jsonObject.TryGetValue(key, out var token) && token is JObject colorObj)
            {
                return new Color(
                    colorObj.Value<float>("r"),
                    colorObj.Value<float>("g"),
                    colorObj.Value<float>("b"),
                    colorObj.Value<float>("a")
                );
            }
            return defaultValue;
        }
        
        public T ReadObject<T>(string key) where T : ISerializable, new()
        {
            if (_jsonObject.TryGetValue(key, out var token) && token is JObject objToken)
            {
                var obj = new T();
                var subSerializer = new JsonSerializer(objToken);
                obj.Deserialize(subSerializer);
                return obj;
            }
            return default(T);
        }
        
        public T[] ReadArray<T>(string key) where T : ISerializable, new()
        {
            if (_jsonObject.TryGetValue(key, out var token) && token is JArray arrayToken)
            {
                var result = new T[arrayToken.Count];
                for (int i = 0; i < arrayToken.Count; i++)
                {
                    if (arrayToken[i] is JObject itemObj)
                    {
                        var item = new T();
                        var subSerializer = new JsonSerializer(itemObj);
                        item.Deserialize(subSerializer);
                        result[i] = item;
                    }
                }
                return result;
            }
            return new T[0];
        }
        
        #endregion
        
        #region Special Operations
        
        public ISerializer GetSubSerializer(string key)
        {
            if (_jsonObject.TryGetValue(key, out var token) && token is JObject subObj)
            {
                return new JsonSerializer(subObj);
            }
            return new JsonSerializer();
        }
        
        public bool HasKey(string key)
        {
            return _jsonObject.ContainsKey(key);
        }
        
        #endregion
        
        #region Utility Methods
        
        /// <summary>
        /// Get the JSON string representation
        /// </summary>
        public string ToJsonString(bool formatted = true)
        {
            return _jsonObject.ToString(formatted ? Newtonsoft.Json.Formatting.Indented : Newtonsoft.Json.Formatting.None);
        }
        
        /// <summary>
        /// Get the underlying JObject
        /// </summary>
        public JObject GetJObject()
        {
            return _jsonObject;
        }
        
        #endregion
    }
}

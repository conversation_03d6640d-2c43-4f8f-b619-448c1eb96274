using System;
using UnityEngine;

namespace StateScript
{
    /// <summary>
    /// Base class for all state variables
    /// </summary>
    public abstract class StateVariable : ISerializable
    {
        public Fixed32String Name { get; set; }
        
        /// <summary>
        /// Type discriminator for polymorphic deserialization
        /// </summary>
        public abstract string TypeName { get; }
        
        /// <summary>
        /// Runtime type of the variable value
        /// </summary>
        public abstract Type ValueType { get; }

        public StateVariable()
        {
        }

        public StateVariable(string name)
        {
            Name = new Fixed32String(name);
        }
        
        public virtual void Serialize(ISerializer serializer)
        {
            serializer.WriteString("name", Name.ToString());
            serializer.WriteString("type", TypeName);
        }
        
        public virtual void Deserialize(ISerializer serializer)
        {
            Name = new Fixed32String(serializer.ReadString("name"));
            // Type is handled during creation, not here
        }
    }
    
    /// <summary>
    /// Integer variable
    /// </summary>
    public class StateVariableInt : StateVariable
    {
        public int Value { get; set; }
        
        public override string TypeName => "Int";
        public override Type ValueType => typeof(int);

        public StateVariableInt()
        {
        }

        public StateVariableInt(int value)
        {
            Value = value;
        }

        public StateVariableInt(string name, int value) : base()
        {
            Name = new Fixed32String(name);
            Value = value;
        }
        
        public override void Serialize(ISerializer serializer)
        {
            base.Serialize(serializer);
            serializer.WriteInt("value", Value);
        }
        
        public override void Deserialize(ISerializer serializer)
        {
            base.Deserialize(serializer);
            Value = serializer.ReadInt("value");
        }
    }
    
    /// <summary>
    /// Float variable
    /// </summary>
    public class StateVariableFloat : StateVariable
    {
        public float Value { get; set; }
        
        public override string TypeName => "Float";
        public override Type ValueType => typeof(float);

        public StateVariableFloat()
        {
        }

        public StateVariableFloat(float value)
        {
            Value = value;
        }

        public StateVariableFloat(string name, float value) : base()
        {
            Name = new Fixed32String(name);
            Value = value;
        }
        
        public override void Serialize(ISerializer serializer)
        {
            base.Serialize(serializer);
            serializer.WriteFloat("value", Value);
        }
        
        public override void Deserialize(ISerializer serializer)
        {
            base.Deserialize(serializer);
            Value = serializer.ReadFloat("value");
        }
    }
    
    /// <summary>
    /// Boolean variable
    /// </summary>
    public class StateVariableBool : StateVariable
    {
        public bool Value { get; set; }
        
        public override string TypeName => "Bool";
        public override Type ValueType => typeof(bool);

        public StateVariableBool()
        {
        }

        public StateVariableBool(bool value)
        {
            Value = value;
        }

        public StateVariableBool(string name, bool value) : base()
        {
            Name = new Fixed32String(name);
            Value = value;
        }
        
        public override void Serialize(ISerializer serializer)
        {
            base.Serialize(serializer);
            serializer.WriteBool("value", Value);
        }
        
        public override void Deserialize(ISerializer serializer)
        {
            base.Deserialize(serializer);
            Value = serializer.ReadBool("value");
        }
    }
    
    /// <summary>
    /// String variable
    /// </summary>
    public class StateVariableString : StateVariable
    {
        public string Value { get; set; } = "";

        public override string TypeName => "String";
        public override Type ValueType => typeof(string);

        public StateVariableString()
        {
        }

        public StateVariableString(string value)
        {
            Value = value;
        }

        public StateVariableString(string name, string value) : base()
        {
            Name = new Fixed32String(name);
            Value = value;
        }

        public override void Serialize(ISerializer serializer)
        {
            base.Serialize(serializer);
            serializer.WriteString("value", Value);
        }

        public override void Deserialize(ISerializer serializer)
        {
            base.Deserialize(serializer);
            Value = serializer.ReadString("value");
        }
    }

    /// <summary>
    /// Vector3 variable
    /// </summary>
    public class StateVariableVector3 : StateVariable
    {
        public Vector3 Value { get; set; }

        public override string TypeName => "Vector3";
        public override Type ValueType => typeof(Vector3);

        public StateVariableVector3()
        {
            Value = Vector3.zero;
        }

        public StateVariableVector3(Vector3 value)
        {
            Value = value;
        }

        public StateVariableVector3(string name, Vector3 value) : base()
        {
            Name = new Fixed32String(name);
            Value = value;
        }

        public override void Serialize(ISerializer serializer)
        {
            base.Serialize(serializer);
            serializer.WriteVector3("value", Value);
        }

        public override void Deserialize(ISerializer serializer)
        {
            base.Deserialize(serializer);
            Value = serializer.ReadVector3("value");
        }
    }
}

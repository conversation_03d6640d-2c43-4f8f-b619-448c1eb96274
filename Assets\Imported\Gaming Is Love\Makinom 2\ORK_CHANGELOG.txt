-------------------------------------------------------------------------------------------------------------------------------------------------------
ORK Framework 3.20.1
-------------------------------------------------------------------------------------------------------------------------------------------------------

New:
- Difficulties: Variable Changes: 'Variable Changes' settings available. Change variables when using a difficulty. The variable changes are used when changing difficulty and starting a new game for a difficulty.

Changes:
- Battle End: Standalone Level Up Dialogue: The standalone level up dialogue now closes on game over or when exiting the game to the start menu.

Fixes:
- Shops: Fixed an issue where selling the last quantity of an item didn't remove the item from the shop, allowing to sell again.
- Combatants: Auto Start Battles: Fixed an issue where 'Start On Range' was missing settings when enabling it.
- Battle AIs: Fixed an issue where value selections using 'Status Value' or other combatant-related values could return 0 in some cases.
- Editor: Console Texts: Fixed an issue where the 'Console Type' setting wasn't displayed.
- Menu Screens: Description: Fixed an issue where 'All' types buttons (e.g. in 'Inventory' menu parts) where using the 'Content Description' instead of 'General Description'.


-------------------------------------------------------------------------------------------------------------------------------------------------------
ORK Framework 3.20.0 CUSTOMIZE OPTIONS
-------------------------------------------------------------------------------------------------------------------------------------------------------

New:
- Option Categories: 'Option Categories' sub-section available in 'UI > Option Categories'. Set up categories and their options (e.g. volume, graphics, input rebinding, etc.) and if the options of the category are saved in save games or an options file. The general settings define where the options file is saved and the default options menu.
- Global Custom Settings: 'Global Custom Settings' sub-section available in 'Base/Control' section. Global custom settings are an easy way to add custom systems to the project by extending from the 'BaseGlobalCustomSettingsType' class. Their data can be saved/loaded with save games and functionality called via schematics ('Use Global Custom Settings' node). See the built-in 'DebugGlobalCustomSettingsType' class for an example implementation.
- Formulas: Status Origin: 'Player Battle' and 'Player Field' status origin types available. You can now use specifically the battle or field player combatant in formula nodes. Previously you could only use the 'Player' (still available), i.e. based on the game being in battle or the field using one or the other. Please note that battle and field player are only different when using a non-battle player setup.
- Status Values: Change Schematics: 'Is Critical' and 'Is Blocked' settings available. Optionally consider using a change schematic based on the change being a critical hit or blocked. This information is also available as local bool variables 'critical' and 'blocked' in the schematic. 'Is Critical' defaults to 'Ignore' and 'Is Blocked' to 'No' to preserve the previous behaviour.
- Status Developments: Level Limit: 'Level Limit' settings available. Optionally limit the maximum level of a combatant until defined conditions are valid. A status development can have multiple level limits.
- Target Selections: Custom Targeting: 'Custom Targeting' settings available. Add custom targeting settings to target selection templates, abilities or items. Custom targeting can be used to sort or filter the available targets and set the used target of an action. This is supported by single and group target range and when using raycast targeting, but it's not used during player target selection (only when the system selects a target, e.g. auto targeting or by non-player combatants). Custom targeting is used after auto target conditions (i.e. only if auto target isn't used or didn't find a target). You can implement your own settings by extending from the 'BaseCustomTargetType' settings.
- Target Selections: Custom Targeting: Attack Modifier Value Sorted: 'Attack Modifier Value Sorted' custom target type available. The targets are sorted by a defined attack modifier's value. Single targeting uses the target with the lowest/highest value.
- Target Selections: Custom Targeting: Defence Modifier Value Sorted: 'Defence Modifier Value Sorted' custom target type available. The targets are sorted by a defined defence modifier's value. Single targeting uses the target with the lowest/highest value.
- Target Selections: Custom Targeting: Distance Sorted (Position): 'Distance Sorted (Position)' custom target type available. The targets are sorted by the distance to a defined position. Single targeting uses the nearest/farthest target.
- Target Selections: Custom Targeting: Distance Sorted (User): 'Distance Sorted (User)' custom target type available. The targets are sorted by the distance to the user. Single targeting uses the nearest/farthest target.
- Target Selections: Custom Targeting: Grid Distance Sorted (User): 'Grid Distance Sorted (User)' custom target type available. The targets are sorted by the grid distance to the user. Single targeting uses the nearest/farthest target.
- Target Selections: Custom Targeting: Status Value Sorted: 'Status Value Sorted' custom target type available. The targets are sorted by a defined status value. Single targeting uses the target with the lowest/highest value.
- Target Selections: Custom Targeting: Variable Sorted: 'Variable Sorted' custom target type available. The targets are sorted by a defined object variable value. Single targeting uses the target with the lowest/highest value.
- Battle Range Templates: Grid Shape: Line of Sight: 'Ignore First In Line' settings available when using 'LOS Allied Combatants' or 'LOS Enemy Combatants'. Optionally ignore the first combatant in a line for line of sight blocking, i.e. the first combatant in line can be targeted, but combatants behind it can't.
- Equipment, Classes: Custom Settings: 'Custom Settings' available. Add custom settings and functionality by extending from the 'BaseCustomSettingsType' class. Their data are saved/loaded with save games as part of their equipment/class and functionality called via schematics ('Use Custom Settings' node). See the built-in 'DebugCustomSettingsType' class for an example implementation.
- Abilities, Items, Status Effects, Combatants: Custom Settings: 'Custom Settings' available. Add custom settings and functionality by extending from the 'BaseCustomSettingsType' class. Their data are saved/loaded with save games as part of their parent content (e.g. ability or item) and functionality called via status changes or schematics ('Use Custom Settings' node). See the built-in 'DebugCustomSettingsType' class for an example implementation.
- Abilities, Items: Target Changes: 'Is Main Target' setting available. Optionally only use target changes if the target is a main target, i.e. not coming from affect range. Can check for being a main target, being an affected target or ignoring this condition. E.g. use this to set up different status changes for targets coming from affect range.
- Abilities, Items: Status Changes: Use Custom Settings: 'Use Custom Settings' status change type available. Uses custom settings of the ability/item, the user or target. Forwards the user, target, variables and selected data used for the calculation to the 'Use' function.
- Abilities, Items: Status Changes: Use Global Custom Settings: 'Use Global Custom Settings' status change type available. Uses defined global custom settings. Forwards the user, target, variables and selected data used for the calculation to the 'Use' function.
- Status Effect Changes: 'Add Count' setting available when adding a random status effect type. Defines the number of times a random status effect of the selected status effect type will be added.
- Status Effect Changes: 'Remove Count' setting available when removing a status effect type (random, first or last). Defines the number of times a status effect of the selected status effect type will be removed.
- Status Effects: Stack Settings: 'Reapply On Remove' setting available for 'Single Instance' stacks. Optionally cause reapply on the remaining stack if the stack count is reduced. E.g. use this to update status changes based on the stack count.
- Status Effects: Status Changes: Status Value: 'Reapply Difference' setting available. Optionally only change the status value using the difference between the already performed change and the new change value when reapplying.
- Status Effects: Status Changes: Use Custom Settings: 'Use Custom Settings' status change type available. Uses custom settings of the status effect, the user or target. Forwards the user, target, variables and selected data of the status effect to the 'Use' function.
- Status Effects: Status Changes: Use Global Custom Settings: 'Use Global Custom Settings' status change type available. Uses defined global custom settings. Forwards the user, target, variables and selected data of the status effect to the 'Use' function.
- Status Effect Types: Wrap Status Effect Name: 'Wrap Status Effect Name' settings available. Optionally wrap the name of status effects of a status effect type with text. E.g. use this to add a text color to all names of status effects of an effect type.
- Status Effect Types: Wrap Status Effect Short Name: 'Wrap Status Effect Short Name' settings available. Optionally wrap the short name of status effects of a status effect type with text. E.g. use this to add a text color to all names of status effects of an effect type.
- Battle Texts, Status Effects, Status Effect Types: 'Immune Flying Text' settings available. Optionally show a flying text when a status effect fails to be applied due to immunity.
- Battle AIs: Variable Sort Targets: 'Variable Sort Targets' node available in 'Target' nodes. Sort the found targets based on a defined variable's value. Uses object variables on the combatants for sorting.
- Battle AIs: Selected Data Contains: 'Selected Data Contains' node available in 'Selected Data' nodes. Checks if data stored in a selected data list contains something stored in another selected data list.
- Console Settings, Status Effects, Status Effect Types: 'Immune Effect Text' settings available. Optionally show a console text when a status effect fails to be applied due to immunity.
- Combatant Groups: Combatants: 'Chance' setting available. Defines the chance that the combatant is used by the group.
- Combatant Groups: Combatants: 'Use Chance Selection' setting available when using 'Use Random Combatant'. Uses the 'Chance' setting of the combatant to determine which combatant will be used. E.g. combatant 0 has 35% chance, combatant 1 has 50% chance, combatant 2 has 15% chance. i.e. combatant 0 is used for 0-35 chance range, combatant 1 for 35-85 and combatant 2 for 85-100.
- Combatant Groups: Group Battle Gains: 'Loot' settings available. Optionally use loot to add battle gains to the group.
- Loot: 'Use Loot Table' setting available. Define if 'All', 'First' or 'Random' loot tables are used. This replaces the 'Use First Found Table' setting, previous settings will be updated automatically.
- Loot: Loot Tables: 'Draw Type' and 'Draw Count' setting available. Define the number of times items from the loot table are used ('Draw Count'), getting either 'All' items, 'Random' items or 'Pool' items (random item, can't get the same multiple times). 'Draw Type' replaces the 'Get Random' setting, previous settings will be updated automatically.
- Loot: Loot Tables: Conditions: 'Check Looter' setting available when using 'Combatant' local origin. Optionally use the looter's variables and selected data in the checks instead of the combatant dropping the loot.
- Loot: Items: 'Use Replacement' settings available. Optionally replace an item in a loot table with an item found in another loot table. Can use 'Auto' replacement if the player already has the item in the inventory or check for defined conditions.
- Research Trees: Research Items: Status Effect: 'Status Effect' research item type available. Changes a status effect on the user when researched.
- Research Trees: Research Items: Status Change: 'Status Change' research item type available. Performs defined status changes on the user when researched.
- HUDs: Tooltip: Tooltip Checks: 'Shortcut Item Type' tooltip type available. Checks if a tooltip is from a shortcut (item, equipment, currency, etc.) of a defined item type.
- Start Menu: Options (Category): 'Options (Category)' option type available. Calls an options category menu, either the default options menu or a custom setup.
- Menu Screens: Options (Category): 'Options (Category)' menu part available. Displays an options menu for option categories defined in 'UI > Option Categories'. Can either use the default options menu or a custom setup.
- Menu Screens: Combatant Settings: 'Other Player Group' settings available. Optionally use a different player group than the active player group as menu users.
- Menu Screens: Button List: Use Global Custom Settings: 'Use Global Custom Settings' type available. Uses defined global custom settings. Forwards the menu screen's user as user and target to the 'Use' function.
- Menu Screens: Button List: Options Menu: 'Options Menu' type available. Calls an options category menu, either the default options menu or a custom setup.
- Menu Screens, Shop Layouts: Combatant: 'Filter Settings' available in 'Combatant' menu part (menu screens) and 'User Info Box' (shop layouts) settings. Optionally filter a list of displayed combatants by defined conditions, e.g. combatant status or variable conditions.
- Battle Menus: Options: Status Changes: Use Global Custom Settings: 'Use Global Custom Settings' status change type available. Uses defined global custom settings. Forwards the combatant as user and target to the 'Use' function (no variables or selected data are forwarded).
- Schematics: Use Custom Settings: 'Use Custom Settings' node available in 'Value > Selected Data' nodes. Calls the custom setting's 'Use' function of content stored in selected data. Forwards the schematic's local variables and selected data to the 'Use' function.
- Schematics: Use Global Custom Settings: 'Use Global Custom Settings' node available in 'Game > Game' nodes. Calls a defined global custom setting's 'Use' function. Forwards the schematic's local variables and selected data to the 'Use' function.
- Schematics: Call Options Menu: 'Call Options Menu' node available in 'UI > Menu' nodes. Opens an options category menu, either the default options menu or a custom setup.
- Schematics: Level Down: 'Level Down' node available in 'Combatant > Status' nodes. Decreases the level or class level of a combatant or it's group.
- Schematics: Can Selected Data Upgrade Level: 'Can Selected Data Upgrade Level' node available in 'Value > Selected Data' nodes. Checks if a combatant can upgrade the level of something stored in selected data. E.g. Checks if the cost for an ability or equipment level upgrade are valid. Please note that this only uses the level upgrade system, not the experience level up system.
- Schematics: Set Combatant Name: 'Reset Name Count' setting available. Optionally reset the combatant's name count coming from the 'Enemy Count' setting of the battle system.
- Schematics: Set Player Group: 'Set Player Group' node available in 'Group > Player Group' nodes. Uses a combatant's group as a player group of a defined group ID. The previous group of the group ID will be removed and replaced with the new group. When changing the active group, the old group will be removed from the scene and the new group will be spawned at the position of the old player.
- Unity UI: ORK HUD Status Text Content Component: 'Inventory Container' type available. Displays a combatant's inventory container information.
- Unity UI: ORK HUD Status Text Content Component: Combatant Information: New text codes available to display the combatant's index in it's group and battle group.
- Unity UI: ORK HUD Value Bar Content Component: 'Inventory Container' type available. Displays a combatant's inventory container occupied slots.
- Unity UI: HUD Combatant List Content Component: 'HUD Combatant List' content component available. Lists combatants of a group, e.g. the active player group, a defined player group ID or of the HUD user's group.
- Unity UI: HUD Click Component: Menu Screen: 'Back To Current Menu' settings available. Optionally return to the currently open menu screen when the called menu screen is closed.
- Unity UI: HUD Player Combatant Content Provider Component: 'Player Type' setting available when not using a member. Define if the current, battle or field player is used. Defaults to current player (previous behavoiur).
- Unity UI: HUD Condition: Status Preview Equipable: 'Status Preview Equipable' condition type available. Checks if the HUD user is a combatant with a status preview for an equipment (requires a combatant).
- Battle Grid Components: 'Keep Game Objects' setting available when using 'Keep Old Cells' during grid generation. If enabled, the old cell game objects will be reused instead of creating new game objects and copying the old settings. E.g. use this option if you added other components to your grid cells. The cells will still be placed at new positions.

Changes:
- Status Values: Change Schematics: Change schematics are now also performed by blocked status value changes (status value change blocks, e.g. status effects).
- Loot: 'Use Loot Table' setting replaces 'Use First Found Table' setting.
- Loot: 'Draw Type' setting replaces 'Get Random' setting.
- Target Selections: Raycast targeting now supports using the auto target conditions.
- Battles: Groups only select a new battle leader if the leader's death settings destroy the prefab. If the prefab is kept (even if e.g. destroyed by the death schematic), no new battle leader will be searched.
- Battle Texts, Status Effects, Status Effect Types: The 'Miss Flying Text' no longer shows for status effects not being applied due to immunity, only for missing the hit chance. Use the 'Immune Flying Text' instead.
- Menu Screens: All player groups are now considered as player group, i.e. using 'Allow Non-Player' setting isn't needed when displaying for not active player groups.
- Menu Screens: Group: Drag and drop in 'Change' and 'Change Reserve' actions now supports removing a battle group/reserve member when not dropping on another combatant.
- Console Settings, Status Effects, Status Effect Types: The 'Miss Effect Text' no longer shows for status effects not being applied due to immunity, only for missing the hit chance. Use the 'Immune Effect Text' instead.
- Game Controls: Player Controls: Button: 'Vertical Axis' and 'Horizontal Axis' settings have been replaced by new input settings. The new settings allow either using a single input key as axis or 2 input keys for positive and negative axis input separately.
- Game Controls: Player Controls: Top Down 2D: 'Vertical Axis' and 'Horizontal Axis' settings have been replaced by new input settings. The new settings allow either using a single input key as axis or 2 input keys for positive and negative axis input separately.
- Game Controls: Camera Controls: Top Down Border: 'Horizontal Panning Key' and 'Vertical Panning Key' settings have been replaced by new input settings. The new settings allow either using a single input key as axis or 2 input keys for positive and negative axis input separately.
- Game Controls: Camera Controls: First Person: 'Vertical Axis' and 'Horizontal Axis' settings have been replaced by new input settings. The new settings allow either using a single input key as axis or 2 input keys for positive and negative axis input separately.
- Raycast Targeting: Position: Input Key Settings: 'Vertical Axis' and 'Horizontal Axis' settings have been replaced by new input settings. The new settings allow either using a single input key as axis or 2 input keys for positive and negative axis input separately.
- Battle Grid Settings: Grid Cell Selection: 'Vertical Axis' and 'Horizontal Axis' settings have been replaced by new input settings. The new settings allow either using a single input key as axis or 2 input keys for positive and negative axis input separately.

Fixes:
- Shops: Fixed an issue where equipment didn't show status previews when selected.


-------------------------------------------------------------------------------------------------------------------------------------------------------
ORK Framework 3.19.1
-------------------------------------------------------------------------------------------------------------------------------------------------------

Changes:
- Status Effects: Auto effects no longer use durations or cause reapplying the effect. Applying the same status effect from a non-auto effect source will cause reapply, but doesn't add a duration.
- Menu Screens: Inventory Container (Slots): Drag and drop now supports swapping from one inventory to another (e.g. when using two menu screens to display separate inventory's inventory containers).
- Move AIs: Blocking the move AI (global or for a specific combatant) during waypoint movement will resume movement to the waypoint after the move AI is unblocked.

Fixes:
- Status Effects: Fixed an issue where reapplying status effects without a duration didn't use the status changes for reapply.
- Random Status Bonuses: Fixed an issue where defence modifier bonuses could cause an error.
- Menu Screens: Research (List View), Research (Tree View): Fixed an issue where using a 'Start Question Dialogue' or 'Cancel Question Dialogue' could cause an error when accepting or declining the question.
- Menu Screens: Group: Fixed an issue where drag and drop didn't work for anything beside changing battle group and group transfers.
- Battle Grid Settings: Combatant Placement: Fixed an issue where canceling placing a combatant kept the spawned game object in the scene.
- Battle Grid Settings: Move Command: Fixed an issue where using 'Mark Target Cell' could prevent a combatant from being set as the cell's on the final cell of a path.
- Move AIs: Fixed a potential issue where auto respawning could cause a stack overflow.
- Move AIs: Fixed an issue where blocking the move AI globally didn't stop moving to the current waypoint.
- Shops: Fixed an issue where overriding an item's price with a price of 0 caused an error when buying in the shop.
- Shop Layouts: Sell Shopping Cart: Fixed an issue where already added items where not recognized as already added, adding them multiple times instead of removing or capping their quantity.
- Shop Layouts: Sell Shopping Cart: Fixed an issue where selling equipped equipment didn't unequip it when sold using a shopping cart.
- Schematics: Clear Inventory: Fixed an issue where inventory containers where not cleared.
- Unity UI: HUD Shortcut Slot Content Component: Fixed an issue where changing combatants while displaying shortcut slots in a HUD could lead to the previous combatant's state change still being displayed (e.g. showing a slot as inactive instead of active).


-------------------------------------------------------------------------------------------------------------------------------------------------------
ORK Framework 3.19.0 EFFECTIVE VISUALS
-------------------------------------------------------------------------------------------------------------------------------------------------------

New:
- Prices: Advanced Prices: 'Use Advanced Prices' settings available in all price settings. Optionally use advanced prices, allowing to define multiple price costs (status values, items, currencies, etc.). Additionally, allowing to buy or sell can depend on defined conditions (e.g. variable or status conditions).
- Abilities, Equipment: Levels: 'Upgrade Settings' available. Optionally allow upgrading to a level using status value and item costs. This system is used independent of the 'Level Up Type', i.e. upgrades can be used without reaching the level points needed for a level. Upgrades can be used using menu screens and schematics.
- Abilities, Equipment: Status Bonuses: 'Keep Bonus' setting available. Optionally keep the status bonuses of a level in higher levels. E.g. use level 1 bonuses in level 2, 3, etc.
- Equipment: Equipment Abilities: Learn Ability: 'Upgrade Settings' available. Optionally allow learning the equipment ability using status value and item costs. Upgrades can be used using menu screens and schematics.
- Abilities, Items: User Settings: Calculation Schematics: 'Calculation Schematics' available for users of abilities/items. Use a schematic for the user before and after calculating the outcome. The user will be used as 'Machine Object', the target(s) as 'Starting Object'. These schematics share the local variables and selected data with the battle animation schematics.
- Abilities, Items: Target Settings: Calculation Schematics: 'Calculation Schematics' available for targets of abilities/items. Use a schematic for each individual target before and after calculating the outcome. The individual target will be used as 'Machine Object', the user as 'Starting Object'. These schematics share the local variables and selected data with the battle animation schematics.
- Item Types: 'Inventory Schematics' settings available. Optionally override the inventory schematics for content of the item type.
- Status Bonuses: Status Effect Replacements: 'Status Effect Replacements' settings available. Automatically replace a status effect when it's applied to a combatant with a different status effect. Please note that this only happens when applying a status effect and doesn't affect already applied status effects of a combatant. E.g. an ability applies a 'Poison' effect to the target, the target's status bonuses turns it into a 'Regeneration' effect, i.e. the target will receive 'Regeneration' instead of 'Poison'.
- Status Conditions: Ability, Ability Type: 'Ignore Availability' setting available. Optionally ignore ability availability settings and use all abilities added to the combatant.
- Status Conditions: Group Size: 'Include Hidden' setting available. Optionally exclude hidden combatants from the group size check.
- Status Effects: Stack Settings: 'Single Instance' stack type available. The effect is stackable, but only uses a single instance (i.e. status changes are only used once for the stack).
- Status Effects: End Settings: 'Death' end after type available. The duration decreases when the target died (but before adding a death action). Different to the 'End On Death' setting, this allows using duration with death, e.g. keeping the effect for multiple deaths.
- Status Effects: Status Changes: Use On Settings: 'Death' use on type available. Uses the status change when the target died (but before adding a death action). E.g. can be used to stop death by using a schematic that increases health ('Change Status Value' node) and revives ('Revive' node).
- Status Effects: Status Changes: Block Status Effect: 'Block Status Effect' change type available. Blocks changes of a defined status effect, either blocking adding, removing or both. Different to auto status effects (status bonuses), this doesn't affect already added status effects.
- Status Effects: Status Changes: Block Status Effect Type: 'Block Status Effect Type' change type available. Blocks changes of a defined status effect type, either blocking adding, removing or both. Different to auto status effects (status bonuses), this doesn't affect already added status effects.
- Status Effects: Auto Replacement: 'Auto Replacement' settings available. Similar to auto apply/remove, this automatically replace a status effect when it's applied to a combatant with a different status effect when defined status conditions are valid. Please note that this only happens when applying a status effect and doesn't affect already applied status effects of a combatant. Different than 'Status Effect Replacmenets' of status bonuses, this auto replacement is always active for all combatants matching the conditions (like auto apply/remove).
- Combatants: Attacks & Abilities: Ability Availability: 'Ability Availability settings available in 'Combatants > General Settings' and individual combatant's settings. Optionally limit the abilities available to the combatant. This doesn't affect the abilities the combatant has or can learn, only if they're listed in menus, available for use (active abilities) or contribute to the combatant's status (passive abilities). E.g. only have abilities assigned to the combatant's shortcut slots available.  This works for active and passive abilities (base/counter attacks are not affected).
- Combatants: Prefab Settings: 'Use Short Name' setting available when using 'Set Object Name'. Optionally use the combatant's short name when setting the spawned game object's name.
- Combatants: Schematics: Battle Start Schematics: 'Battle Start Schematics' settings available in 'Combatants > General Settings' and individual combatant's settings. Optionally execute a schematic when a combatant's battle starts, either at the start of a battle or when joining a running battle.
- Combatants: Schematics: Battle End Schematics: 'Battle End Schematics' settings available in 'Combatants > General Settings' and individual combatant's settings. Optionally execute a schematic when a combatant's battle ends, either at the end of a battle or when leaving a running battle.
- Combatants: Schematics: Status Reset Schematics: 'Status Reset Schematics' settings available in 'Combatants > General Settings' and individual combatant's settings. Optionally execute a schematic when a combatant's status is recalculated. Recalculation happens when e.g. equipment or status effects change or 'Normal' type status values change.
- General Conditions: Area: 'Area' general condition type available. Checks the current area.
- General Conditions: Area Type: 'Area Type' general condition type available. Checks the current area type.
- Game Settings: Player/Group Settings: 'Count Excludes Hidden' setting available. Optionally exclude hidden combatants from being counted for the total group size. E.g. this impacts the (optional) group size limit, group size conditions or experience splitting by group size.
- Shops: Override Price: 'Use Advanced Price' setting available when overriding prices for items. Optionally use an advanced buy price, allowing to define multiple price costs (status values, items, currencies, etc.).
- Shops: Sell To Shop: 'Sell Equipped Equipment' settings available. Optionally allow selling equipment currently equipped by the shop's user or group to the shop. When sold, the equipment will be unequipped. Equipment that can't be unequipped can't be sold.
- Shop Layouts: Buy Shopping Cart, Sell Shopping Cart: 'Buy Shopping Cart' and 'Sell Shopping Cart' settings available. Optionally use a shopping cart instead of buying/selling items immediately. After accepting an item (including quantity selections and confirmation dialogues), the item is added to a shopping cart and only purchased or sold after confirming all items in the cart. The shopping cart can display the combined costs of all items in the cart (text codes and HUDs via 'HUD Cost List' content).
- Shop Layouts: Buy Quantity Details, Sell Quantity Details: 'Buy Quantity Details' and 'Sell Quantity Details' settings available. Optionally use a quantity selection to display details of a selected item when buying from the shop/selling to the shop. The quantity selection will be opened when selecting (not accepting) an item. Accepting the item will focus on the quantity selection, allowing to select the quantity and buy/sell the item.
- Shop Layouts: Buy/Sell Box: 'Keep Open' and 'Show On Selection' settings available. Optionally keep the buy/sell box open when showing type/list boxes. Additionally, the type/list boxes can be displayed when selecting buy or sell buttons, instead of accepting them (i.e. similar to 'Multi' display mode in menu screens).
- Shop Layouts, Menu Screens: Quantity Questions: 'HUD Type' settings available for 'Message Content' settings. Optionally use a HUD template or shortcut UI for the quantity question dialogue's content.
- Menu Screens: Ability Upgrade: 'Ability Upgrade' menu part available. Displays upgradeable abilities (i.e. using upgrade settings for level ups or equipment abilities that can be learned via upgrades).
- Menu Screens: Equipment Upgrade: 'Equipment Upgrade' menu part available. Displays upgradeable equipment (i.e. using upgrade settings for level ups).
- Menu Screens: Comparison: 'Comparison' menu part available. Displays a comparison between current and selected content, mainly for slots and content that will be assigned/equipped to them. E.g. the currently equipped equipment (or empty equipment slot) and selected equipment of an 'Equipment' menu part.
- Menu Screens: Ability, Single Slot, Multi Slot, Multi Content: 'Ignore Availability' setting available in when displaying abilities. Optionally ignore ability availability settings and use all abilities added to the combatant.
- Menu Screens: Inventory: Item Box: 'Add Unadded' setting available. Optionally add content (items, equipment, etc.) that isn't in the inventory with a quantity of 0 to the list. I.e. the inventory menu will list all possible items, equipment, etc. for the current item type, listing unowned items with a quantity of 0.
- Menu Screens: Equipment, Equipment (Single Slot): 'HUD Type' settings available for 'Message Content' settings. Optionally use a HUD template or shortcut UI for the equipment box's content. The selected equipment slot is used as HUD content.
- Menu Screens: Equipment, Equipment (Single Slot): 'Add Equipped Group' setting available when using 'Add Equipped'. Optionally add the equipment currently equipped by group members (on the same equipment slot) to the equipment list. Equipping an equipment equipped by other group members will unequip it from them. If unequipping isn't possible, the equipment can't be equipped.
- Menu Screens: Research (List View), Research (Tree View): Details Box: 'Hide For Completed' setting available when using 'Show All Buttons'. The start and cancel research buttons will not be displayed for completed research items.
- Menu Screens: Research (List View), Research (Tree View): Details Box: 'Show Start Research Button' and 'Show Cancel Research Button' settings available. Adding the start and cancel research buttons is now optional. By default enabled (previous behaviour).
- Menu Screens: Combatant: 'Select Menu User' setting available when not using 'Current' combatant scope. Optionally change the menu screen's user when accepting/clicking on a listed combatant.
- Menu Screens: Combatant: 'Use Screen Sorting' setting available when not using 'Current' combatant scope. Optionally sort the listed combatants by the current sorting of the menu screen.
- Menu Screens: Menu List: 'Forward User Change' setting available. Optionally forward the new user to the currently open menu screen if the menu list menu screen's user changes.
- Menu Screens: Sub Menus, Menu Actions: 'Level Upgrade' menu action available. Uses the upgrade settings of an ability/equipment level or learns an equipment ability using it's upgrade settings.
- Quantity Selections: Quantity Changes: 'Hide For Max Quantity 1' setting available. Optionally hide the quantity change input if only a maximum quantity of 1 can be selected.
- Quantity Selections: 'Block Focus' setting available. Blocking focus changes is now optional for quantity selections.
- Crafting Notifications: Crafting List Failed: 'Crafting List Failed' notification available. Optionally display a notification if using the crafting list ('Crafting List' menu screen part) didn't create anything.
- Content Sorters: Level: 'Level' sort type available. Sorts content by level (e.g. equipment, abilities or combatants).
- Battle Menus: Ability: 'Ignore Availability' setting available in 'Ability' option types. Optionally ignore ability availability settings and use all abilities added to the combatant.
- Battle Menus, Target Settings: Target Menu: 'Multi-Target Rotation' setting available when using 'Rotate To Target'. Define which target to rotate to when more than 1 target is selected, e.g. to the 'Center' of all targets, the 'Nearest' target, etc.. Defaults to 'Center' (previous behaviour).
- Battle Menus, Target Settings: Target Menu: 'Block Diagonal Rotation' setting available when using 'Rotate To Target' and 'Grid Rotation'. Optionally block using diagonal directions during grid battles with square grids.
- Battle Texts: Text Settings: 'Other Content' text settings available. Define content information for action cost and use counts. This can be used in cost displays.
- Battle Grid Settings: Cell Occupation Settings: 'Cell Occupation Settings' available. Optionally allow a cell to be occupied by multiple combatants. Allowing more than one combatant requires to define a limit for player/ally and enemy combatants on a cell. Grid cell types and individual grid cells in the scene can override this.
- Battle Grid Settings: Move Command: Waypoint Settings: 'Waypoint Settings' available. Optionally allow setting waypoints during grid move selection, plotting a path over one or multiple waypoints.
- Battle Grid Settings: Examine Grid: Combatant Information: 'Add To HUD' setting available. Optionally add the combatants occupying an examined grid cell to an 'Information' type HUD.
- Battle Grid Highlights: Move Command: Waypoint Cell: 'Waypoint Cell' highlight settings available. Used to highlight a waypoint during grid move selection.
- Battle Grid Highlights: Move Command: Waypoint Path: 'Waypoint Path' highlight settings available. Used to highlight a path to a waypoint during grid move selection.
- Battle Grid Cell Types: Cell Occupation Settings: 'Cell Occupation Settings' available. Optionally override the default cell occupation settings with a custom setup for a cell type. Individual grid cells in the scene can override this.
- Text Display Settings: Price Texts: 'Advanced Buy Price Text' and 'Advanced Sell Price Text' settings available. Define how advanced prices will be displayed.
- Text Display Settings: Use Cost Display: Use Count Text, Action Cost Text: New text codes available to add content information for use count and action cost defined in 'Battles > Battle Texts'.
- Formulas: Selected Data Level: 'Use Max Level' setting available. Optionally use the maximum level instead of the current level.
- Formulas, Battle AIs: Check Status: 'Combatant Scope' and 'Combatants Needed' settings available. Optionally check all members of the combatant's group isntead of just the combatant itself.
- Formulas, Battle AIs, Schematics: Check Selected Data Level: 'Check Selected Data Level' node available. Checks the level of something stored in selected data.
- Formulas, Battle AIs, Schematics: Init Selected Data Variables: 'Init Selected Data Variables' node available. Initializes variables of content stored in selected data. This can be used to initialize the variables of abilities, items or equipment again, using a defined combatant as user.
- Formulas, Battle AIs, Schematics: Select Combatant: 'Attacked' combatant origin available. Uses combatants the combatant attacked as selected data.
- Formulas, Battle AIs, Schematics: Select Ability: 'Ignore Availability' setting available. Optionally ignore ability availability settings and use all abilities added to the combatant.
- Schematics: Actors: Player Group: 'Other Player Group' settings available. Optionally use a different player group of a defined group ID instead of the current active player group.
- Schematics: Change Equip Ability: 'Change Equip Ability' node available in 'Inventory > Equipment' nodes. Adds or removes an ability to/from an equipment currently stored in selected data. The abilities added to an equipment are available to a combatant while having the equipment equipped. These abilities are different than 'Equipment Abilities' set up in an equipment, i.e. they can't be learned by the wearer, but they can be leveled up.
- Schematics: Check Equip Ability: 'Check Equip Ability' node available in 'Inventory > Equipment' nodes. Checks if an ability has been added to an equipment currently stored in selected data. This only checks abilities added via schematics, not 'Equipment Abilities' set up in an equipment.
- Schematics: Selected Data Question Dialogue: 'Selected Data Question Dialogue' node available in 'UI > Dialogue' nodes. Displays a question dialogue with selected data information.
- Schematics: Stop Target Selection: Ends a combatant's active target selection (including any cell selections). Please note that this can lead to returning to the previous battle menu selection, depending on how the target selection was started.
- Schematics: Select Grid Cell Position: 'Select Grid Cell Position' node available in 'Battle > Grid Cell' nodes. Stores the cell position game object for a combatant into selected data. Cell positions are defined on a grid cell using a 'Battle Grid Cell Positions' component on it's prefab. If no cell positions are found on a cell, or the combatant exceeds the defined positions, the cell's game object is used.
- Schematics: Change Active Group: 'Spawn New Group' and 'Destroy Old Group' settings available. Spawning the new active player group's combatants and destroying the old player group's combatants is now optional. By default enabled (previous behaviour).
- Schematics: Select Item Box: 'Select Item Box' node available in 'Value > Selected Data' nodes. Uses items from an item box as selected data.
- Schematics: Save Item Box: 'Encrypt Data' setting available. Optionally encrypt the XML-formatted text using the save game encryption.
- Schematics: Load Item Box: 'Decrypt Data' setting available. Optionally decrypt the XML-formatted text using the save game encryption.
- Schematics: Create Combatant: 'Create Combatant' node available in 'Combatant > Combatant' nodes. Creates a new combatant instance (with it's own group) and stores it in selected data. The combatant isn't spawned.
- Schematics: Register Combatant: 'Register Combatant' node available in 'Combatant > Combatant' nodes. Registers a combatant with ORK's system. Registered combatants will receive time updates and can be found by other systems (e.g. for target selection). Spawned combatants and the player group members are automatically registered. E.g. use this to register a combatant created with the 'Create Combatant' node.
- Schematics: Lock Combatant: 'Lock Combatant' node available in 'Combatant > Combatant' nodes. Locks a registered combatant from scene-change removing. Non-player group combatants will be removed from the system upon scene changes, locking a combatant will prevent this.
- Schematics: Save Combatant: 'Save Combatant' node available in 'Combatant > Combatant' nodes. Saves the data of a combatant or group into a defined file, global variable or PlayerPrefs variable (string).
- Schematics: Load Combatant: 'Load Combatant' node available in 'Combatant > Combatant' nodes. Loads the data of a combatant or group from a file, global variable or PlayerPrefs variable (string) and store the new combatants into selected data.
- Schematics: Add Item Type: 'Add Item Type' node available in 'Inventory > Inventory' nodes. Adds all currencies, items, equipment, AI behaviours, AI rulesets or crafting recipes of a defined item type to a combatant's inventory.
- Schematics: Remove Item Type: 'Remove AI Behaviours', 'Remove AI Rulesets' and 'Remove Crafting Recipes' settings available. You can now also remove AI behaviours, AI rulesets or crafting recipes of a defined item type from a combatant's inventory.
- Schematics: Area Type Fork: 'Area Type Fork' node available in 'Game > Game' nodes. Checks the current area type.
- Schematics: Mark New Shop Content: 'Mark New Shop Content' node available in 'UI > Shop' nodes. Changes a stored shop's items to be marked or unmarked as new content. Only available for shops having 'Save Shop' enabled in the shop's settings.
- Schematics: Change Loot Rewards: 'Change Loot Rewards' node available in 'Battle > Gains' nodes. Changes the quantity of loot of the battle's gains.
- Schematics: Sell From Inventory: 'Sell Combatants' setting available when using 'Use Selected Data'. Optionally also sell combatants stored in selected data.
- Battle Grid Cell Positions Component: 'Battle Grid Cell Positions' component available. Define individual positions on a cell used for placing player/ally and enemy combatants. This is mainly used when using 'Cell Occupation Settings' to allow multiple combatants on a cell, but can also be used for defining the position of a single combatant on a cell.
- Move AIs: Auto Respawn: 'Destroy Spawned' setting available. Optionally destroy the spawned game object of combatants that are respawned. If disabled, the game objects will only be placed at a new position.
- Move AIs: Saving a combatant will now save the current waypoints and resume movement to the last waypoint when loading.
- Text Codes: 'Player Group Size Limit' text code available. Displays the current size limit of the player group, or 0 if no size limit is used. Size limits for groups can be set using a 'Change Group Size Limit' node in schematics.
- UI Input Filters: New input filter types available for Makinom's new UI input filter system. Adds ability type, AI type, combatant status conditions, crafting type, item type, quest type and research type filters.
- Unity UI: UI Boxes: UI Invert Sorting Input Component: 'UI Invert Sorting Input' component available. Can be used to invert the sorting of displayed content by using a button or toggle input. Only supported by UI boxes that are controlled by something sortable, e.g. menu screens or shops.
- Unity UI: UI Boxes: UI Sorting Cycle Button Component: 'UI Sorting Cycle Button' component available. Can be used to cycle through the available sorting options of displayed content by using a button input. Only supported by UI boxes that are controlled by something sortable, e.g. menu screens or shops.
- Unity UI: UI Boxes: UI Sorting Cycle Button Component: 'UI Sorting Dropdown Input' component available. Can be used to change the sorting of displayed content by using a dropdown input. Only supported by UI boxes that are controlled by something sortable, e.g. menu screens or shops.
- Unity UI: HUD Cost List Component: 'HUD Cost List' component available. Lists the costs of the displayed content (e.g. use costs, level upgrade costs, research costs or buy/sell price cost). The prefabs used for the individual costs need to use an 'ORK HUD Status Text Content' component using the 'Cost' status type to display information on the individual costs.
- Unity UI: ORK HUD Status Text Content Component: 'Cost' type available. Displays information of a cost, e.g. price cost, use cost, level upgrade cost, research cost, etc.
- Unity UI: ORK HUD Status Text Content Component: 'Ability Type' type available. Displays information of an ability type or an ability's ability type or secondary ability types.
- Unity UI: ORK HUD Status Text Content Component: 'AI Type' type available. Displays information of an AI type or an AI behaviour/ruleset's AI type or secondary AI types.
- Unity UI: ORK HUD Status Text Content Component: 'Combatant Type' type available. Displays information of a combatant type or a combatant's combatant type or secondary combatant types.
- Unity UI: ORK HUD Status Text Content Component: 'Crafting Type' type available. Displays information of a crafting type or a crafting recipe's crafting type or secondary crafting types.
- Unity UI: ORK HUD Status Text Content Component: 'Item Type' type available. Displays information of an item type or a shortcut's item type or secondary item types.
- Unity UI: ORK HUD Status Text Content Component: Item: Inventory space text codes available. Use '<space>' to show the inventory space for an item with a quantity of 1 and '<spacefull>' for the item's quantity.
- Unity UI: ORK HUD Status Text Content Component: Equipment: Inventory space text codes available. Use '<space>' to show the inventory space for an equipment with a quantity of 1 and '<spacefull>' for the equipment's quantity.
- Unity UI: HUD Item Box List Component: 'HUD Item Box List' component available. Lists items stored in an item box using 'Shortcut UI'. Either uses the item box of a defined item ID or from an 'Item Collector' attached to the HUD's content (game object).
- Unity UI: HUD Equipment Combatant Content Provider Component: 'HUD Equipment Combatant (Content Provider)' component available. Requires an equipment as content and provides the combatant that has the equipment equipped as content.
- Unity UI: HUD Upgrade Original Shortcut Content Provider Component: 'HUD Upgrade Original Shortcut (Content Provider)' component available. Requires an upgrade shortcut (used by ability/item upgrade menus) and provides the original ability/equipment's shortcut instead of the upgraded preview.
- Unity UI: HUD Combatant Research Tree Content Provider Component: 'HUD Combatant Research Tree (Content Provider' component available. Requires a combatant and provides a defined research tree of the combatant as content.
- Unity UI: HUD Combatant Ability Content Provider Component: 'Ignore Availability' setting available when using 'Ability' ability type. Optionally ignore ability availability settings and use all abilities added to the combatant.
- Unity UI: HUD Ability List Component: 'Ignore Availability' setting available. Optionally ignore ability availability settings and use all abilities added to the combatant.
- Unity UI: Context Menu: 'UI Box > Input Sorting' context menu entries available. Use the new context menu entries to create input sorting inputs (for prefab or added to a UI box).
- HUD Condition Component: 'Shortcut Ability Type' condition type available. Checks if the HUD user is an ability shortcut of a specific ability type.
- HUD Condition Component: 'Shortcut AI Type' condition type available. Checks if the HUD user is an AI behaviour/ruleset shortcut of a specific AI type.
- HUD Condition Component: 'Shortcut Crafting Type' condition type available. Checks if the HUD user is an crafting recipe shortcut of a specific crafting type.
- HUD Condition Component: 'Shortcut Item Type' condition type available. Checks if the HUD user is a shortcut of a specific item type.
- HUD Condition Component: 'Has Upgrade' condition type available. Checks if the HUD user is an equipment/ability with level upgrade or an equipment ability with upgrade learning. Can also check if the upgrade can be used.
- HUD Condition Component: 'Can Consume Cost' condition type available. Checks if the HUD user is a cost (e.g. price cost, use cost, upgrade cost, research cost) that can be consumed (i.e. cost doesn't exceed available quantity).
- HUD Condition Component: 'Has Price' condition type available. Checks if the HUD user is something with price information (buy/sell price).
- HUD Condition Component: 'Sellable' condition type available. Checks if the HUD user is something that can be sold.
- HUD Condition Component: 'Is New Content' condition type available. Checks if the HUD user is marked as new content.
- HUD Condition Component: 'Is Combatant' condition type available. Checks if the HUD user is a combatant (i.e. an actual combatant, e.g. not a combatant coming from a shortcut).
- HUD Condition Component: 'Is Equipment Equipped' condition type available. Checks if the HUD user is an equipment that is currently equipped by a combatant.
- HUD Condition Component: 'Is Equipment Slot' condition type available. Checks if the HUD user is an equipment slot.
- HUD Condition Component: 'Has Use Cost Display' condition type available. Checks if the HUD user is something that can display a use cost (text).
- HUD Condition Component: 'Is Shortcut Assigned' condition type available. Checks if the HUD user is a shortcut (e.g. ability or item) assigned to a shortcut slot of a combatant's current shortcut list.

Changes:
- Quantity Selections, Quantity Questions: '<currencyinventorysub>' and '<currencyinventoryadd>' text codes have been removed. Previous text codes are not removed and will be displayed in-game without being replaced.
- Prices: Text Codes: All price text codes (e.g. '<price>', '<buyprice>' or '<sellprice>') now display the price based on the price texts defined in 'UI > Text Display Settings' (i.e. either using regular price texts or advanced price texts, depending on the price defined by the displayed content). Previously, price text codes could also only display the currency quantity without any additional information.
- Inventories: Adding content that allows keeping 0 quantities can now be added with a quantity of 0 to have it in the inventory (with 0 quantity).
- Status Effect Changes: 'Add Count' and 'Remove Count' settings are now float value selections instead of defined, fix values. E.g. allows using formulas to define the added/removed count of status effects.
- Equipment: Equipment Abilities: Equipment abilities will no longer be added to a combatant/group if the ability has already been learned by the combatant/group.
- Combatants, Equipment, Abilities: Combatants can now use multiple instances of passive abilities coming from equipment. Previously, only the highest level passive ability would be used. For active/useable abilities it remains as it was (i.e. only highest level ability is available).
- Menu Screens: The menu's user is now re-initialized after a potential open schematic was used to take player group changes into account that could be done in the schematic.
- Shop Layouts: The 'Display' setting has been renamed to 'Display Type>List' and moved to the 'Type Box' settings.
- Group/Individual Targets: Input Settings: The 'Remove Target Key' can now be the same key as any of the other target keys (next, previous, nearest). The remove key will remove a target if one was already selected, otherwise allow selecting a new target.
- Schematics: Store Item Box: The 'Store Item Box' node has been renamed to 'Save Item Box'.

Fixes:
- Battle Menus: Equipment: Fixed an issue where equipment slots couldn't open the equipment and unequip selection if no other equipment for the slot was available in the inventory.
- Menu Screens: Multi Slot, Multi Content: Fixed an issue where hidden content (e.g. a hidden ability) was displayed.
- Shop Layouts: Backgrounds: Fixed an issue where general backgrounds where not shown when opening a shop a 2nd time.
- Console: Using 'Auto Remove Lines' and 'Maximum Lines' of 1 caused a new line replacing the previous one to be removed too soon.
- Move AIs: Fixed an issue where blocking move AI during an idle schematic could result in the combatant returning to waypoint movement. Blocking move AI will now stop a running idle schematic.
- Status Bonuses: Status Value Change Modifiers: Fixed an issue where change modifiers where combined wrong or not used at all.
- Status Conditions: Combatant Type: Fixed an issue where negated combatant type checks failed.
- Battle Range Templates: Grid Shape: Line of Sight: Fixed an issue where using 'Raycast Line of Sight' could result in the last cells blocked by line of sight still being available.
- Groups: Locked Field Leader: Fixed an issue where changing a group member in battle that is the locked field leader didn't destroy the combatant's game object.
- Inventory: Fixed an issue where using 'Keep 0 Quantity' could cause an error when removing an item.
- Abilities, Items: Critical Chance: Fixed an issue where the critical chance uses the hit bonus instead of the critical hit bonus.
- Combatants, Save Games: Fixed an issue where loading a save game with dead player combatants could cause their death action/schematics to be executed during loading.
- Class Slots, Classes: Abilities: Fixed an issue where classes equipped on class slots didn't learn abilities bound to class levels on class level up.
- Phase Battles: Battle Menu: Fixed an issue where canceling out of a non-auto open battle menu only worked when also using the combatant selection to select the acting combatant.
- Schematics: Mark As Temporary Combatant: Fixed an issue where combatants wheren't marked as temporary.
- Schematics: Remove Scene Data: Fixed an issue where using 'Global' scene type could cause an error.
- Equipment Viewers: Fixed an issue where changing from a mulit-slot equipment to a single-slot equipment could cause an error.


-------------------------------------------------------------------------------------------------------------------------------------------------------
ORK Framework 3.18.1
-------------------------------------------------------------------------------------------------------------------------------------------------------

Fixes:
- Menu Screens: Quest: Fixed an issue where adding a back button to the 'Info Box' without enabling 'Use Activation Choice' didn't add a back button.
- Menu Screens: Sorting: Buy/Sell Price: Fixed an issue where price sorting could be wrong when displayed for all item types and having special content (e.g. combatants, quests, etc.) in the list).
- Move AIs: Fixed an issue where following the group leader could cause an error after scene changes.
- Grid Battles, Move AIs: Fixed an issue where the move AI could cause wrong combatant placements in battles when auto respawn was enabled (i.e. combatants too far away from group leader).
- Battle Grid Highlights: Line Renderer: Fixed an issue where the 'Enclosed Cells' setting was used inversed, i.e. enabling it wasn't using cell enclosing, disabling it was using cell enclosing.


-------------------------------------------------------------------------------------------------------------------------------------------------------
ORK Framework 3.18.0
-------------------------------------------------------------------------------------------------------------------------------------------------------

New:
- Unity 6: ORK Framework 3 now supports Unity 6.
- Game Settings: Player/Group Settings: 'Use Empty Battle Positions' setting available. The battle group can now have empty positions between combatants, e.g. having a combatant at position 0 and 2, leaving position 1 unoccupied. Empty positions can be used in group menu screens and battle spot placement. By default enabled (previous behaviour).
- Combatant Creation: 'Set Battle Group Index' settings available. Set a newly created combatant's index in the battle group, e.g. index 1 for being the 2nd battle group member. Only used when using 'Use Empty Battle Positions' in the player/group settings. Available everywhere a combatant is created, e.g. in combatant groups, 'Battle' components, 'Join Group' schematic nodes, etc.
- Combatant Creation: 'Use Combatant Type' settings available. Optionally use a random combatant from a defined combatant type instead of a defined combatant when creating combatants.
- Combatants: Price Settings: 'Price Settings' available. Combatants can now be sold to shops. Previously they could only be bought. The new price settings replace the 'Buy Price' setting (previous data is updated automatically).
- Float Values: Status Effect Duration: 'Status Effect Duration' value type available. The duration of a defined status effect applied to a combatant is used as value.
- Float Values: Status Effect Count (All), Status Effect Count (Type): 'Add Hidden Effects' setting available. Define if hidden status effects will be included, excluded or only hidden effects will be counted. Defaults to include hidden effects (previous behavoiur).
- Animations: Custom: Play Function: 'Returns Duration' setting available. If enabled, the play function will require to return a float value representing the animation's duration. If disabled, a separate 'Duration Function' returning a float value for the animation's duration can be used.
- Status Effects: Duration: 'Status Value Change' type available. Status effect duration is reduced when a defined status value of the target was changed. Can optionally check the change value, e.g. limiting to negative or positive changes.
- Status Effects: Duration: 'Selected As Target' type available. Status effect duration is reduced when the effect's target was selected as target of an action (i.e. an action targeting the combatant was added to the system). Can optionally be limited to actions of the effect's user or allies/enemies.
- Status Effects: Duration: Action Start, Action End: 'Is Action User' and 'Is Action Target' settings available. Status effect duration based on actions starting/ending can now also use actions targeting the effect's target. Can optionally be limited to actions of the effect's user or allies/enemies. 'Is Action User' is by default enabled, keeping the previous behaviour.
- Status Effects: Status Changes: Use On: 'Status Value Change' type available. Status changes are used when a defined status value of the target was changed. Can optionally check the change value, e.g. limiting to negative or positive changes.
- Status Effects: Status Changes: Use On: 'Selected As Target' type available. Status changes are used when the effect's target was selected as target of an action (i.e. an action targeting the combatant was added to the system). Can optionally be limited to actions of the effect's user or allies/enemies.
- Status Effects: Status Changes: Use On: Action Start, Action End: 'Is Action User' and 'Is Action Target' settings available. Status changes based on actions starting/ending can now also use actions targeting the effect's target. Can optionally be limited to actions of the effect's user or allies/enemies. 'Is Action User' is by default enabled, keeping the previous behaviour.
- Status Effects: Status Changes: Tagged Machine: 'Tagged Machine' status change type available. Starts a tagged machine on the combatant's game object.
- Abilities, Items: Status Changes: Tagged Machine: 'Tagged Machine' status change type available. Starts a tagged machine on the combatant's game object.
- Abilities: Schematics: 'Level Up Schematic' setting available. Optionally use a schematic when an ability is leveled up. The combatant used for leveling up (usually the combatant knowing it) will be used as 'Machine Object' and 'Starting Object'. The ability is available as (local) selected data with the key 'action'.
- Abilities, Equipment, Research Trees, Status Development: Override Content: 'Custom Content' settings available. Override content of ability/equipment levels, research tree items or status development upgrades can now override custom content. Adding a custom content setup for the same content key will replace the original setup's content with the override content. Also allows using content keys that are not used by the original content.
- Inventory Settings: 'Keep 0 Quantity' setting available. Optionally keep inventory content that is removed with a quantity of 0 in the inventory.
- Item Types: Inventory Settings: 'Keep 0 Quantity' setting available. Optionally override the default 'Keep 0 Quantity' setting defined in 'Inventory > Inventory Settings'.
- Items, Equipment, AI Behaviours, AI Rulesets, Crafting Recipes: 'Keep 0 Quantity' setting available. Optionally override the default 'Keep 0 Quantity' setting defined in 'Inventory > Inventory Settings'. AI behaviours/rulesets only have this available when they use quantity, crafting recipes only have it when they can be consumed.
- Notifications, Item Types, Equipment: 'Equipment Equipped' and 'Equipment Unequipped' notifications available. Optionally display notification UI boxes when a player group member equipped or unequipped equipment.
- Console Settings, Item Types, Equipment: 'Equipment Equipped' and 'Equipment Unequipped' console texts available. Optionally add console texts when a player group member equipped or unequipped equipment.
- Status Conditions: Battle Menu Open: 'Battle Menu Open' condition type available. The combatant's battle menu must or mustn't be opened.
- Status Conditions: Is Affected Target: 'Is Affected Target' condition type available. The combatant must or mustn't be an affected target during the player's target selection (only used when the player selects targets). Affected targets are combatants within the affect range of a selected target.
- Status Conditions: Is Defending: 'Is Defending' condition type available. The combatant must or mustn't be defending.
- Status Conditions: Status Effect Any, Status Effect Type: 'Add Hidden Effects' setting available. Define if hidden status effects will be included, excluded or only hidden effects will be checked. Defaults to include hidden effects (previous behavoiur).
- Battle Menus: Options: Status Changes: Tagged Machine: 'Tagged Machine' status change type available. Starts a tagged machine on the combatant's game object.
- Battle Menus, Menu Screens, Shop Layouts: Description: 'At Input Position' settings available when not using 'Always Visible'. Optionally place the descrition's UI box based on the selected input button's position.
- Menu Screens: Group: 'Auto Select Available' setting available in '1st Group Box Settings'. Optionally have the first available combatant of the list selected when opening the 1st group box. E.g. when using empty battle group positions, this will skip empty buttons and have the first combatant selected.
- Grid Battles: Move Command: Cell Selection Schematics: 'Cell Selection Schematics' settings available. Optionally start a schematic when selecting a cell. The user will be used as 'Machine Object', the selected cell as 'Starting Object'. If a valid path cell is selected, the path to the cell is available as local selected data via the data key 'path'.
- Grid Battles: Move Command: Potential Attack Highlights: 'Potential Attack Highlights' settings available. Optionally highlight enemies that can attack the selected cell. This uses the enemy's move range and action use ranges to determine if the enemy can attack a cell. Highlights can use the usual combatant highlights (e.g. prefab, color fading or schematics) as well as special schematic highlights with additional information available (e.g. selected cell).
- Grid Battles: Orientation Selection, Combatant Placement, Target Cell Selection, Examine Grid: Cell Selection Schematics: 'Cell Selection Schematics' settings available. Optionally start a schematic when selecting a cell. The user will be used as 'Machine Object', the selected cell as 'Starting Object'.
- Inventory Settings: Item Collection: Collection Dialogue: 'HUD Type' settings available for 'Item Message', 'Currency Message' and 'Combatant Message' settings. Optionally use a HUD template or shortcut UI for the item that will be collected.
- Equipment, Inventory Settings: Schematics: 'Level Up Schematic' setting available. Optionally use a schematic when an equipment is leveled up. The combatant used for leveling up (usually the combatant wearing it) will be used as 'Machine Object' and 'Starting Object'. The equipment is available as (local) selected data with the key 'action'.
- Combatants: Body Parts: Death Status Changes: Tagged Machine: 'Tagged Machine' status change type available. Starts a tagged machine on the combatant's game object.
- Combatants: Grid Settings: 'Grid Move Over' settings available. Optionally override the 'Move Over Allies' and 'Move Over Enemies' settings of the grid move command for a combatant.
- Schematics: Actors: Player Group: 'Member' settings available when using 'Current' combatant scope. You can now use the 'Leader' of the group, a 'Member By Index' (group or battle group) or member(s) matching defined conditions (group or battle group).
- Schematics: Change Defending State: 'Change Defending State' node available in 'Battle > Combatant' nodes. Changes the defending state of a combatant, either defending or not defending.
- Schematics: Ability Level Points: 'Ability Level Points' node available in 'Combatant > Ability' nodes. Increases the level points of abilities currently stored in selected data. Level points are the experience or uses needed to level up an ability.
- Schematics: Equipment Level Points: 'Equipment Level Points' node available in 'Inventory > Equipment' nodes. Increases the level points of equipment currently stored in selected data. Level points are the experience or uses needed to level up an equipment.
- Schematics: Join Battle Group: 'Set Battle Group Index' settings available. Set the combatant's index in the battle group, e.g. index 1 for being the 2nd battle group member. Only used when using 'Use Empty Battle Positions' in the player/group settings.
- Schematics: Join Group: 'Get From' setting available, replacing 'Use Game Object' and 'Use Combatant Group' settings. The combatant joining the group can be created using a defined combatant, a random combatant of a defined combatant type, a defined combatant group or use an existing combatant from a defined object (e.g. 'Machine Object').
- Unity UI: HUD Attack Modifier List, HUD Defence Modifier List, HUD Defence Modifier ID List, HUD Status Effect List, HUD Status Value List: 'Total Combatant Bonus' setting available when using 'Show Status Bonus'. Optionally display the total status bonus of a combatant (i.e. from equipment, status effects, etc.) instead of only the bonus defined in the settings of the combatant. Only used if a combatant is displayed, otherwise just displays the bonus of the displayed content.
- Unity UI: Content Providers: HUD Attack Modifier Attribute, HUD Attack Modifier, HUD Defence Modifier Attribute, HUD Defence Modifier, HUD Defence Modifier ID, HUD Status Value: 'Total Combatant Bonus' setting available when using 'Show Status Bonus'. Optionally display the total status bonus of a combatant (i.e. from equipment, status effects, etc.) instead of only the bonus defined in the settings of the combatant. Only used if a combatant is displayed, otherwise just displays the bonus of the displayed content.
- Unity UI: HUD Conditions: Quantity: 'Quantity' condition available. Checks if the HUD user is an inventory content (e.g. item, equipment, etc.) matching a defined quantity check. E.g. use this to only show quantity for items with a quantity greater than 1.

Changes:
- Abilities: Schematics: The 'Add Schematic' is no longer used by ability level ups, using the new 'Level Up Schematic' instead. Previous settings will updated automatically and load the 'Add Schematic' also for the 'Level Up Schematic'.
- Abilities, Equipment: Passive abilities and equipment will now remove the old level's status effects and add the new level's status effects on level up.
- Status Effects: Removing a status effect from a combatant now happens after status changes used on 'Remove'. This allows using the new 'Status Effect Duration' float value type in those status changes, but might impact previous setups that check for status effects during their removal.
- Status Conditions: Combatant Type: Now also checks the secondary types of the combatant.
- Inventory: Adding quantity of items, currency, etc. is now overflow-safe.
- Move AIs: Certain move AI calls will now take the combatant's move AI block state into account instead of only the battle system allowing using move AI for the combatant.
- Shops: You can now sell combatants to shops.
- Schematics: Actors: Player Group: 'Use Member' toggle has been replaced by 'Member' popup field. You can now use either the leader, a member defined by index or member(s) matching defined conditions. Previous settings will be updated automatically and retain their functionality.
- Schematics: Join Group: 'Get From' setting replaces 'Use Game Object' and 'Use Combatant Group' settings. Previous settings will be updated automatically.

Fixes:
- Classes, Class Slots: Fixed an issue where equipping a class without status development on a class slot that allows using status development caused an error.
- Inventory, Equipment Slots: Fixed an issue where using 'Add Inventory Space' enabled in an equipment slot's settings could lead to wrong occupied inventory space when using 'Group' inventory.
- Text Codes: Fixed an issue where text codes for sub-data where not used.
- Status Bonuses, Random Status Bonuses: Status Value Percent: Fixed an issue caused by floating point imprecision that could lead to final bonuses being off by 1.
- Move AIs: Fixed an issue where changing the active player group could lead to an error when the move AI was following the group leader.
- Battles: Fixed an issue where returning to the start menu during a running battle could keep a battle's game object alive.


-------------------------------------------------------------------------------------------------------------------------------------------------------
ORK Framework 3.17.1
-------------------------------------------------------------------------------------------------------------------------------------------------------

New:
- Player Controls: Mouse: 'Manage Move To Interaction' setting available. Provides a better integration with moving to interaction. When a move to an interaction starts, this will update the mouse control's cursor position and cancel movement to interaction when the player controls receive a new movement click.
- Battle Menus: Message Content: 'Message Content' settings available. Add a message (including HUD) to battle menus. Optionally use different messages in the root and sub-menus.
- Battle Menus, Target Settings: Target Menu: 'Hide Multi Mode Menu' setting available when not showing a target menu (i.e. 'Use Target Menu' setting disabled). Optionally hide the battle menu's UI boxes when using multi mode without a target menu during target selection.
- Battle AIs: Ability: 'Add Hidden Abilities' setting available when using a random ability. Using hidden abilities is now optional. By default enabled (previous behaviour).
- Battle AIs: Item: 'Add Hidden Items' setting available when using a random item. Using hidden items is now optional. By default enabled (previous behaviour).
- Formulas: Select Item: 'Filter Settings' available. Optionally filter items and equipment by variable conditions.
- Schematics, Formulas, Battle AIs: Select Ability: 'Add Hidden Abilities' setting available when using 'All Abilities'. Using hidden abilities is now optional. By default enabled (previous behaviour).
- Schematics, Formulas, Battle AIs: Select Items: 'Add Hidden Items' setting available when using 'All Items'. Using hidden items is now optional. By default enabled (previous behaviour).
- Unity UI: HUD Inventory List Component: 'HUD Inventory List' component available. Lists the content (e.g. items, equipment, etc.) of a combatant's inventory using shortcut UI.

Changes:
- Equipment, Prefab View Portraits: Displaying the portrait of a prefab will now use the current menu/shop user (if available) for combatant object variable checks of the equipment's conditional prefabs.

Fixes:
- Combatants, Passive Abilities: Random Status Bonuses: Fixed an issue where random status bonuses of passive abilities where ignored.
- Menu Screens: Inventory, Inventory Container (Slots), Multi Content: Fixed an issue where drag+drop on the same UI box without using 'Drop Slit' settings could cause an error.
- Phase Battle System, Status Effects: Fixed an issue where 'Phase Start' effect duration or use on setups didn't work if the phase battle system's 'Auto Start Turn' setting wasn't set to 'Phase Start'.
- Battle Grid Highlights: Line Renderer: Fixed an issue where line positions where wrong when using rotated grids.
- Schematics: Change Shortcut, Check Shortcut: Using 'Use Selected Data' when changing or checking shortcuts now also uses value selection fields for 'Shortcut List Index' and 'Shortcut Slot Index' settings. Previous settings are updated automatically.
- Equipment Viewer Components: Fixed an issue where equipment viewers checked the player's variables instead of the wearer's variables for object variable conditions used by the equipment's conditional prefabs.
- Editor: Abilities: Fixed an issue where changing an ability between active and passive didn't reset the ability's levels, leading to not correctly set up level settings.


-------------------------------------------------------------------------------------------------------------------------------------------------------
ORK Framework 3.17.0
-------------------------------------------------------------------------------------------------------------------------------------------------------

New:
- Item Collectors, Battles, Combatant Spawners: 'Is Global' setting available for scene IDs. Optionally use a global scene ID that's shared across all scenes. E.g. use this for unique battles or item collectors that should only be used once, independent of the scene.
- Combatant Spawners, Add Combatant: 'Is Global' setting available for 'Remember Combatant' GUIDs. Optionally use a global GUID to remember combatants across all scenes. E.g. use this to have a unique combatant share it's status in multiple scenes.
- Status Conditions: Bestiary: 'Bestiary' status condition check type available. Checks if the combatant has an entry in the bestiary. Can optionally check for a complete entry.
- Status Effects: Duration: 'Phase Start' and 'Phase End' types available. Status effect duration can now use phases in 'Phase' type battles. Can be reduced in any phase or the phase of the user or target.
- Status Effects: Duration: 'Custom' type available. Status effect duration is reduced when a schematic uses a 'Custom Status Effect' node with a matching key on the combatant.
- Status Effects: Status Changes: Use On: 'Phase Start' and 'Phase End' types available. Status changes can now be used based on phases in 'Phase' type battles. Can be be used in any phase or the phase of the user or target.
- Status Effects: Status Changes: Use On: 'Custom' type available. Status changes are used when a schematic uses a 'Custom Status Effect' node with a matching key on the combatant.
- Status Effects: Status Changes: Applied Duration Check: 'Phases' duration check type available. Checks the number of phases an effect is applied (any phase).
- Status Effects: Status Changes: Applied Duration Check: 'Custom' duration check type available. Checks the number of times a 'Custom Status Effect' node was used on the combatant (any key).
- Abilities: Passive: 'Toggle On' and 'Toggle Off' settings available. Optionally use conditions and use costs/status changes when toggling a passive ability on or off. E.g. use the use costs for toggling on to reduce a consumable status value and the use costs of toggling off to increase the status value again.
- Abilities, Items: Status Changes: 'Inventory Add' change type available. Adds an item to the combatant's inventory.
- Abilities, Items: Status Changes: 'Inventory Remove' change type available. Removes an item from the combatant's inventory. When used as use costs it'll be displayed via the use costs text and is also a requirement to use the action (unless 'Exceed Use Costs' is enabled).
- Combatant Body Parts, Battle Menus: Status Changes: 'Inventory Add' change type available. Adds an item to the combatant's inventory.
- Combatant Body Parts, Battle Menus: Status Changes: 'Inventory Remove' change type available. Removes an item from the combatant's inventory.
- Battle AI: Change Found Targets: 'Change Found Targets' node available in 'Target' nodes. Changes the found targets using the first, last, a random or all found targets.
- Menu Screens: Research (List View), Research (Tree View): 'Drag and Drop' settings available. The researched content of research items can be assigned to shortcut slots by dragging or click-dragging. This'll only work on research items that represent things already known or owned by the combatant, e.g. an already researched ability.
- Menu Screens: Research (List View), Research (Tree View): Details Box Settings: 'Back After Start' setting available in 'Use' details box mode. Optionally return to the research item/tree view box after starting research.
- Menu Screens: Sub Menus: 'Show Preview' setting available. Optionally show a status preview of the sub menu's content (e.g. an equipment or abiltiy) while the sub menu is open.
- Battle Menus: Options: Selected Data (Shortcuts): 'Selected Data (Shortcuts)' option type available. Lists shortcuts (e.g. abilities, items) stored in selected data as battle menu options. Each viable shortcut will be added as it's own button.
- Battle End: Level Up Texts: Status Value Text: 'All Status Values' settings available. Listing all status value changes is now optional. When disabled, only defined status values will be listed (can include hidden status values). By default enabled, listing all status value changes (previous behaviour).
- Notifications: 'Max Display Count' setting available when using 'Queue' notification mode. Defines how many notification can be displayed at the same time before being queued.
- Schematics: Select Status Effect: 'Select Status Effect' node available in 'Value > Selected Data' nodes. Uses status effects of combatants as selected data.
- Schematics: Custom Status Effect: 'Custom Status Effect' node available in 'Combatant > Status' nodes. Reduces duration and uses status changes of status effects using custom duration/use on settings with a matching key. Uses the status effects of a combatant or stored in selected data.
- Schematics: Change Status Effect: 'Use Selected Data' settings available. Optionally use status effects stored in selected data to add, remove or toggle effects on combatants. Can optionally use the remaining duration of the stored effects.
- Schematics: Change Effect Duration: 'Use Selected Data' settings available. Optionally change the duration of status effects stored in selected data.
- Schematics: Store Use Range: 'Store Use Range' node available in 'Battle > Action' nodes. Stores the use range of an ability, item or action stored in selected data into a float variable.
- Schematics: Store Affect Range: 'Store Affect Range' node available in 'Battle > Action' nodes. Stores the affect range of an ability, item or action stored in selected data into a float variable.
- Schematics: Remove Scene Data: 'Scene Type' setting available. Defines if the global scene data (shared by all scenes), the data of all scenes (excluding global scene data) or a defined scene's data will be removed. Previous setups will be updated automatically.
- Schematics: Change Scene ID, Is Item Collected, Is Battle Finished: 'Global Scene' setting available. Optionally change/check the scene ID in the global scene data instead of a defined scene. Global scene data is shared by all scenes, e.g. having a unique item collector in multiple scenes.
- Cursor Prefab Components: Schematic: The 'Schematic' variants of cursor prefab components (for combatant highlights, target selections and target raycast cursors) have the game object of the cursor available as local selected data via the data key 'cursor'.
- Unity UI: HUD Attack Modifier List, HUD Attack Modifier Attribute List: 'Target Action Trait' setting available when listing 'Strength', 'Weakness', 'Immunity' or 'Recovery' modifiers/attributes. Optionally base the displayed attribute traits on the currently selected action during target selection of the player. E.g. use this to display if a combatant has a weakness to the selected ability's attack modifiers.
- Unity UI: HUD Conditions: Is Player: 'Is Player' condition available. Checks if the HUD user is the player (requires a combatant).

Changes:
- Status Effects: End Settings: 'Turn/Time Duration' setting has been renamed to 'Duration'.
- Battle Grids: Movement: Grid pathfinding will now prioritize empty cells when move over allies/enemies is allowed. This can result in slightly different paths than before, but works better when the last cell(s) of a path are occupied.
- Schematics, Formulas: Check Equipment: The 'Level' setting has been replaced by 'Level Check' settings, allowing more detailed level comparison (e.g. equals, range checks, etc.).
- Schematics: In Use Range: Can now also check the position of game objects that are not combatants or cells for being in use range.
- Schematics: Change Shortcut, Check Shortcut: 'Shortcut List Index' and 'Shortcut Slot Index' settings now use value selections instead of fix values. Previous settings are updated automatically.
- Status Conditions: Equipment: The 'Level' setting has been replaced by 'Level Check' settings, allowing more detailed level comparison (e.g. equals, range checks, etc.).
- Menu Screens: Inventory Exchange: The inventory exchange menu part will now output a warning to the console when opened without an inventory (e.g. using 'Selected Data' inventory source without having something with an inventory stored in it).

Fixes:
- Menu Screens: Types: Fixed an issue where using an 'All Types' button and only using defined types could list all available types instead of only all defined types.
- Menu Screens: Equipment: Fixed an issue where disabling 'Show Equip Box' blocked all settings below it, even in other menu screen parts.
- Menu Screens: Quest: Fixed an issue where quests of hidden types where listed in merged types or 'All Types' button display.
- Status Effects: Fixed an issue where adding effect stacks didn't work when used by certain setups (e.g. coming from status effect changes).
- Abilities: Passive: Fixed an issue where passive abilities set to 'Start Disabled' started enabled.
- Abilities: Passive: Fixed an issue where a toggleable passive ability's toggle state wasn't saved.
- Abilities, Items, Schematics: Fixed an issue where 'None' target range prevented calculating the action's outcome when using a 'Calculate Action' node.
- Classes, Shortcut Slots: Fixed an issue where auto adding abilities from classes to shortcut slots didn't work.
- Status Bonuses, Random Status Bonuses: Status Value Change Modifier: Fixed an issue where 'Positive' change types wheren't used correctly.
- Start Menu: New Game: Fixed an issue where returning to the start menu from a played game could lead to the start menu being opened again when selecting new game.
- Menu Screens: Ability: Fixed an issue where toggleable passive abilities could call a combatant selection instead of toggling enabled/disabled.
- HUDs: Turn Order: Fixed an issue where the first combatant's HUD in the turn order list could be immediately removed instead of using it's closing schematics when using 'Multi Turns' mode in a 'Turn Based' battle system.
- Shops, Loot: Fixed an issue where using loot to fill a shop's stock didn't allow using combatants from loot.
- Move AIs: Fixed an issue that could lead to combatants getting stuck at the move range when trying to reach an action target.
- Battle System: Turn Bonuses: Fixed an issue where turn bonuses where used twice at the start of a new turn.
- Phase Battle System: Player Combatant Selection: Fixed an issue that caused an error when using the 'Next Combatant Key' while no combatant is selected.
- Schematics: Lock Equipment Slot: Fixed an issue where using 'Lock All' didn't work.
- Schematics: Filter Selected Combatants: Fixed an issue where the 'Enemy Check' was reversed.
- Unity UI: Quest List HUD: Fixed an issue where quests of hidden types where listed. Now they're only listed if the type is defined to be listed.


-------------------------------------------------------------------------------------------------------------------------------------------------------
ORK Framework 3.16.3
-------------------------------------------------------------------------------------------------------------------------------------------------------

New:
- UI Box Selections: 'UI Layout' settings available for most 'UI Box' selection settings. Optionally use a UI layout for the displayed UI box. E.g. available in menu screens or shops.
- Item Selections: Item Type: Using 'Limit Level' for equipment is now supported when item selections are used by item creations (e.g. in loot tables or item collectors).
- Crafting Recipes: Layout: 'HUD Type' settings available. Crafting recipe layouts can now use HUD content for more advanced content display.
- Status Values: Flying Texts: 'Show With Traits' setting available. Optionally show the flying text defined in the status value in addition to an attack modifier's flying text (strenght, weakness or recovery).
- Menu Screens: Equipment: Sub Menu: 'Use In Slot Box' and 'Use In Equipment Box' settings available. The sub menu can now be used in the equipment slot box and using it in the equipment box is now optional. Previous behavoiur is set up as default (i.e. not used in slot box, used in equipment box).
- Schematics: Change Ability Reuse: 'Change Ability Reuse' node available in 'Combatant > Ability' nodes. Changes the running reuse time/turns of a combatant's abilities matching defined block scope or abilities stored in selected data.
- Schematics: Change Item Reuse: 'Change Item Reuse' node available in 'Inventory > Inventory' nodes. Changes the running reuse time/turns of a combatant's items matching defined block scope or items stored in selected data.
- Schematics: Set Move AI Waypoints: 'Use Positions' setting available. Optionally use Vector3 position values instead of game objects do define waypoints.
- Scripting: Combatant Spawner: 'Spawned' property available. This gives script access to the 'SpawnerData' array, containing references to the spawned groups (and their combatants).
- Unity UI: ORK HUD Status Text Content Component, ORK HUD Value Bar Content Component: Reuse Time: 'Always Display' setting available. Optionally always display reuse time, even if it's currently not running.
- Unity UI: UI Research Tree (Single), UI Research Tree (Multi): 'HUD Content Provider' setting available in 'Tree Creator' settings. Optionally use a child object with a 'HUD Content Provider' component to display content information of a research tree.

Changes:
- Item Selections: Item Type: Not using 'Limit Level' for equipment now uses a random level between 1 and the maximum level of the equipment.
- Ability Type Limits, Item Type Limits: The optional type limit settings now also take secondary types into account. This is e.g. used by status effects (end on ability/item) or status change blocks.

Fixes:
- Abilities, Items: Reuse: Fixed an issue where 'Custom' block scope wasn't working correctly.
- Quest Tasks: Notifications: Fixed an issue where notifications where still displayed when the required count was already reached. Now only allowing overcount will show the notification.
- Phase Battle System: Player Combatant Selection: Fixed an issue where an error could occur when no selectable combatants are available and a combatant is clicked.
- HUD Conditions: Fixed an issue where 'Reuse' condition type could cause an error when used in tooltips.
- Group Changes: Fixed an issue where an error occured when changing the player combatant (e.g. using the group menu).
- Schematics: Select Grid Cells: Fixed an issue where using 'Store Combatants' didn't work unless a combatant was on the origin object/cell or a separate user was defined.


-------------------------------------------------------------------------------------------------------------------------------------------------------
ORK Framework 3.16.2
-------------------------------------------------------------------------------------------------------------------------------------------------------

New:
- Player Controls: Mouse: 'Remove Set Move AI' setting available. Optionally set the last clicked target position as the combatant's move AI waypoint when the control component is removed (e.g. when changing player after clicking to move somewhere).
- Status Conditions: Status Effect Any: 'Status Effect Any' status condition check type available. Checks if any status effect is applied. Optionally checks the number of applied effects.
- Unity UI: ORK HUD Status Text Content Component: Status Value: New text code available. Use '<valuebonus>' to display the bonuses added to the base value (i.e. current value - base value). When preview values are displayed, it shows the difference between preview and ase value (i.e. preview value - base value).

Changes:
- Schematics: Spawn ORK Player: 'Rotation' and 'Scale' settings when spawning at 'Position' now use Vector 3 value selections. Previous data will be updated automatically.

Fixes:
- Abilities, Items: Status Changes: Fixed an issue where modifier changes where only used for negative changes, not positive changes.
- Status Values: Flying Texts: Fixed an issue where using 'Attribute Content' didn't work without also using 'Source Content'.
- Status Developments: Fixed an issue where using a 'Formula' development type for 'Experience' type status values that have 'From Minimum' enabled didn't result in the correct value.
- Menu Screens: Research (List View), Research (Tree View): Fixed an issue where using 'Sequence' display mode could lead to UI boxes closing immediately after opening or when the menu was refreshed (e.g. due to external change).
- Active Time Battles: Fixed an issue where 'Use All Actions' was still used when 'Use Multi Choice' was disabled.
- Active Time Battles, Turn Based Battles: Battle Menu Call: Fixed an issue where not using 'Auto Call Menu' and using 'Cancel Closes' (without a custom cancel key) didn't close a called battle menu on cancel.
- Target Selection Templates: Fixed an issue where the default templates created for a new project didn't initialize some data for 'None' target range setups.
- Battle AIs: In Grid Action Range: Fixed an issue where checking the targets overruled a positive result from checking found targets.
- Damage Dealers: Fixed an issue where 'Set Combatant' (when using an 'Always On' damage dealer) didn't allow setting a combatant.
- Battle Grid Settings: Examine Grid: Combatant Info Box: Fixed an issue where using 'Auto Show' while having the (now hidden) 'Use Call Key' setting enabled didn't show the combatant information.
- Battle Grid Settings: Examine Grid: Fixed an issue where using 'Target Selection Examine' settings didn't close the examine infos when canceling out of the target selection.
- Battle Grid Highlights: Fixed an issue where using 'Hide Unused Prefabs' and hiding the grid after battle (instead of destroying it) could lead to highlights not being displayed when fighting on the same grid again.
- Schematics: Spawn ORK Player: Fixed an issue where spawning at 'Position' didn't allow setting the rotation.
- Schematics: Use Battle Action: Fixed an issue where using actions for multiple combatants with a wait time between actions could lead to a stack overflow error when the wait time is 0.


-------------------------------------------------------------------------------------------------------------------------------------------------------
ORK Framework 3.16.1
-------------------------------------------------------------------------------------------------------------------------------------------------------

Fixes:
- Battle Grids: Highlights: Fixed an issue where a combatant's cell highlights could cause an error when being removed.


-------------------------------------------------------------------------------------------------------------------------------------------------------
ORK Framework 3.16.0 ABSOLUTE STATE
-------------------------------------------------------------------------------------------------------------------------------------------------------

New:
- Status Effects: Status Changes: 'Status Changes' settings available. Status changes combine the previous status value changes, schematics, end effect changes and end action and allow using them on turn, time, action, etc. (as the previous status value changes did). Add custom status changes by extending from the 'BaseEffectStatusChangeType' class. Previous settings are automatically updated.
- Status Effects: Status Changes: Action Bar: 'Action Bar' status change type available. Changes the action bar of the combatant. In 'Turn Based Battles' and 'Phase Battles', this will change the actions per turn of the combatant. In 'Active Time Battles', this will change the timebar of the combatant.
- Status Effects: Status Changes: Move Range: 'Move Range' status change type available. Changes the grid move range.
- Status Effects: Status Changes: Variable: 'Variable' status change type available. Changes variables.
- Status Effects: Status Changes: Animation: 'Animation' status change type available. Plays an animation type on the combatant.
- Status Effects: Status Changes: Apply: 'On Apply' and 'On Reapply' settings available. You can now control if the status change is used on first applying and/or reapplying the status effect. Defaults to only being used 'On Apply' (previous behaviour, since reapplying didn't use 'Apply' changes again).
- Status Effects: Status Changes: Remove: 'Effect Removed By' settings available. You can now control when a status change used on 'Remove' of the status effect is used by the type of removal. E.g. you can separate between remove by end of the battle, attack, death, etc.
- Status Effects: Status Changes: Conditions: 'Conditions' settings available for status changes. Optionally check the user or target of the effect for various conditions, e.g. variables or combatant status. If the check fails, the status change will not be used.
- Status Effects: Status Changes: Applied Duration Check: 'Applied Duration Check' settings available. Check the effect's applied duration to allow using a status change. Applied duration checks are independent of the status effect's actual duration, e.g. you can check for turns in a time-based effect (or with no duration at all). Applied duration can check for turns, battle turns, time and actions (any action).
- Status Effects: Status Changes: 'Is Enemy' setting available. Optionally only use a status change if user (combatant who cast the effect on the target) and target (combatant who has the effect applied) are enemies or allies.
- Status Effects: Status Changes: 'Switch User/Target' setting available. Optionally use the status change with user and target switched, e.g. restoring health on the user (combatant who cast the effect on the target).
- Status Effects: 'User Snapshot' setting available. Calculations using the user's status will use the status information at the time the effect was applied. This'll create a copy of the user combatant for this effect, but it'll not have access to the original user's group. Status changes done by this effect on the user will still use the original user, not the copy.
- Status Effects: 'Local Variables' and 'Local Selected Data' settings available. Select how local variables/selected data are used. They can either be shared with the cast (e.g. from schematic), copied from cast or create a new instance.
- Random Status Bonuses: Combatants, Classes, Equipment, (Passive) Abilities, Status Effects: 'Random Status Bonuses' settings available. Add random status bonuses to individual instances of combatants, classes, passive abilities, equipment or status effects. Replace previously available random bonus settings (equipment, combatants, classes).
- Random Status Bonuses: 'Status Value', 'Status Value Percent' and 'Status Value Change Modifier' random bonus types available. Add a random bonus to a status value, either a direct value bonus, a percent value bonus or a bonus to change modifiers (e.g. positive health changes +25%).
- Random Status Bonuses: 'Attack Modifier' and 'Attack Modifier Factor' random bonus types available. Add a random bonus to an attack modifier attribute's value or factor.
- Random Status Bonuses: 'Defence Modifier' and 'Defence Modifier Factor' random bonus types available. Add a random bonus to a defence modifier attribute's value or factor.
- Random Status Bonuses: 'Random Bonus' random bonus type available. Add a set of random bonuses.
- Random Status Bonuses: 'Block Chance', 'Counter Chance', 'Critical Chance', 'Currency Steal Chance', 'Escape Chance', 'Hit Chance' and 'Item Steal Chance' random bonus types available. Add a random bonus to the defined chance.
- Random Status Bonuses: 'Experience Factor' random bonus type available. Add a random bonus to a combatant's experience factor.
- Random Status Bonuses: 'Grid Move Range' random bonus type available. Add a random bonus to a combatant's grid move range.
- Random Status Bonuses: 'Inventory Slot' random bonus type available. Add a random bonus to a combatant's inventory slots.
- Random Status Bonuses: 'Item Limit' random bonus type available. Add a random bonus to a combatant's item limit.
- Random Status Bonuses: 'Random Battle Factor' random bonus type available. Add a random bonus to the random battle factor (player group only).
- Random Status Bonuses: 'Walk Speed', 'Run Speed' and 'Sprint Speed' random bonus type available. Add a random bonus to a combatant's walk, run or sprint speed.
- Random Status Bonuses: 'Chance Selection' add range available. Add random status bonuses based on their chance setting. E.g. bonus 0 has 35% chance, bonus 1 has 50% chance, bonus 2 has 15% chance, i.e. bonus 0 is used for 0-35 chance range, bonus 1 for 35-85 and bonus 2 for 85-100.
- Status Bonuses: Most status bonuses have been combined into a single, extensible setup. This affects all status bonus settings, e.g. in status bonus templates or custom bonuses of equipment.
- Status Bonuses: 'Status Value', 'Status Value Percent' and 'Status Value Change Modifier' bonus types available. Add a bonus to a status value, either a direct value bonus, a percent value bonus or a bonus to change modifiers (e.g. positive health changes +25%). This was previously combined in a single setup, the data is udpated automatically.
- Status Bonuses: 'Attack Modifier' and 'Attack Modifier Factor' bonus types available. Add a bonus to an attack modifier attribute's value or factor. This was previously combined in a single setup, the data is udpated automatically.
- Status Bonuses: 'Defence Modifier' and 'Defence Modifier Factor' bonus types available. Add a bonus to a defence modifier attribute's value or factor. This was previously combined in a single setup, the data is udpated automatically.
- Status Bonuses: 'Block Chance', 'Counter Chance', 'Critical Chance', 'Currency Steal Chance', 'Escape Chance', 'Hit Chance' and 'Item Steal Chance' bonus types available. Add a bonus to the defined chance.
- Status Bonuses: 'Experience Factor' bonus type available. Add a bonus to a combatant's experience factor.
- Status Bonuses: 'Grid Move Range' bonus type available. Add a bonus to a combatant's grid move range.
- Status Bonuses: 'Inventory Slot' bonus type available. Add a bonus to a combatant's inventory slots.
- Status Bonuses: 'Item Limit' bonus type available. Add a bonus to a combatant's item limit.
- Status Bonuses: 'Random Battle Factor' bonus type available. Add a bonus to the random battle factor (player group only).
- Status Bonuses: 'Walk Speed', 'Run Speed' and 'Sprint Speed' bonus type available. Add a bonus to a combatant's walk, run or sprint speed.
- Abilities, Items: Status Changes: Animation: 'Animation' status change type available. Plays an animation type on the combatant.
- Target Selections: Target Raycast: 'Raycast Type' settings available. Raycast targeting now supports different raycasting options to select targets. The previous mode is now available as 'Position' type, where the raycasted position is used directly.
- Target Selections: Target Raycast: 'Game Object' raycast type available. Available raycast target positions are defined by game objects found in the scene. Game objects can be searched by tag, name or an attached component. Available positions can be highlighted by a cursor prefab.
- Target Selections: Target Raycast: 'Grid' raycast type available. Available raycast target positions are generated in a square or hexagonal grid around the user. Available positions can be highlighted by a cursor prefab.
- Target Selections: Target Raycast: 'Auto Target' setting is now a selection. Auto targeting can now use the user, a target (found using the user's AI settings) or the screen center (also used as fallback when user/target aren't available). Previous settings will be updated automatically.
- Target Selections: Target Raycast: 'Local Space' settings available when using auto targeting settings. The offset to the target during auto targeting can now be used in local space of the target.
- Target Settings: Affected Target Highlight: 'Affected Target Highlight' settings available. Optionally highlight combatants within affect range of a targeted combatant during target selection.
- Target Selection Templates, Abilities, Items: Conditional Affect Ranges: 'Distance Condition' settings available for conditional affect ranges. Optionally base conditional affect ranges on the distance between user and target (or targeted cell).
- Active Time Battles: Pause On Menu: 'Pause In Root', 'Pause In Sub' and 'Pause In Target Selections' settings available when using 'Pause On Menu'. Pauses the battle while the the root (i.e. main battle menu option selection), a sub-menu or a target selection (including grid examine/move/orientation) is currently used by the battle menu.
- Action Combos: 'Ignored Action' settings available. Optionally add actions that will be ignored when used by the combatant. Ignored actions will not reset the stage if used. E.g. use this if you don't want a 'None' action (e.g. used to end the turn) to interfere with action combos.
- Player Controls: Top Down 2D: 'No Diagonal Movement' setting available. Optionally block diagonal movement, i.e. only horizontal and vertical movement is allowed.
- Combatants: Body Parts: Death Status Changes: Animation: 'Animation' status change type available. Plays an animation type on the combatant.
- Move AIs: Enemy Detection: Move Detection: 'Detection Conditions' settings available. Optionally only detect targets if defined conditions are valid. Uses the same conditions as e.g. hunting or fleeing. Conditions are only used for detecting combatants, not points of interest.
- Move AIs: Conditions: Conditions in move AIs are now an extensible class. Extend from the 'BaseMoveConditionType' class to implement custom conditions. Previous settings will be udpated automatically.
- Move AIs: Conditions: 'Game State' condition type available. Checks if game states are active or inactive.
- Move AIs: Conditions: 'Variable' condition type available. Checks variables (e.g. global variables, object variables on user/target, etc.).
- Move AIs: Conditions: 'Difficulty' condition type available. Checks the game's difficulty.
- Move AIs: Conditions: 'Quest' condition type available. Check quest status conditions.
- Move AIs: Conditions: 'Quest Task' condition type available. Check quest task status conditions.
- Move AIs: Conditions: 'Quest Type' condition type available. Check quest type status conditions.
- Game Controls: Group Member Keys: 'Click Member' settings available. Optionally allow switching the player or the battle menu user by clicking on a combatant.
- Menu Screens: Menu Actions, Sub Menus: 'To Item Box' menu action available. Stores an item/equipment/etc. into a defined item box (via box ID).
- Menu Screens: Group: 'Hide Empty Slot' setting available in 1st and 2nd group box settings. Optionally hide empty slot buttons, e.g. in case you only want to use drag+drop input.
- Battle Menus: Time Scale: 'Change In Root', 'Change In Sub' and 'Change In Target Selections' settings available when using 'Change Time Scale'. Changing the time scale can now be used while the the root (i.e. main battle menu option selection), a sub-menu or a target selection (including grid examine/move/orientation) is currently used by the battle menu. E.g. only stop the timescale when being in a sub menu.
- Battle Menus: 'Remember In Root' setting available when using 'Remember Selection'. Remembering the selection in the root of the battle menu (i.e. where the main battle menu options are selected) is now optional. By default enabled (previous behaviour).
- Battle Menus: Options: Schematic, Menu Screen: 'Reset Battle Menu' setting available when closing and reopening the battle menu. Optionally reset the battle menu when opening to restart from the menu's root instead of restoring the previous menu.
- Battle Menus: Options: Status Changes: Animation: 'Animation' status change type available. Plays an animation type on the combatant.
- HUDs: Combatant, Combatant Object: Display Conditions: 'Is Affected Target' setting available when using 'Is Targeted' settings. Optionally only display the HUD if the combatant is currently highlighted as an affected target by the regular target selection (e.g. via battle menu).
- HUDs: Console: 'Limit Lines' settings available. Optionally limit the number of displayed lines of the console. This overrides the maximum lines defined in 'UI > Console Settings & Types'.
- UI Settings: Prefab View: 'Disable Machines' setting available in prefab settings for prefab view. Disable all machine components on a prefab spawned for prefab view.
- UI Settings: Prefab View: 'Disable Component' settings available in prefab settings for prefab view. Define components that should be disabled on a prefab spanwed for prefab view.
- Schematics: Add Random Status Bonus: 'Add Random Status Bonus' node available in 'Value > Selected Data' nodes. Add a random status bonus to an equipment, passive ability, combatant, class or anything implementing the 'IAddRandomStatusBonus' interface stored currently stored in selected data.
- Schematics: Remove All Status Bonus: 'Remove All Status Bonus' node available in 'Value > Selected Data' nodes. Remove all instance status bonuses of an equipment, passive ability, combatant, class or anything implementing the 'IAddRandomStatusBonus' interface stored currently stored in selected data. This only removes instanced bonuses, e.g. from random status bonuses or from bonuses added via the 'Change Equip Status Value' node.
- Schematics: Set Battle Action Finished: 'Set Battle Action Finished' node available in 'Battle > Action' nodes. Sets the schematic's action (or actions stored in selected data) to be finished while still continuing the action's schematics. This'll cause the action's end to be used (e.g. unblocking controls, ending turn, etc.) and releases the user from being in action (e.g. allowing a new action to be used).
- Schematics: Is Battle Action Finished: 'Is Battle Action Finished' node available in 'Battle > Action' nodes. Checks if the schematic's action (or actions stored in selected data) is finished (i.e. ended by finishing all schematics or via the 'Set Battle Action Finished' node).
- Schematics: Is Current Battle Action: 'Is Current Battle Action' node available in 'Battle > Action' nodes. Checks if the schematic's action (or actions stored in selected data) is the current action of a combatant.
- Schematics: Change Selected Data Quantity: 'Change Selected Data Quantity' node available in 'Value > Selected Data' nodes. Changes the quantity of content in selected data. This can be used to change the quantity of items, equipment or other shortcuts stored in selected data.
- Schematics: Calculate Action, Use Ability Calculation, Use Item Calculation: 'Calculation Options' settings available. Define if user and target changes will be used. Target changes can also only use a defined target change index instead of all target changes (e.g. only using 'Target Change 0' or 'Target Change 1')
- Schematics: Reopen Battle Menu: 'Reset Battle Menu' setting available. Optionally reset the battle menu when opening to restart from the menu's root instead of restoring the previous menu.
- Schematics: Store Inventory Quantity: 'Use Selected Data' settings available. Optionally use items stored in selected data to get the available quantity in the inventory.
- Schematics: Quest Choice: 'No Schematics' setting available. A newly created quest instance (only used for showing information) will not start any schematics (e.g. 'Activate Schematic' of quest tasks).
- Schematics: Join Battle: 'Set Turn Ended' setting available when not using 'Join Turn Order'. Sets the combatant's turn as ended (i.e. 'After Turn'). E.g. use this to show the correct grid highlight for a combatant that can't perform actions or other turn state based conditions.
- Schematics: Join Turn Order: 'Join Turn Order' node available in 'Battle > Combatant' nodes. A combatant joins the turn order of a running battle. This is only used by 'Turn Based' and 'Phase' battles, allowing the combatant to perform actions in the current turn.
- Schematics: Set Turn Ended: 'Join Turn Order' node available in 'Battle > Combatant' nodes. A combatant's turn state is set to have ended (i.e. 'After Turn' state). This doesn't use the combatant's regular end turn functionality and only sets the state.
- Random Battle Area Components: 'Check Local Position' setting available. Optionally check the local position of the player instead of the world position. E.g. use this when the player is mounted on a moving platform.
- Scripting: Access Handler: New access handler function available to cancel casting an action.
- Scripting: Random Status Bonuses: Add custom random status bonuses by extending from the base 'BaseRandomBonusType' class.
- Scripting: Status Bonuses: Add custom status bonuses by extending from the base 'BaseBonusType' class.
- Unity UI: ORK HUD Status Text Content Component: Combatant Console: 'Limit Lines' settings available. Optionally limit the number of displayed lines of the combatant's console. This overrides the maximum lines defined in 'UI > Console Settings & Types'.

Changes:
- Status Effects: 'End Status Effect Changes', 'End Action', 'Status Value Changes' and 'Schematics' settings have been replaced by the new 'Status Changes' settings. Previous data is updated automatically.
- Status Effects: 'Status Value Changes' have been split into 'Status Value', 'Block Status Value' and 'Absorb Status Value' status change types. Previous data is updated automatically.
- Equipment: 'Random Status Value Bonuses', 'Random Attack Modifier Bonuses' and 'Random Defence Modifier Bonuses' settings have been replaced by the new 'Random Status Bonuses' settings. Previous data is updated automatically.
- Combatants, Classes: 'Random Status Value Bonuses' settings have been replaced by the new 'Random Status Bonuses' settings. Previous data is updated automatically.
- Status Bonuses: 'Chance/Other Bonuses', 'Status Value Bonuses', 'Status Change Modifiers', 'Attack Modifier Bonuses' and 'Defence Modifier Bonuses' settings have been replaced by the new 'Status Bonuses' settings. Previous data is updated automatically. This affects all status bonuses everywhere, i.e. status bonus templates and custom bonuses of equipment, status effects, combatants, etc.
- Directional Target Selections: Directional selection based on screen position has been improved. Horizontal or vertical input how prioritizes based on the input axis distance instead of overall distance, resulting in a more natural selection.
- Directional Target Selections: 'Allow Both' setting has been removed.
- Menu Screens: 'Sequence' display mode now only closes a sequenced UI box if one of it's parent UI boxes gained focus instead of any other UI box.
- Battle Menus: The 'Time Scale' settings have been moved into the 'Options' foldout.
- Battle Grids: A combatant moving off a cell will remove all combatant-related grid cell highlights.
- Unity UI: ORK HUD Status Text Content Component: Ability, Item: The max reuse time/turns texts will now display the first found time when no reuse time is running for the ability/item. Previously the max time was only shown when a reuse time was running.

Fixes:
- Action Combos: Replacement Action: Fixed an issue where the 'Reset Stage On Fail' setting wasn't used and always reset the stage when the replacement action wasn't found.
- Abilities, Items: Absorb: Absorbing status changes on 'Normal' type status values didn't change the base value, i.e. the change was reset by the next status recalculation.
- Menu Screens: Fixed an issue where stopping playing with a menu screen open when not using 'Domain Reload' (Unity) could lead to issues in the next playtest.
- Menu Screens: Group: Transfer: Fixed an issue where locked battle members could be transferred.
- Menu Screens: Sub Menus: Fixed an issue where sub menus couldn't be opened by click-accept when using an accept timeout.
- Shops: Fixed an issue where sorting by 'Added' wasn't working for items in the player's inventory.
- Target Selections: Fixed an issue where showing a tooltip of another action didn't show the highlighted targets again when the tooltip ended. This also affected target information displays.
- Inventory: Space Limit: Status Effects: Fixed an issue where only one combatant received status effects (based on inventory filling) instead of all group members when using 'Group' inventory.
- Phase Battles: Fixed an issue where combatants joining the turn order during a running turn/phase where not able to select actions.
- Cursor Settings, Grid Battles: Cell Target Selection: Fixed an issue where the cursor could flicker between 2 states during grid cell target selections.
- Change Member, Conditional Prefabs: Fixed an issue where using the 'Change Member' command with a combatant using conditional prefabs to use a separate prefab in battles could result in the old combatant not being destroyed correctly, spawning it again with the field prefab.


-------------------------------------------------------------------------------------------------------------------------------------------------------
ORK Framework 3.15.2
-------------------------------------------------------------------------------------------------------------------------------------------------------

Fixes:
- Abilities: Fixed an issue where loading the data could result in an error if the ability wasn't yet set up in the editor. This mainly occured in new projects where the default ability wasn't yet viewed or set up.
- Menu Screens: Group: Transfer: The 'Transfer' action now supports drag+drop to move combatants between groups.


-------------------------------------------------------------------------------------------------------------------------------------------------------
ORK Framework 3.15.1
-------------------------------------------------------------------------------------------------------------------------------------------------------

New:
- Editor: Abilities, Items: Target Selection Settings: 'Create Custom From Template' button available when using 'Template' or any 'Default' target selection type. Uses the settings of a template to create a 'Custom' target selection setup.

Fixes:
- HUDs: Combatant: Fixed an issue where some settings where not displayed.


-------------------------------------------------------------------------------------------------------------------------------------------------------
ORK Framework 3.15.0 TARGETS LOCKED
-------------------------------------------------------------------------------------------------------------------------------------------------------

New:
- Combatants: Body Parts: 'Body Parts' settings availabe in the 'Status Settings'. Optionally add body parts to a combatant. Body parts define their status via another combatant's setup and are added or mounted to their parent combatant's game object. They're individual combatants and can e.g. be added as passive combatants or also use actions in battles. Killing a body part can use status changes on it's parent combatant.
- Combatants: Prefabs: 'Fallback Radius' setting available. Optionally define a radius that'll be used if no 'Radius' component is found on the combatant's game object.
- Target Selection Templates: 'Target Selection Templates' sub-section available in 'Templates' section of the editor. Define templates for target selection settings (target type/range, use range, etc.). The templates are used by abilities, items and default target selection settings. Using templates allow you to quickly change settings for a vast range of abilities/items.
- Target Settings: Default Target Selections: 'Default Target Selections' settings available. Define which target selection templates are used by 'Default' target selection types. Separate default selections are defined for enemy single/group/none, ally single/group/none, all single/group/none and self targeting. Ability/item types can override default target selections for their abilities/items.
- Target Settings: Available Target Highlight: 'Available Target Highlight' settings available. Optionally highlight available targets during action/target selection.
- Abilities, Items: Target Selection Settings: 'Target Selection Type' setting available. Select which type of target selection is used, a 'Template', a 'Custom' setup or one of the 'Default' target selections. Previous settings are automatically updated to 'Custom'. Custom setups can be turned into templates via a button.
- Abilities, Items, Target Selection Templates: Auto Damage Multipliers: 'Auto Damage Multipliers' settings available (found in target selection setup). Optionally use an automatic damage multiplier that impacts status changes depending on having a single or more than one target. You can define separate multipliers for single and multi targets. E.g. a 'Single' targeting ability that can toggle the target range to 'Group' can do less damage on multiple targets.
- Ability Types, Item Types: 'Default Target Selections' settings available. Optionally override the default target selections defined in 'Battles > Target Settings'. Abilities/items using the type as their primary type will use the type's target selections when using a 'Default' selection type. Only the defined target selections will be used, otherwise it'll fall back to the global default selections.
- Battle Menus: Target Selection Settings: 'Target Selection Settings' available. Define the target selection mode of the battle menu. Targets can either be selected after actions (i.e. targets depend on actions) or before actions (i.e. actions depend on targets). Defaults to 'After Action' target selection (previous functionality).
- Battle Menus: Ability: 'Check Useable Ability' setting available for 'Combined' and 'Type' display type. Optionally check if a combined/type button contains a useable ability. If no useable ability is found, the button will be inactive.
- Battle Menus: Item: 'Check Useable Item' setting available for 'Combined' and 'Type' display type. Optionally check if a combined/type button contains a useable item. If no useable item is found, the button will be inactive.
- Battle Menus: Equipment: 'Blocked Slot Display' and 'Linked Slot Display' settings available. Define how slots blocked by equipment (on other slots) or slots linked to other slots (via multi-slot equipment) will be displayed. Either show them as active (can be changed), inactive (can't be changed) or hide them.
- Status Conditions: Has Body Part: 'Has Body Part' status condition check type available. Checks if the combatant has body parts or a defined body part (combatant).
- Status Conditions: Is Body Part: 'Is Body Part' status condition check type available. Checks if the combatant is a body part of another combatant or a defined combatant.
- Status Conditions: Battle Turn: 'Battle Turn' status condition check type available. Checks the current battle turn against a defined number. The battle turn is independent of the combatant's turn.
- Status Conditions: Turn: 'Turn' status condition check type available. Checks the combatant's current turn against a defined number.
- Game States: Auto Activate, Auto Inactivate: 'Item Collection Start' and 'Item Collection End' state change events available. Caused when item collection is started or ended, e.g. to create an item collection game state that's active while the player collects items via 'Item Collector' components.
- Camera Controls: Top Down Border: Panning: 'Ignore Player Move' setting available. Optionally ignore changes to the player position while panning. Panning will be based on the position the panning started from, the player's movement will be ignored.
- Camera Controls: Top Down Border, Mouse: 'In Battle' setting available for 'Remember' options. Optionally not remember/restore rotation, panning or zoom when loading into or out of a battle scene. By default enabled (previous behaviour).
- Quest Layouts: Rewards: 'Normal SV Reward Text' settings and new text code available. You can now display rewards for 'Normal' type status values and optionally use a separate text for them.
- Quest Layouts: Tasks: New text codes available to show the rewards of a quest task.
- HUDs: Combatant, Combatant Object: Display Conditions: 'Is Available Target' setting available when using 'Is Targeted' settings. Optionally only display the HUD if the combatant is currently available as a target by the regular target selection (e.g. via battle menu).
- Menu Screens: Equipment: 'Blocked Slot Display' and 'Linked Slot Display' settings available. Define how slots blocked by equipment (on other slots) or slots linked to other slots (via multi-slot equipment) will be displayed. Either show them as active (can be changed), inactive (can't be changed) or hide them.
- Menu Screens: Inventory Exchange: 'Inventory Source' settings available in the 'Item Box' settings. Define the inventory source that'll be used (e.g. menu screen user or defined item box) when the inventory exchange is used without defining the inventory sources (e.g. opening it via regular menu call or 'Button List' menu part instead of shop, item box or 'Call Menu Screen' node).
- Menu Screens, Shop Layouts: Sorting: 'Use Sort Key' settings available for individual sort options. Optionally use an input key to directly change to a sorting option instead of toggling between the available options.
- Menu Screens, Shop Layouts: Sorting: 'Quantity Price' setting available for 'Buy Price' and 'Sell Price' sorting. Optionally sort by the price for the whole stack instead of 1 unit.
- Menu Screens: Status Value Distribution: 'Accept Close' setting available. Defines if accepting the status changes will close the menu screen. By default enabled (previous behaviour).
- Shop Layouts: Type Box, List Box: 'Mark New Types/Items' settings available. Optionally use a different content layout for type/item buttons with new content.
- Shops: 'Buyback Sell Price' settings available. Optionally allow the player to buy back items sold to the shop at the price the items where sold. Also works without saving sold items (items will be removed when closing the shop). When saving sold items, the buyback price can be optionally reset when closing the shop (selling at regular price).
- Notifications: 'Use Content Icon' settings available for 'Title' and 'Message' content. Optionally use the notification content's icon instead of a defined icon (e.g. the icon of the item the notification is for).
- Battle Texts, Status Effects: Add Flying Text: 'Show Recast' setting available. Optionally show the 'Add Flying Text' when a status effect is recast on a combatant. Only used when 'On Recast' is set to 'Add' or 'Reset' (i.e. when an effect can be recast).
- Battle End: Loot Dialogues: 'No Outcome' setting available in 'Battle Outcome' settings. The loot dialogue will be used for loot collection when no battle outcome has been achieved yet (e.g. loot collection during a running battle). By default enabled.
- Formulas: Select Combatant Body Part: 'Select Combatant Body Part' node available in 'Selected Data' nodes. Uses body parts of combatants or the parent combatant of body parts as selected data.
- Battle AIs: Select Combatant Body Part: 'Select Combatant Body Part' node available in 'Selected Data' nodes. Uses body parts of combatants or the parent combatant of body parts as selected data.
- Battle AIs: Add Combatant Body Part: 'Add Combatant Body Part' node available in 'Combatant' nodes. Adds body parts to a combatant.
- Battle AIs: Remove Combatant Body Part: 'Remove Combatant Body Part' node available in 'Combatant' nodes. Removes body parts from a combatant.
- Battle AIs: Ability: 'Know Ability' setting available when not using 'Use Random Ability'. The combatant knowing the ability is now optional, i.e. battle AIs can also use abilities that are not known to the user. By default enabled (i.e. previous behaviour, requiring to know the ability).
- Schematics: Actors: ORK Player: 'Player Type' setting available. Select which player to use (field or battle). Can use the 'Current' player (based on being in battle or in the field), the 'Field' player or the 'Battle' player.
- Schematics: Select Combatant Body Part: 'Select Combatant Body Part' node available in 'Combatant > Body Part' nodes. Uses body parts of combatants or the parent combatant of body parts as selected data.
- Schematics: Add Combatant Body Part: 'Add Combatant Body Part' node available in 'Combatant > Body Part' nodes. Adds body parts to a combatant.
- Schematics: Remove Combatant Body Part: 'Remove Combatant Body Part' node available in 'Combatant > Body Part' nodes. Removes body parts from a combatant.
- Schematics: Area Fork: 'Area Fork' node available in 'Game > Game' nodes. Checks if the game's current area is one of the defined areas and uses that check's 'Next' slot to continue the schematic.
- Schematics: Check Selected Data Shortcut: 'Check Selected Data Shortcut' node available in 'Combatant > Shortcut' nodes. Checks a shortcut stored in selected data. E.g. check if it's an item or ability.
- Schematics: Collect Battle Gains: 'Is Immediate Collection' setting available. The battle gains collection is used as an immediate collection.
- Schematics: Check Quest Status: 'Check Quest Status' node available in 'Game > Quest' nodes. Checks the status of a defined quest or quests stored in selected data.
- Schematics: Quest Status Fork: 'Quest Status Fork' node available in 'Game > Quest' nodes. Checks the status of a defined quest or a quest stored in selected data. The valid quest status's next node will be executed.
- Schematics: Has Quest: 'Use Selected Data' settings available. Optionally check quests stored in selected data instead of a defined quest.
- Schematics: Selected Data Choice: 'Selection Settings' available. Optionally remember the selected choice (via an int variable) or set a default selection.
- Unity UI: HUD Quest Reward List: 'HUD Quest Reward List' component available. Lists rewards of a HUD content's quest or quest task.
- Unity UI: HUD Quest Reward Text Content: 'HUD Quest Reward Text Content' component available. Shows content information of a HUD content's quest reward (e.g. coming from a 'HUD Quest Reward List Content' component).
- Unity UI: HUD Quest Text Content, HUD Quest Task Text Content, HUD Quest Task Requirement Text Content: 'Use Content Icon' setting available. Optionally use the icon of the displayed quest/task/requirement content instead of a defined icon.
- Unity UI: Context Menu: Quest: New variants available for creating quest and quest tasks with reward lists, add quest reward lists and create quest reward prefabs.
- HUD Conditions: Quest Reward: 'Quest Reward' condition type available. Checks if the HUD user is a quest or quest task with rewards.
- HUD Conditions: Quest Task Requirement: 'Quest Task Requirement' condition type available. Checks if the HUD user is a quest task with finish requirements.

Changes:
- Shortcut UI: Using shortcut UI when displaying combatants sold in shops (e.g. in quantity selections) can now access status information of the combatant (e.g. displaying status value lists and other content).

Fixes:
- Quantity Selections: Fixed a potential issue where using shortcut UI in quantity selections for buying combatants could result in an error.
- Inventory Settings: Item Box: Fixed an issue where using 'Inventory Exchange Menu' caused an error.
- Active Time Battles: Fixed an issue where finishing an action cast could lead to the action being performed at the same time as a combatant selecting actions (while using a setup that should prevent that).
- Battle Menus: Description: Fixed an issue where changing the use level of an ability didn't update the displayed description.
- Battle AI: Combatant Trigger Nodes: Fixed an issue where combatant trigger nodes where only used in grid battles.
- HUDs: Battle Action, Combatant, Turn Order: Fixed an issue where using 'Offset' or 'Relative Offset' multi-box layout didn't set the offset when closing and opening the HUD again.
- HUDs: Console: Fixed an issue where toggling a console HUD off/on allowed the UI box of the console to get focused.
- Menu Screens: Button List: Fixed an issue where opening a non-single screen menu screen from a single screen menu screen could lead to the new menu being called twice (potentially closing it again).
- Menu Screens: Equipment: Fixed an issue where hidden equipment slots could cause displaying the wrong equipment for a slot.
- Menu Screens: Equipment, Equipment (Single Slot): Fixed an issue where the description of the unequip button wasn't shown by 'Description' menu parts.
- Menu Screens: Multi Slot: Shortcut Slot: Fixed an issue where an assigned shortcut wasn't displayed on a slot if it was unavailable while keeping and showing unavailable shortcuts was enabled in 'UI > Shortcut Settings'.
- Menu Screens: Inventory Exchange: Fixed an issue where adding the 'All' type could cause an error.
- Menu Screens: Fixed a potential issue where menu screens could refresh while being closed.
- Schematics: Call Menu Screen: Fixed an issue where using 'Inventory Exchange' menus with item box didn't use the defined scene object content.
- Schematics: Select Quest: Fixed an issue where not using 'Limit Quest Type' caused an error.
- Schematics: Change Shortcut: Fixed an issue where using a shortcut from selected data could lead to unexpected functionality afterwards due to using the actual shortcut instead of a copy. Now, a copy of the shortcut is created to separate the assigned shortcut from whatever source was used.


-------------------------------------------------------------------------------------------------------------------------------------------------------
ORK Framework 3.14.1
-------------------------------------------------------------------------------------------------------------------------------------------------------

Fixes:
- Equipment: Schematics: Fixed an issue where override schematic settings per equipment level where not loaded correctly.
- Area Components: Fixed an issue where 'Auto Name' didn't automatically set the game object's name when changing the used area of the component.


-------------------------------------------------------------------------------------------------------------------------------------------------------
ORK Framework 3.14.0 ACTION PIE
-------------------------------------------------------------------------------------------------------------------------------------------------------

New:
- Child Objects: New child object settings available. Beside defining the 'Path' to the child object, you can now also find child objects by 'Name' or 'Tag'. Available everywhere child objects can be defined. Previous settings are automatically updated to 'None' or 'Path' (depending if a path was defined or not).
- HUDs: Battle Actions: 'Battle Actions' HUD type available. Lists the battle actions currently registered with the system. Can display active actions, casting actions and actions scheduled for performing (i.e. selected by combatants but not yet active or casting). Each action is displayed as an individual HUD (e.g. like individual combatants in the 'Combatant' HUD type).
- HUDs: Combatant: 'Other Player Groups' setting available when displaying player combatants. Optionally add other player groups instead of only showing the active player group. You can set up multiple player groups using a 'Group ID' to identify them.
- Menu Screens: Call Screen Key: 'Allow In-Menu Call' setting available. Optionally allow using the call key to call a menu screen while another (control blocking) menu screen is opened. Otherwise call keys can't be used during blocked controls.
- Quantity Selections, Combatant Selections, Assign Shortcut Menus: 'Use Content Icon' settings available. Optionally use the displayed content's icon in the title and/or message instead of a defined icon.
- Quantity Selections, Combatant Selections, Assign Shortcut Menus: Message: 'HUD Type' settings available. Optionally use HUD content in the message content (uses the displayed content as HUD user, e.g. an item). Can use a 'HUD' as template or a 'UI Key' setup, e.g. showing the shortcut UI of an item.
- Status Conditions: Is Target: 'Is Target' status condition check type available. Checks if a combatant is the target of an action (ability/item).
- Status Conditions: Is Selected Target: 'Is Selected Target' status condition check type available. Checks if a combatant is a selected target during the player's target selection.
- Status Conditions: Group Size: 'Include Alive' and 'Include Dead' settings available. Group size can now be limited to alive or dead members of the group. Both by default enabled (previous behaviour).
- Battle Systems: System Action Schematics: 'System Action Schematics' settings available in all battle systems and field mode settings (general settings). Optionally start schematics when an action is added to the system (after selection by the combatant), removed, starts/cancels/finishes casting or starts/stops/finishes performing.
- Abilities/Items: System Action Schematics: 'System Action Schematics' settings available for useable abilities/items (found in the battle settings of the ability/item). Optionally use additional schematics or replace the default system action schematics defined by the battle system.
- Battle Settings: 'Remove Dead Actions' setting available. A combatant's death will remove all scheduled actions of the combatant. Otherwise the actions are kept scheduled and will be removed when trying to perform them.
- Battle Texts: Special Command Content: 'Change Member Command' settings available. Define the content of the 'Changer Member' command.
- Battle Texts: Special Command Content: 'Join Battle' settings available. Define the content of the join battle action.
- Battle Texts: Special Command Content: 'Death' settings available. Define the content of the death action.
- Battle Texts, Abilities, Ability Types, Items, Item Types: Action Info Notifications: New text codes available for actions with targets (e.g. ability, item, stealing). Add information about the target of the action (name, icon, etc.) or the list of targets (as defined in 'UI > Text Display Settings').
- Battle Grid Settings: Examine: Combatant Info Dialogue: 'Keep Open' setting available when not using 'Auto Show'. Keep the combatant info dialogue open for the last displayed combatant when selecting a cell without a combatant.
- Battle Grid Settings: Examine: Combatant Info Dialogue: 'Keep Same' setting available when not using 'Auto Show'. Keep the same combatant displayed when selecting a new cell with a combatant.
- Battle Range Templates: Grid Shape: Line of Sight: 'Raycast Line of Sight' settings available. Optionally also use a raycast to check for line of sight. The raycast is used in addition to the other checks (e.g. blocked/occupied cells).
- Action Checks: 'Is Valid' setting available. Defines if the action must or mustn't have the defined type. Action checks are e.g. used in the 'Battle Actions' HUD, status effects and schematic nodes.
- Cast Times: Schematics: 'Finish Casting Schematic' setting available. Optionally use a schematic when a combatant finished casting an action.
- Inventory Settings: Inventory Schematics: 'Inventory Schematics Settings' available. Optionally start schematics when adding or removing items to/from an inventory. Define default schematics for all content and optionally override it for items, equipment, etc.
- Inventory Settings: Inventory Schematics: Item Schematics: 'Init Schematic' setting available. Define a default schematic that'll be used when an instance of an item is created. Each item can override the default schematic.
- Inventory Settings: Inventory Schematics: Equipment Schematics: 'Init Schematic', 'Equip Schematic' and 'Unequip Schematic' settings available. Define default schematics that'll be used when an instance of an equipment is created, an equipment is equipped or unequipped. Each equipment can override the default schematics.
- Currencies, Items, Equipment, Crafting Recipes, AI Behaviours, AI Rulesets: Schematics: 'Own Inventory Schematics' settings available. Optionally override the inventory schematics defined in 'Inventory > Inventory Settings'.
- Currencies, Crafting Recipes, AI Behaviours, AI Rulesets: Custom Schematics: 'Custom Schematics' settings available. Custom schematics can be used on an instance of a currency/recipe in other schematics using a 'Custom Selected Data Schematic' node.
- Items: Schematics: 'Own Init Schematic' setting available. Optionally override the default init schematic defined in 'Inventory > Inventory Settings'. Previous settings will be updated automatically.
- Equipment: Schematics: 'Own Equip Schematics' setting available. Optionally override the default init/equip/unequip schematics defined in 'Inventory > Inventory Settings'. Available for the equipment and per level. Previous settings will be updated automatically.
- Crafting Settings: Crafting Schematics: 'Crafting Schematics' settings available. Define default schematics for all crafting recipes (fail, pre-crafting, ingredients, outcome and critical outcome). Individual recipes can override the default schematics.
- Crafting Recipes: Schematics: 'Own Crafting Schematics' settings available. Optionally override the default crafting schematics defined in 'Inventory > Crafting Settings & Types'. Previous settings will be updated automatically.
- Combatant Highlights: Schematic: 'Schematic' highlight type available. Uses a schematic when highlighting and stopping the highlight of a combatant.
- Grid Cell Highlights: Schematic: 'Schematic' highlight type available. Uses a schematic when highlighting and stopping the highlight of cells.
- Schematics: Select Turn Order: 'Select Turn Order' node available in 'Value > Selected Data' nodes. Uses a turn based battle's turn order (combatants) to change selected data. The combatants are stored into selected data in the order of the current turn order. Please note that this is only available in running 'Turn Based' battles.
- Schematics: Select Battle Actions: 'Select Battle Actions' node available in 'Value > Selected Data' nodes. Uses battle actions currently registered with the system to change selected data.
- Schematics: Filter Selected Battle Actions: 'Filter Selected Battle Actions' node available in 'Value > Selected Data' nodes. Filters battle actions stored in selected data and only keeps actions matching the defined conditions. E.g. filter for specific actions, check the action's user or targets, etc.
- Schematics: Replace Battle Action: 'Replace Battle Action' node available in 'Battle > Action' nodes. Replaces scheduled battle actions with a new battle action. This can only be used to replaces battle actions that have been added to the system but are not yet casting or performing.
- Schematics: Remove Battle Actions: 'Remove Battle Actions' node available in 'Battle > Action' nodes. Removes battle actions that are scheduled with the system but not yet performing or casting. Removes either all actions, the actions of a defined combatant or actions stored in selected data.
- Schematics: Select Battle Action User: 'Select Battle Action User' node available in 'Battle > Action' nodes. Uses the user(s) of battle actions (stored in selected data) to change selected data.
- Schematics: Select Battle Action Target: 'Select Battle Action Target' node available in 'Battle > Action' nodes. Uses the target(s) of battle actions (stored in selected data) to change selected data.
- Schematics: Is Targeting Combatant: 'Is Targeting Combatant' node available in 'Battle > Action' nodes. Checks if a combatant is targeting another combatant. Checks battle actions registered with the system (scheduled, casting and active) for the involved user and target.
- Schematics: Is Action Countered: 'Is Action Countered' node available in 'Battle > Action Outcome' nodes. Checks if the schematic's battle action caused a counter attack and optionally stores the countering combatants into selected data.
- Schematics: Change Grid Move State: 'Change Grid Move State' node available in 'Battle > Grid' nodes. Changes the grid move state of a combatant, making grid movement available or unavailable. If used by a grid move action's schematic, prevents the grid move from using 'Use Only Once' setup.
- Schematics: Update Battle Spots: 'Update Battle Spots' node available in 'Battle > Spawn' nodes. Update the positions of the battle spots of combatants participating in battle. Each combatant's battle spot will be placed according to the battle spot setup in 'Battles > Battle Spots'. E.g. use this node after conditions changed for conditional battle spots.
- Schematics: Sort Selected Combatants: 'None', 'Invert' and 'Content' sort types available. Either keeps the current sorting, inverts it or sorts by content information (e.g. name, type name, etc.).
- Schematics: Consume Costs, No Use Costs, No Time Consume, Damage Multiplier, Change Action Targets, End Battle Action, Cancel Death, End Turn, Learn Action Ability, Change Action Cost, Check Action Cost: 'Use Selected Data' settings available. Optionally use battle actions stored in selected data instead of the battle action the schematic is animating.
- Schematics: Place On Grid: 'Use Spawn Rotation' setting available. Optionally use the spawn rotation of the cell when placing combatants.
- Schematics: Add Temporary Cell Event: 'Remove Schematic' setting available. Optionally use a schematic when a temporary cell event is removed from a cell. The cell is used as 'Machine Object' and 'Starting Object' of the schematic. E.g. use this to clean up spawned prefabs or cell highlights.
- Damage Dealer (Schematic) Component: 'Damage Dealer (Schematic)' component available. A new damage zone variant that only starts a schematic and doesn't require to be placed on a combatant. E.g. add it to environmental objects that can be destroyed by actions (destruction must be handled in the started schematic).
- Auto Machines: Notify Start: 'Combatant Status Condition' type available. Start the machine when defined status conditions of a combatant are valid.
- Unity UI: ORK HUD Status Text Content Component: Action Information: 'Action Information' status type available. Displays information about a battle action (e.g. coming from a 'Battle Actions' HUD type). Can show content information of the action, user and target of the action.
- Unity UI: HUD Combatant Action List Component: 'HUD Combatant Action List' component available. Lists battle actions with the HUD user (combatant) used as 'User' or 'Target'.
- Unity UI: HUD Action Target List Component: 'HUD Action Target List' component available. Lists the targets of a battle action and spawns a prefab for each target (using the target as a content provider's user on that prefab). 
- Unity UI: HUD Action Combatant (Content Provider) Component: 'HUD Action Combatant' content provider component available. Uses the user or target of a battle action as content. Requires a battle action as the content provider's user (e.g. coming from a 'Battle Actions' HUD).
- Scripting: Damage Dealer Components: 'IsDamageActive' property available to check if a damage dealer is active via scripting.

Changes:
- Crafting Recipes: The individual schematics settings (fail, pre-crafting, ingredients, outcome and critical outcome) are now bundled under the recipe's 'Item Settings > Schematic Settings' instead of split up into the different sub-settings.
- Schematics: Player Group Nodes: The 'Group ID' fields now use float value selections instead of strictly defined values.
- Schematics: The 'Select Battle Action' node has been renamed to 'Select Combatant Action'.

Fixes:
- Abilities, Items: Fixed an issue where a selecting a random target (e.g. after the original target was killed) included combatants not participating in battle.
- Grid Battles: Examine: Combatant Info Box: Fixed an issue where using 'Use User' and '2nd Call Closes' with 'Same Combatant' didn't close the UI box on 2nd call.


-------------------------------------------------------------------------------------------------------------------------------------------------------
ORK Framework 3.13.0
-------------------------------------------------------------------------------------------------------------------------------------------------------

New:
- Game Controls: Player Input ID Settings: 'Player Input ID Settings' available in the ORK control settings. Optionally automatically add interaction controllers or player controls to all player group members that have an input ID set (e.g. via 'Change Combatant Input ID' node in schematics).
- Status Values: Flying Texts: 'Use Real Change' setting available for 'Refresh', 'Critical Refresh', 'Damage' and 'Critical Damage' flying texts. Optionally use the real change value instead of the value used to change the status value. E.g. refreshing at maximum value or damaging at minimum value will result in 0 change value.
- Status Values: Flying Texts: 'Ignore 0 Refresh' setting available for 'Refresh' and 'Critical Refresh' flying texts. Optionally ignore a refresh of 0 (i.e. no real refresh), flying texts and HUD flash won't be performed.
- Status Values: 'Use Changed State' setting available. Enables using the changed state and last change values for a status value. By default disabled.
- Status Conditions: Shortcut Slot: 'Shortcut Slot' status condition check type available. Checks if a combatant's shortcut slot is assigned with a defined shortcut (e.g. ability, item, etc.).
- Status Conditions: Shortcut Slot (Ability Type): 'Shortcut Slot (Ability Type)' status condition check type available. Checks if a combatant's shortcut slot is assigned with an ability of a defined ability type.
- Status Conditions: Shortcut Slot (Item Type): 'Shortcut Slot (Item Type)' status condition check type available. Checks if a combatant's shortcut slot is assigned with an inventory shortcut (e.g. item or equipment) of a defined item type.
- Status Conditions: Input ID: 'Input ID' status condition check type available. Checks if a combatant's input ID is or isn't set.
- Status Effects: Information Overrides: 'Time Formatting' settings available. Optionally override the default time formatting settings ('UI > Text Display Settings'). Used to display the time duration of the status effect.
- Abilities, Items: Information Overrides: 'Time Formatting' settings available. Optionally override the default time formatting settings ('UI > Text Display Settings'). Used to display the reuse time of the ability/item.
- Equipment Slots: 'Add Inventory Space' setting available. Equipment equipped on an equipment slot can optionally add to the combatant's inventory space.
- Classes: Status Settings: Random Status Value Bonuses: 'Random Status Value Bonuses' settings available. Classes can add random bonuses to status values of a combatant while being their single-class or equipped on a class slot. The random bonuses are calculated when the class is first initializied (e.g. added to a combatant).
- Shops: Schematic Settings: 'Schematic Settings' available. Optionally use schematics when opening/closing the shop or buying/selling in the shop.
- Battle Spots: Conditional Battle Spots: 'Conditional Battle Spots' available for player, ally and enemy spots. Add different battle spots based on conditions, e.g. group size. Each combatant that's placed on a battle spot will be checked individually, allowing you to combine the default spots with any of the conditional spots. The placed combatant will use the spot matching it's group member index.
- Battle Grid Settings: Combatant Placement: 'Cancel Remove Placed' setting available. Placed combatants can be removed by canceling in the combatant selection. When not using a combatant selection, canceling in the cell selection during placement will remove the previously placed combatant (requires 'Allow Cancel Cell' enabled).
- HUDs: Combatant, Combatant Object: Display Conditions: Limited Time Display: 'Level Up' and 'Class Level Up' notify by types available. Get notified by a combatant's level up or class level up to display the HUD.
- Text Display Settings: Time Formatting: 'Time Formatting' settings available. Define how time/duration values will be formatted. Can use time value checks to define different formats, e.g. displaying seconds for <60s and otherwise mintues. Can be overridden by individual settings displaying time/durations.
- Text Display Settings: Number Formatting: 'Conditional Format' settings available for all number formats (and overrides in individual settings, e.g. abilities). Optionally use different formats based on the displayed value, e.g. displaying values greater equal 1000 as 1k, etc.
- Animations: Mecanim: Auto Rotation Parameters: 'In Camera Direction' setting available. Optionally use the rotation based on the direction of the camera. E.g. useful for 2.5D setups (i.e. mixing 2D sprites with 3D environments).
- Animations: Mecanim: Auto Rotation Parameters: 'Rotation Offset' setting available. Add an offset to the rotation of the axis.
- Animations: Mecanim: Auto Rotation Parameters: 'Negate Rotation' setting available. Optionally negate the rotation value (i.e. using -rotation, e.g. -90 instead of 90) to reverse the rotation value.
- Game Options: Remember Selections: 'Remember Selections' option available. The player can define if battle menus, menu screens and combatant selections remember their selections. Only used if they individually enable remembering selections in their settings.
- Save Game Settings: PlayerPrefs Game Options: 'Remember Selections' setting available. The 'Remember Selections' option will also be stored in the PlayerPrefs (outside of save games).
- Save Game Settings: PlayerPrefs Game Options: 'Random Battle Chance' setting available. The 'Random Battle Chance' option will also be stored in the PlayerPrefs (outside of save games).
- Menu Screens: Group: 'Remove Sort Change' setting available in '1st Group Box Settings'. Optionally change the group sort position of a combatant when removing it from the battle group or battle reserve, sorting the combatant in the front or back of the group.
- Battle Menus: Options: Attack, Ability, Item, Class Ability, Change Member, Command, Shortcut Slot: 'Custom Target Menu' settings available. Optionally override the default 'Target Menu' settings ('Battles > Target Settings'), the 'Target UI Box' and the 'Combatant Choice Layout' of the battle menu. Overrides can be made for each battle menu option individually, e.g. having the 'Attack' command not use a target menu or abilities use a different target menu.
- Schematics: Force Player Controlled: 'Force Player Controlled' node available in 'Group > Group' nodes. Changes if a non-player controlled group is player controlled or not. This allows giving battle commands to non-player controlled groups when forcing player controls on them.
- Schematics: Is Force Player Controlled: 'Is Force Player Controlled' node available in 'Group > Group' nodes. Checks if a combatant's group has forced player control.
- Schematics: Use Battle Action: 'Outside Battle Order' selection available in 'Add Action' setting. Adds the action outside the battle order and can be used at all times. The action will not consume APT/timebar or change the action state of the user.
- Schematics: Select Battle Action: 'Select Battle Action' node available in 'Battle > Action' nodes. Use the current or next battle action of a combatant as selected data.
- Schematics: Stop Battle Action: 'Selected Data' settings available. Optionally stop actions stored in selected data.
- Schematics: Is Combatant Input ID Set: 'Is Combatant Input ID Set' node available in 'Combatant > Combatant' nodes. Checks if the input ID of a combatant or a combatant's group is set.
- Schematics: Add Player Control: 'Add Player Control' node available in 'Game Object > Component' nodes. Adds the player controls that are set up in 'Base/Control > Game Controls' to a game object and registers the components with the system.
- Schematics: Remove Player Control: 'Remove Player Control' node available in 'Game Object > Component' nodes. Removes the player controls that are set up in 'Base/Control > Game Controls' from a game object and unregisters the components from the system.
- Content Layouts: '<quantitymax>' text code available. Shows the maximum allowed quantity of an item, equipment or currency (or 0 if no limit is set).
- Unity UI: HUD Status Value List Component: 'Show Status Bonus' settings available. Optionally show status value bonuses instead of a combatant's status value. E.g. use this to list status value bonuses of equipment, passive abilities or classes.
- Unity UI: HUD Attack Modifier List Component: 'Show Status Bonus' settings available. Optionally show attack modifier bonuses instead of a combatant's attack modifiers. E.g. use this to list attack modifier attribute bonuses of equipment, passive abilities or classes.
- Unity UI: HUD Attack Modifier Attribute List Component: 'Only With Bonus' setting available. Optionally only list the attack modifier attributes that have a status bonus. Only used when displaying attack modifiers from status bonuses.
- Unity UI: HUD Defence Modifier List Component: 'Show Status Bonus' settings available. Optionally show defence modifier bonuses instead of a combatant's defence modifiers. E.g. use this to list defence modifier attribute bonuses of equipment, passive abilities or classes.
- Unity UI: HUD Defence Modifier Attribute List Component: 'Only With Bonus' setting available. Optionally only list the defence modifier attributes that have a status bonus. Only used when displaying defence modifiers from status bonuses.
- Unity UI: HUD Defence Modifier ID List Component: 'Show Status Bonus' setting available. Optionally show defence modifier ID bonuses instead of a combatant's defence modifier IDs. E.g. use this to list defence modifier ID changes of equipment, passive abilities or classes.
- Untiy UI: HUD Status Effect List Component: 'Show Status Bonus' settings available. Optionally show status effects added or removed by status bonuses instead of a combatant's status effects. E.g. use this to list effect immunities or auto effects from status bonuses of equipment, passive abilities or classes.
- Untiy UI: HUD Status Effect List Component: 'Effect Origin' settings available. Defines if status preview information will be used when listing status effects of a combatant. Can show the current effects, current effects with preview changes (adding/removing effects), only the effects added by the preview and only the effects removed by the preview.
- Unity UI: HUD Ability List Component: The 'HUD Ability List' component now also lists abilities of an equipment. The equipment must be the content provider's HUD content/user, either directly or equipped on an equipment slot.
- Unity UI: ORK HUD Status Text Content Component: Bonus Status Value: 'Bonus Status Value' status type available. Displays a status value bonus (e.g. from an equipment, class or passive ability). Requires a status value bonus to display instead of a regular tatus value (e.g. coming from a 'HUD Status Value List' component using 'Show Status Bonus').
- Unity UI: ORK HUD Status Text Content Component: Bonus Attack Modifier Attribute: 'Bonus Attack Modifier Attribute' status type available. Displays an attack modifier attribute bonus (e.g. from an equipment, class or passive ability). Requires an attack modifier attribute bonus to display instead of a regular attack modifier attribute (e.g. coming from a 'HUD Attack Modifier List' component using 'Show Status Bonus').
- Unity UI: ORK HUD Status Text Content Component: Bonus Defence Modifier Attribute: 'Bonus Defence Modifier Attribute' status type available. Displays a defence modifier attribute bonus (e.g. from an equipment, class or passive ability). Requires a defence modifier attribute bonus to display instead of a regular defence modifier attribute (e.g. coming from a 'HUD Defence Modifier List' component using 'Show Status Bonus').
- Unity UI: ORK HUD Status Text Content Component: Bonus Defence Modifier ID: 'Bonus Defence Modifier ID' status type available. Displays a defence modifier ID bonus change (e.g. from an equipment, class or passive ability). Requires a defence modifier ID bonus to display instead of a regular defence modifier UD (e.g. coming from a 'HUD Defence Modifier ID List' component using 'Show Status Bonus').
- Unity UI: ORK HUD Status Text Content Component: Status Value: '<lastchangecombinedpositive>' and '<lastchangecombinednegative>' text codes available to show the last combined positive and negative change value of the status value.
- Unity UI: ORK HUD Status Text Content Component: Status Effect: '<durationF>' and '<durationmaxF>' text codes available to show the duration and maximum duration using the time formatting.
- Unity UI: ORK HUD Status Text Content Component: Ability, Item: '<reuseF>' and '<reusemaxF>' text codes available to show the reuse time and maximum reuse time using the time formatting.
- Unity UI: ORK HUD Status Text Content Component: Action Time: '<timeF>' and '<timemaxF>' text codes available to show the action time and maximum action time using the time formatting.
- Unity UI: ORK HUD Status Text Content Component: Cast Time: '<timeF>' and '<timemaxF>' text codes available to show the cast time and maximum cast time using the time formatting.
- Unity UI: ORK HUD Status Text Content Component: Delay Time: '<timeF>' and '<timemaxF>' text codes available to show the delay time and maximum delay time using the time formatting.
- Unity UI: ORK HUD Status Text Content Component: Reuse Time: '<timeF>' and '<timemaxF>' text codes available to show the reuse time and maximum reuse time using the time formatting.
- Unity UI: ORK HUD Status Text Content Component: Ability, Item, Shortcut: '<quantitymax>' text code available. Shows the maximum allowed quantity of an item, equipment or currency (or 0 if no limit is set).
- Unity UI: ORK HUD Text Content Component: '<effectdurationF>' text code available to show the duration of a status effect using the time formatting.
- Unity UI: ORK HUD Text Content Component: '<reuseF>' and '<reusemaxF>' text codes available to show the reuse time and maximum reuse time of an ability/item using the time formatting.
- Unity UI: ORK HUD Text Content Component: '<quantitymax>' text code available. Shows the maximum allowed quantity of an item, equipment or currency (or 0 if no limit is set).
- Unity UI: HUD Shortcut (Content Provider) Component: 'Use Active Shortcut' setting available. Use the currently active shortcut of the combatant as HUD content. A shortcut is active during it's target selection and while it's being used.
- Unity UI: HUD Combatant Action (Content Provider) Component: 'Next' action origin available. Uses the next action that's scheduled to be use (added as 'Next' action or queued by the battle AI) by the combatant as HUD content.
- Unity UI: HUD Combatant Target (Content Provider) Component: 'Selected' target origin available. Uses the currently selected targets of the combatant's target selection as HUD content (only used when the player selects targets).
- Unity UI: HUD Content Providers: Status Value, Attack Modifier, Attack Modifier Attribute, Defence Modifier, Defence Modifier Attribute, Defence Modifier ID: 'Show Status Bonus' settings available. Optionally use the status information coming from a content with status bonuses instead of a combatant.
- Unity UI: Context Menu: Status Value: New variants available for 'Bonus Status Value' setups and 'Status Value' setups without value bars.
- Unity UI: Context Menu: Attack Modifier: New variants available for 'Bonus Attack Modifier Attribute' setups.
- Unity UI: Context Menu: Defence Modifier: New variants available for 'Bonus Defence Modifier Attribute' setups and 'Bonus Defence Modifier ID' setups.
- HUD Conditions: Status Bonus: 'Status Bonus' condition type available. Checks if the HUD user is a content with displayable status bonuses (e.g. equipment, class or passive ability).
- Line Target Raycast Cursor Component: 'Line' target raycast component available. Implements the 'ITargerRaycastCursorPrefab' interface and sets positions of a 'LineRenderer' component. The start (index 0) position is set to the user, the end (last position) is set to the raycast position, optionally adjusting positions inbetween.
- Line Target Selection Cursor Component: 'Line' target selection component available. Implements the 'ITargetSelectionCursorPrefab' interface and sets positions of a 'LineRenderer' component. The start (index 0) position is set to the user, the end (last position) is set to the target's position, optionally adjusting positions inbetween.
- Schematic Target Raycast Cursor Component: 'Schematic' target raycast component available. Implements the 'ITargerRaycastCursorPrefab' interface and uses a schematic when starting or stopping the target raycast. The user (combatant) is available as 'Machine Object', the ability/item as selected data via the data key 'action'.
- Schematic Target Selection Cursor Component: 'Schematic' target selection component available. Implements the 'ITargetSelectionCursorPrefab' interface and uses a schematic when starting or stopping the target selection. The user (combatant) is available as 'Machine Object', the target as 'Starting Object' and the ability/item as selected data via the data key 'action'.
- Schematic Combatant Highlight Cursor Component: 'Schematic' combatant highlight cursor component available. Implements the 'ICombatantHighlightCursorPrefab' interface and uses a schematic when starting or stopping the highlight. The highlighted combatant is available as 'Machine Object'.

Changes:
- Abilities, Items, Combatants: Battle Animations: The 'Set Prefabs' and 'Set Audio Clips' settings have been removed. Instead, just add prefabs/audio clips to override them in the used schematic. Also, the settings are now grouped under the 'Resource Overrides' foldout.
- Player Controls: The 'Button' and 'Top Down 2D' player controls now use the input ID of the combatant they're added to for their input keys. E.g. used to allow local multiplayer controls.
- Editor: Game Controls: The 'ORK Control Settings' in 'Base/Control > Game Controls' have been rearranged. The player/camera controls are now listed first, the 'Group Member Keys' and 'Ability Level Keys' are now grouped together under 'Other Controls'.
- Status Values: Changed State, Last Change Values: A status value will only use the changed state and last change values if enabled in it's settings. By default disabled.
- Status Values: Last Change Values: Status values now also keep track of the last combined positive and negative change values.
- Random Battle Components: Random battles can only start when not changing scenes and the player control isn't blocked.
- Text Display Settings: Number Formatting: The 'Format' setting is now split into 'Format Text' and 'Value Format' settings. 'Value Format' is the previous 'Format' setting, defining the format of the value (e.g. '0.0'). 'Format Text' will be used to display the value and allows adding additional information (the formatted value is added via text code '<value>').
- Schematics: Stop Battle Action: The 'All' toggle setting has been replaced by the 'Stop Actions' dropdown setting (offers 'All', 'Combatant' and 'Selected Data' selections).
- Editor: Combatants: Start Equipment: The start equipment setup is no longer limited by the available equipment setup of the combatant. However, in-game the start equipment is still limited by the available equipment of the combatant.

Fixes:
- Battle Range Templates: Grid Shape: Mask: Fixed an issue where using masks in local space of the user couldn't target combatants left or right of the user, even though the range was displayed correctly.
- Battle Menus: Fixed an issue where hiding the battle menu (e.g. starting grid examine via shortcut) and showing it again showed an empty battle menu.
- Menu Screens: Fixed issues with individual UI boxes being updated too often or not at all.
- Combatant Spawners: Fixed an issue with spawn positions in 2D environments.
- Status Previews: Fixed an issue where target change previews where not calculated when toggling the target type (e.g. not calculated for allies on a toggled enemy targeting ability).
- Status Previews: Fixed an issue where combined values could cause wrong preview values when used as maximum status value for a 'Consumable' status value.
- Schematics: Show Battle Grid: Fixed an issue where a previously hidden grid isn't shown again.
- Notifications: Fixed an issue where blocked notifications would still play audio clips and create disabled UI boxes.


-------------------------------------------------------------------------------------------------------------------------------------------------------
ORK Framework 3.12.0
-------------------------------------------------------------------------------------------------------------------------------------------------------

New:
- Auto Machines: Notify Start: 'Battle Started' type available. Start the machine when a defined or any 'Battle' component's battle started (before battle start schematic).
- Auto Machines: Notify Start: 'Battle Ended' type available. Start the machine when a defined or any 'Battle'component's battle was finished (after battle end schematic).
- Auto Machines: Notify Start: 'Combatant Spawner' type available. Start the machine when a defined 'Combatant Spawner' component spawns or despawns combatants, or reports killed combatants.
- Float Values: Status Effect Count: 'Status Effect Count' value type available. The number of a defined status effect applied to a combatant is used as value.
- Float Values: Status Effect Count (All): 'Status Effect Count (All)' value type available. The number of all status effects applied to a combatant is used as value.
- Float Values: Status Effect Count (Type): 'Status Effect Count (Type)' value type available. The number of status effects of a defined status effect type applied to a combatant is used as value.
- Content Layouts: 'Use Content Variables' setting avaialble. Optionally replace variable text codes with variables from the displayed content instead of global variables. If the content doesn't have variables, the text codes will be replaced with default values (e.g. int/float '0', bool 'false', etc.).
- Status Values: Status values now remain in a changed state for a short duration after a change was done to them. This (and the last change values) is only affected by active changes to the status values (e.g. ability/item target changes, status effects, schematics, etc.) and doesn't react to changes due to status resets or combined value changes.
- Status Values: Status values now keep track of the last change and last combined change values. The combined change value will add all changes that where made within a defined timeout between changes (defined in 'UI > Text Display Settings'). The last change values are available for display and use similar to other status value information.
- Status Conditions: Attack Modifier Trait: 'Attack Modifier Trait' status condition check type available. Checks if a defined attack modifier attribute is recognized as a trait (strength, weakness, etc.) of a combatant.
- Status Conditions: Defence Modifier Trait: 'Defence Modifier Trait' status condition check type available. Checks if a defined defence modifier attribute is recognized as a trait (strength, weakness, etc.) of a combatant.
- Status Conditions: Status Value Changed State: 'Status Value Changed State' status condition check type available. Checks if a defined status value is or isn't in changed state.
- Control Maps: Action: 'Inventory Container Slot' action type available. Uses an item or equipment stored in a defined inventory container slot.
- AI Rulesets: Rules: Action: 'Inventory Container Slot' action type available. Uses an item or equipment stored in a defined inventory container slot.
- Action Combos: Required Action: 'Inventory Container Slot' action type available. Checks for an item or equipment stored in a defined inventory container slot.
- Action Combos: Replacement Action: 'Inventory Container Slot' action type available. Uses an item or equipment stored in a defined inventory container slot.
- Status Effects: End Action: 'Inventory Container Slot' action type available. Uses an item or equipment stored in a defined inventory container slot.
- Item Types: Inventory Settings: 'Inventory Settings' available. An item type can now override the default inventory settings (stacking, space, quantity limit and stack limit) for all items and equipment using the item type (primary type only). Individual items and equipment can override the settings.
- Crafting Recipes: Outcome, Critical Outcome: 'Get Random' settings available. Optionally get a random item from the defined items instead of all of them. Also allows using a chance-based selection.
- Crafting Recipes: Outcome, Critical Outcome: 'Use Loot' settings available. Optionally use loot from loot tables as outcome or critical outcome of the crafting recipe. Still uses the defined outcome items.
- Crafting Recipe Layouts: 'Critical Outcome' settings available. Optionally display the critical outcome of a crafting recipe in the layout using the '<criticaloutcome>' text code.
- Crafting Recipe Layouts: Chance text codes available for ingredients and outcome display. Display the chance an item is consumed (ingredients) or created (outcome, critical outcome) using the new chance text codes ('<chance>' for '0', '<chance1>' for '0.0', '<chance2>' for '0.00').
- Menu Screens: Group: 'Empty Content Layout' is now available in both '1st Group Box Settings' and '2nd Group Box Settings'. Previous data is updated to load for both group boxes.
- Text Display Settings: Status Change Display: 'Status Value Changed State' settings available. Define how long status values remain in changed state after a change was made on them. Also has settings for handling the last changes and last combined changes of status values.
- Editor: Foldouts: Many foldout titles of array elements display additional information about their settings when using 'Extended Information' in the editor settings.
- Schematics: Reset Ability: 'Reset Ability' node available in 'Combatant > Ability' nodes. A combatant forgets and relearns a defined ability or abilities stored in selected data.
- Schematics: Use Battle Action: 'Inventory Container Slot' action type available. Uses an item or equipment stored in a defined inventory container slot when using the control key.
- Unity UI: Context Menu: New variants available for 'Slider' value bars.
- Unity UI: ORK HUD Status Text Content Component: Status Value: '<lastchange>' and '<lastchangecombined>' text codes available to show the last change value and last combined change value of the status value.
- HUD Conditions: Status Value Changed State: 'Status Value Changed State' condition type available. Checks if the HUD user is a status value that is or isn't in changed state (requires a status value).
- Scripting: Battle Components: 'NotifyBattleStarted' and 'NotifyBattleFinished' event handlers available. Register a function (no parameters) to get notified when the component's battle starts or finishes.

Changes:
- Target Settings: Individual/Group Target Settings: The 'Target Cursor' settings have been replaced by 'Target Highlight' settings. Now supports prefab, color fade and HUD color fade target highlighting, as well as cursor prefab components (like other target highlights). Previous settings are updated automatically.
- Turn Based Battles, Phase Battles: 'Auto End Turn' is enabled by default in new projects and newly added battle systems.
- Interactions: Object Turn Settings: Using a 2D setup (i.e. 'Default Horizontal Plane' set to 'XY' in 'Game > Game Settings') will now rotate the game objects on their Z-axis to face the other game object's position.
- Random Battle Area Components: The distance checks are now using the actually moved distance instead of distance to the last check/battle position. E.g. moving around a spot can now also trigger battles.

Fixes:
- Abilities: Fixed an issue where an ability's displayed level could exceed the max level when learning it. The ability's actual level was still limited to the max level.
- Grid Battles: Examine: Fixed an issue where a combatant info box opened by a call key wasn't closed or updated when changing the selected cell.
- Grid Battles: Examine: Fixed an issue where the cell highlights where not removed when canceling grid examination.
- Active Time Battles: Fixed an issue where using 'Action Blocks Choosing' didn't block the next combatant from choosing when the current combatant selected an action (due to the action not being active immediately).
- Target Selections: Fixed an issue where using 'Cursor Over Selection' (without 'Cursor Move Only') didn't automatically select the target below the cursor when switching between target selection for actions (e.g. using control maps to change between actions).
- Menu Screens: Group: Sort: Fixed an issue where the highlight of the selected combatant isn't removed when canceling.
- Schematics: Join Group: Fixed an issue where the 'Store in Selected Data' settings where not available when using 'Use Game Object'.
- Unity UI: ORK HUD Text Content, ORK HUD Status Text Content: Fixed an issue where using variable text codes could cause to errors.


-------------------------------------------------------------------------------------------------------------------------------------------------------
ORK Framework 3.11.1
-------------------------------------------------------------------------------------------------------------------------------------------------------

Changes:
- Schematics: End Phase: The 'End Phase' node can now also be used when the schematic is not started by a battle action (but is still limited to running 'Phase' battles). It's recommended to only use it in a battle action's schematics or a combatant's turn start/end schematics.

Fixes:
- Unity UI: ORK HUD Status Text Content: 'Actions Per Turn' and 'Timebar' status types where not updated correctly.
- Unity UI: ORK HUD Value Bar Content, ORK HUD ICon Bar Content: 'Actions Per Turn' and 'Timebar' value types where not updated correctly.


-------------------------------------------------------------------------------------------------------------------------------------------------------
ORK Framework 3.11.0
-------------------------------------------------------------------------------------------------------------------------------------------------------

New:
- Combatants: Input IDs: Combatants/groups now support using input IDs. Input IDs are used to define which inputs are used in an input key. The combatant/group's input ID is e.g. used in menu screens, battle menus or schematics animating an action. A combatant/group's input ID can be changed using schematics and defaults to the global input ID. This can e.g. be used to create local multiplayer.
- Formulas, Battle AIs: Select Ability: 'Use Sub-Types' setting available when limiting ability type. Uses sub-types of the defined ability type. By default enabled (previous behaviour).
- Status Conditions: Is Passive: 'Is Passive' status condition check type available. Checks if a combatant is active or passive. Passive combatants are not part of the battle order, i.e. they don't use any actions but can still be targeted.
- Item Types: Equipment Settings: 'Animation Settings' available. Item types can now override a combatant's animations when wearing an equipment of the item type (primary item type only). The equipment can override the type's animations. This allows an easier setup for equipment animation overrides, as you can now override e.g. all sword animations via their sword item type.
- Items: Use Count: 'Use Count' settings available in the item's 'User Settings > Use Cost' settings. Optionally allow using an item a defined number of times (like ability use count). If the item is consumed, it'll only be consumed after the use count reached 0. Reducing an item's quantity will reset the use count to it's maximum (i.e. item with less use count is removed).
- Abilities, Items: Target Selection Settings: 'Target Type Toggle' setting available for 'Enemy' and 'Ally' target types (and not using 'None' target range). Allows switching between enemy and ally targets during target selection using the 'Target Type Key' defined in 'Battles > Target Settings'.
- Battle Grid Settings: Move Command: Pathfinding Settings: 'Prioritize Direction' setting available. The path will prioritize the direction of the selected/target cell. Please note that this can have a heavy impact on performance due to the move range being created for multiple directions instead of only once.
- Target Settings: Target Change Keys: 'Target Type Key' settings available. Defines the input key that's used to toggle between 'Enemy' and 'Ally' target types of abilities and items during target selection. Abilities/items that have 'Target Type Toggle' enabled allow switching between enemy and ally targets.
- Target Settings: Target Change Keys: Target Range Key: 'Reset Target Range Toggle' setting available. Optionally reset the target range after target selection ended (selecting a target or canceling).
- Menu Screens: Crafting List: 'Exact Order' creation type available. The items in the crafting list must match the crafting recipe's ingredients in the same order the ingredients are defined.
- Battle Menus: Title Settings: 'Show Sub-Menu Title' settings available. Optionally show a different title for sub-menus of the battle menu. This title can use additional text codes to show content information of the previously selected menu item. E.g. use this to show the selected ability type or action in the next menu.
- Research Trees: Research Items: 'Keep Instances' setting available when a 'Cancelable' research item refunds costs. Optionally keeps the instance of the consumed item cost for refunding instead of creating a new instance of the item. Use this option to get your original items back in case you use item/equipment variables or other customizations on the consumed items.
- Text Display Settings: Use Cost Display: Use Count Text: The use count is now also displayed for items. Items using use count will list the use count in their use cost text.
- Text Display Settings: Status Change Display: Apply Status Effects: Hit chance text codes avaialble for apply and critical apply status effect texts. Display the hit chance of the status effect change using text codes. If the status effect doesn't use a hit chance, the maximum chance will be displayed.
- Shortcut Settings: 'Only Same Item/Equipment/Currency Instance' settings available (separate settings). Only the same, matching instance of an item, equipment or currency will be kept in shortcut slots. If disabled, a matching item/equipment/currency will be selected from the inventory instead. By default disabled (previous behaviour). E.g. use this to remove shortcuts once an item stack has been completely used.
- Schematics: Change Combatant Input ID: 'Change Combatant Input ID' node available in 'Combatant > Combatant' nodes. Changes the input ID of a combatant or a combatant's group. Input IDs are used to define which inputs are used in an input key. The combatant/group's input ID is e.g. used in menu screens and battle menus the combatant is the user of, or schematics animating actions of a combatant.
- Schematics: Check Combatant Input ID: 'Check Combatant Input ID' node available in 'Combatant > Combatant' nodes. Checks the input ID of a combatant or a combatant's group.
- Schematics: Preload Grid Highlights: 'Preload Grid Highlights' node available in 'Battle > Grid Highlight' nodes. Preloads grid cell highlights on a battle grid. This'll spawn prefab highlights (disabled) on each cell (depending on the used grid highlights).
- Schematics: Change Combatant Passive State: 'Change Combatant Passive State' node available in 'Combatant > Combatant' nodes. Changes a combatant's passive state. Passive combatants are not part of the battle order, i.e. they don't use any actions but can still be targeted.
- Schematics: Set Combatant Game Object: 'Set Combatant Game Object' node available in 'Combatant > Spawn' nodes. Sets a combatant's game object to a defined game object.
- Schematics: Sort Selected Shortcuts: 'Sort Selected Shortcuts' node available in 'Value > Selected Data' nodes. Sort shortcuts stored in selected data by defined metrics, e.g. by name, ID or type ID. Shortcuts are things like abilities, items, equipment and other inventory content.
- Schematics: Change Item Use Count: 'Change Item Use Count' node available in 'Inventory > Inventory' nodes. Changes or resets the use count of items of a combatant.
- Schematics: Check Item Use Count: 'Check Item Use Count' node available in 'Inventory > Inventory' nodes. Checks the use count of items stored in selected data.
- Unity UI: ORK HUD Status Text Content: Use Count: 'Use Count' status type avaialble. Displays the use count of an ability or item. Only displayed when the ability/item uses use count.
- Unity UI: ORK HUD Status Text Content: Item: New text codes available. Display the use count ('<usecount>') and maximum use count ('<usecountmax>') of an item using text codes.
- Unity UI: HUD Menu Shop User Content Provider Component: 'HUD Menu Shop User Content Provider' component available. Uses the user of a currently open menu screen or shop as content. Can fall back to use the player if no menu/shop is open.
- HUD Conditions: Item Use Count: 'Item Use Count' condition type available. Checks if the HUD user is an item with use count.
- HUD Conditions: Shop Content: 'Shop Content' condition type available. Checks if the HUD user is something listed in a shop.
- Scripting: Combatants: Event Handlers: Most event handlers of a combatant are now accessed via the 'Events' property of the combatant instance instead of spread out over multiple properties. The only exceptions are for event handlers of individual status features, e.g. per status value.

Changes:
- Turn Based Battles, Phase Battles: If 'Auto End Turn' is disabled, the combatant's turn no longer ends when no actions per turn are left.
- Target Selection: The 'Target Range Toggle' state of abilities and items is now handled globally instead of per ability/item instance.
- Unity UI: ORK HUD Text Content: The use count text codes ('<usecount>' and '<usecountmax>') now also display use count of displayed items.
- Unity UI: ORK HUD Status Text Content: The 'Item' and 'Ability' status type will now show shop prices when displayed for an item sold in a shop (e.g. in tooltip HUDs).
- Unity UI: UI Research Tree Components: Generating the trees now consider any condition for 'Research Item' checks. Previously, only 'Researched' and 'Completed' where taken into account.

Fixes:
- Status Values: Change Schematics: Fixed an issue where using multiple 'From X' settings not set to 'Ignore' blocked execution.
- Classes: Fixed an issue where changing the single-class of a combatant and forgetting learned abilities didn't remove abilities added to the class by research trees.
- Turn Based Battles, Phase Battles: Fixed an issue where disabling 'Auto End Turn' still ended the turn after a combatant finished using an action and no longer having any actions per turn left.
- Shop Layouts: Fixed an issue where 'Auto Close Buy' and 'Auto Close Sell' didn't close the shop's item selection (caused by the inventory change refreshing the shop's UI).
- Schematics: Select Ability: Fixed an issue where the 'Ability Type' setting wasn't displayed when using 'Limit Ability Type.
- Unity UI: UI Research Tree: Fixed an issue where unavailable research item buttons and their lines where not disabled (hidden). Affected both 'Single' and 'Multi' variants.


-------------------------------------------------------------------------------------------------------------------------------------------------------
ORK Framework 3.10.0
-------------------------------------------------------------------------------------------------------------------------------------------------------

New:
- Research UI: New features available to display research trees in a tree view UI. The 'Research Tree UI' setup of research trees is used to display single trees, the combatant's setup to display merged trees. Using the Unity UI module uses prefabs with 'UI Research Tree' components to create the trees. The components can create the tree structure using button/line prefabs in the inspector.
- Research Trees: Research Tree UI: 'Research Tree UI' settings available. Define the UI setup for research tree UI that'll be used by the research tree. Research tree UI is used in 'Research (Tree View)' menu screen parts to display a (single) research tree. When using Unity UI module, requires a prefab with a 'UI Research Tree' component attached.
- Research Types, Research Trees: 'Custom Research Item UI' settings available in research types, research trees and individual research items. Optionally override the default research item UI setup for research items of the type/tree or the individual research item.
- Research Trees: Research Items: Ability: 'Learn By Class' setting available. Optionally learn the ability by the class that added the research tree instead of the combatant. If the tree wasn't added by a class (or the class is no longer available to the combatant), the combatant will learn the ability.
- Research Trees: Research Items: Status Value: 'Ugrade Class' setting available. Optionally add the status value upgrade to the class that added the research tree instead of the combatant. If the tree wasn't added by a class (or the class is no longer available to the combatant), the combatant will upgraded.
- Combatants: Research Tree UI: 'Research Tree UI' settings available (general settings and individual combatants). Defines the UI setup for a combatant's research trees when displaying them 'Merged' in a 'Research (Tree View)' menu screen part.
- Combatants: Available Equipment: 'Replace Class Equipment' setting available. Optionally replace the class equipment settings of the combatant's class. If disabled, both combatant and class equipment settings are used together, i.e. the class can add additional equipment slots and available equipment to the combatant.
- Status Bonus Templates, Combatants, Classes, Equipment, Abilities, Status Effects: Block Status Changes: 'Block Status Changes' settings available in all status bonus settings. Optionally block status changes from attacks, abilities and items (target changes) to the combatant with the status bonus.
- Abilities, Items: Target Settings: 'Ignore Status Change Blocks' setting available. Optionally ignore any 'Block Status Changes' from status bonuses that are on the target.
- Turn Based Battles: 'Alternating Turn Order' setting available in 'Classic' and 'Active' turn based modes. Player/ally and enemy combatants will alternate in the turn order (e.g. player > enemy > player > enemy > etc.). The turn calculation (using the combined outcome of the whole group) determines which side will start the turn order.
- Battle Spots: 'Ignore Scale' setting available for individual battle spots. Optionally ignore the scale of the battle's game object when placing battle spots.
- HUDs: Combatant, Combatant Object: Display Conditions: 'Limited Time Display' settings available. Optionally only display the HUD for a defined time after getting notified by a status change of the combatant. E.g. display the HUD for 5 seconds after the combatant's experience status value increases. Supports getting notified by status value, status effect, attack/defence modifier, (object) variable and inventory changes.
- Menu Screens: Research (Tree View): 'Research (Tree View)' menu part available. Displays research trees in a tree view, optionally separated by research types. Uses 'Research Tree UI' setups of the research tree to display individual, single trees. Using 'Merged' tree display, uses the menu user's 'Research Tree UI' to display the merged trees.
- Menu Screens: Multi Slot: 'Multi Slot' menu part available. Displays defined equipment slots, class slots, AI behaviour slots, AI ruleset slots and shortcut slots of a combatant. Each slot defines what'll be available to equip on the slot (e.g. equipment slots via item type, shortcut slots define if items, abilities and which types, etc.).
- Menu Screens: Multi Content: 'Multi Content' menu part available. Displays the inventory, abilities and special actions of a combatant. The content can be separated by item types and ability types, and allows using a sub menu.
- Menu Screens: Single Slot: 'Single Slot' menu part available. Displays a list of content for a defined equipment slot, class slots AI behaviour slot, AI ruleset slot or shortcut slot of a combatant. Works like the 'Multi Slot' menu part, but for quick-selection of a single, defined slot.
- Menu Screens: Quest: Quest Box Settings: 'Sort Order' settings available for adding active, inactive, finished and failed quests. Defines the order in which quests are displayed based on their state. All default to 0 (ignoring state for the sort order).
- Menu Screens: Sub Menus: 'Always Open' setting available. Optionally always show the sub menu (opened unfocused) when an input with content (e.g. item, equipment, etc.) is selected.
- Menu Screens: Sub Menus: Message: 'HUD Type' settings available in the sub menu's content. Optionally use a HUD or shortcut UI in the sub menu's content.
- Menu Screens: Sub Menus, Menu Actions: Sell: 'Sell' menu action available. Sells the selected item/equipment/etc., adding the currency to the menu user's inventory and removing the item. Only used for sellable content.
- Menu Screens: Sub Menus, Menu Actions: 'Use Condition' settings available. Optionally make the sub menu item or menu action only available when defined conditions are valid. Can check the menu screen's user and variables on the action's content (e.g. an item or equipment).
- Menu Screens: Group: Sort: 'Use Whole Group' setting available for 'Sort' action. Optionally change the order of the whole group instead of only the battle group.
- Menu Screens, Battle Menus, Shop Layouts: Description: 'HUD Type' settings available in 'Content Description' settings when using custom content. Optionally use a HUD or shortcut UI in the description display.
- Shortcut Settings: 'Slot Content Information' settings available. Define content information (name, description, etc.) for shortcut slots that can be used by other UI, e.g. HUD elements or the 'Multi Slot' menu part. Also allows setting up content information per shortcut list (via list index) and for individual shortcut slots (via list/slot index).
- UI Settings: HUD Settings: 'Default Research Item UI' settings available. Define the default research item UI setup for all research items. Research item UI can be used by UI to display a research item, similar to 'Status Effect UI', 'Status Value UI' and 'Shortcut UI' features.
- Inventory Settings: Item Box: 'Sort By' setting available in the 'Item List Settings'. Defines the order of items that are displayed in an item box dialogue.
- Schematics: Sell From Inventory: 'Sell From Inventory' node available in 'Inventory > Inventory' nodes. Sells sellable content from a combatant's inventory. Either sells defined items or content stored in selected data.
- Schematics: Is In Shop: 'Is In Shop' node available in 'UI > Shop' nodes. Checks if a stored shop (using the shop's 'Shop ID') contains defined items, equipment, etc.
- Schematics: Select Combatant Objects: 'Only Nearest/Farthest' setting available when using 'Distance Sort' settings. Optionally only store the nearest combatant into selected data. When using 'Invert Order', the farthest combatant will be stored.
- Item Collector Component: Collection Settings: 'Use Auto Stop Distance' setting available. Optionally use the auto stop distance defined in 'Base/Control > Game Controls' to automatically stop the interaction when the player is a defined distance away.
- Shop Interaction Component: 'Use Auto Stop Distance' setting available. Optionally use the auto stop distance defined in 'Base/Control > Game Controls' to automatically stop the interaction when the player is a defined distance away.
- Auto Machine Component: Notify Start: 'Attack Modifier Attribute Change' notify start type available. Starts the machine by attack modifier attribute value changes of a combatant.
- Auto Machine Component: Notify Start: 'Defence Modifier Attribute Change' notify start type available. Starts the machine by defence modifier attribute value changes of a combatant.
- Auto Machine Component: Notify Start: 'Defence Modifier ID Change' notify start type available. Starts the machine by defence modifier ID changes of a combatant.
- Unity UI: UI Research Tree (Single) Component: 'UI Research Tree (Single)' component available. Used to display a single research tree via 'Research Tree UI' setups. The research item buttons are referenced only by the index of the research item, allowing the setup to be shared between multiple research trees in case they have the same structure. The component's inspector can create a tree structure using prefabs for buttons and lines.
- Unity UI: UI Research Tree (Multi) Component: 'UI Research Tree (Multi)' component available. Used to display one or more research trees via 'Research Tree UI' setups. The research item buttons are referenced by research tree and index of the research item, allowing to show multiple research trees together (via the combatant's research tree UI setup, used in 'Merged' tree display). The component's inspector can create a tree structure using prefabs for buttons and lines.
- Unity UI: UI Research Item Button Input Component: 'UI Research Item Button Input' component available. Used for input buttons displaying research items in 'UI Research Tree' components.
- Unity UI: UI Research Line (UI Line Renderer) Component: 'UI Research Line (UI Line Renderer)' component available. Used to display a line between research item buttons. The line is displayed using 'UI Line Renderer' components included in Makinom. Uses different line colors to represent the current state of the line's target research item.
- Unity UI: UI Research Line (Game Object) Component: 'UI Research Line (Game Object)' component available. Used to display a line between research item buttons. Uses different game objects to represent the current state of the line's target research item.
- Unity UI: ORK HUD Status Text Content: New text codes available for all content with shortcut slot information. You can now display a shortcut slot's name, description, etc. using text codes.
- Unity UI: ORK HUD Status Text Content: Research Item: 'Use Content Icon' setting available. Uses the research item's icon as the icon.
- Unity UI: HUD Research Item List Component: 'UI Key' setting available. Defines the key that identifies the used research item UI setup. Falls back to the component's prefab if no research item UI is set up.
- Unity UI: HUD Quest List Component: 'Sort By' and 'Sort Order' settings available for listing quests.
- Unity UI Setup: Context Menu: 'Research' entries available in 'ORK Framework > HUD'. Create different variants of research item contents for HUD prefabs.
- HUD Conditions: Research Item State: 'Research Item State' condition type available. Checks if the HUD user is research item with a defined state.
- Inspectors: Combatants: The 'Combatant' inspector information now has a separate foldout for inventory information and allows adding/removing items to/from the combatant's inventory.

Changes:
- Status Effects: The 'Block Status Changes' settings are now part of the 'Status Bonuses' settings. Previous settings are updated automatically, adding a custom bonus for the change blocks.
- UI: Content Layouts: The 'Shortcut UI' HUD type has been renamed to 'UI Key Setup' and now supports displaying UI setups for all UI key content. I.e. shortcuts (abilities, items, equipment, etc.), research items, status effects and status values.
- Menu Screens: Types: The 'All Type' button is now also available when only displaying defined types. Available in all menu screen parts that have type display.
- Menu Screens: The 'Research' menu screen part has been renamed to 'Research (List View)'.
- Menu Screens: Research: 'HUD Content' settings have been removed in the 'Research Item Box' settings. Please use the HUD options of the content layout instead (now also supports the new 'Research Item UI'). The previous setup will not be updated automatically.
- Menu Screens, Battle Menus, Shop Layouts: Description: Not using 'Always Visible' will now show the description for buttons or content without description when using custom text.
- Research Trees: Research Items: Level Up: Using 'Use Class Level' will now increase the level of the class that added the research tree. If the tree wasn't added by a class (or the class is no longer available to the combatant), the combatant's current single-class level will increase.
- Editor: Battle Texts: The 'Action Info Notifications' are now their own main foldout, no longer being placed under the 'Text Settings'.

Fixes:
- HUDs: Combatant Object: Fixed an issue where a 'Combatant Object' type HUD that shows shortcut slot content could lead to an error when displayed for a combatant added via 'Add Combatant' component.
- HUDs: Turn Order: Fixed an issue where the turn order would be displayed wrong in 'Multi Turns' turn based battles with inverted turn order.
- Unity UI: ORK HUD Icon Bar Content Components: Fixed an issue that caused an error when displaying icon bars.
- Unity UI: HUD Equipment List Component, HUD Equipment Slot Content Component: Now use the equipment slot as user for spawned UI prefabs instead of the equipped equipment. Allows displaying equipment slot information.
- Menu Screens: AI Behaviour, AI Ruleset: Fixed an issue where usint 'Tabs' type display still showed a type UI box.
- Bestiary: Fixed an issue where combatants outside of battle didn't check bestiary information for displaying their status.
- Grid Battles: Examine: Fixed an issue where combatant move ranges (when showing the max move range) wheren't displayed if their turn wasn't initialized yet.
- Item Boxes: Fixed an issue that could cause errors when showing description in the item box dialogue.
- UI: Previews: Fixed issues that could cause previews to stay or not appear when not using domain reload in the Unity editor.


-------------------------------------------------------------------------------------------------------------------------------------------------------
ORK Framework 3.9.0
-------------------------------------------------------------------------------------------------------------------------------------------------------

New: 
- Item Selections: Combatant: 'Combatant' item selection type available (e.g. used in loot tables, item collectors, etc.). The defined combatant will join the group of the combatant collecting it. When used for checks, it'll check if the combatant is a member of the user's group.
- Item Selections: Item Type: 'Limit Level' settings available when using equipment. Optionally limit which equipment is used by a defined level check.
- Types: 'Hidden' setting available in all types that support sub-types (e.g. item types, ability types, etc.). A hidden type will not be displayed in menus or shops. E.g. use hidden types in combination with secondary types for background mechanics, type checks, etc.
- Battle End: Loot Dialogues: Sequence: 'Sequence' loot dialogue type available. Displays the battle's loot and player combatant changes/level ups in individual UI boxes in sequence, one after another. Displaying the loot or combatant changes/level ups can optionally display them all at once before continuing to the next content (e.g. showing loot one at a time, followed by all combatant changes at once).
- Battle Settings, Abilities, Items: Reuse Time, Delay Time: 'Set On' setting available. Define when the reuse/delay time will be set, either at the start of the action, when the action's outcome (use costs) is calculated or at the end of the action. Defaults to 'End' (previous behaviour).
- Control Maps: Actions: 'Ability Type' and 'Item Type' action types available. Uses the first useable ability/item of a defined type.
- Status Effects: End Action: 'Ability Type' and 'Item Type' action types available. Uses the first useable ability/item of a defined type as the effect's end action.
- AI Rulesets: Action:  'Ability Type' and 'Item Type' action types available in the 'Action' rule. Uses the first useable ability/item of a defined type.
- Action Combos: Required Actions: 'Ability Type' and 'Item Type' action types available. Checks if an ability or item is of a defined ability/item type.
- Action Combos: Replacement Action: 'Ability Type' and 'Item Type' action types available. Uses the first useable ability/item of a defined type as the replacement action.
- Menu Screens: Inventory, Inventory Exchange: 'Limit Level' settings available when showing equipment in the inventory menu screen. Optionally limit which equipment is displayed by a defined level check.
- Battle AI, Formulas, Schematics: Select Item: 'Limit Level' settings available in the 'Select Item' node when selecting equipment. Optionally limit which equipment is selected by a defined level check.
- Schematics: Has Inventory Containers: 'Has Inventory Containers' node available in 'Inventory > Container' nodes. Checks if a combatant has inventory containers.
- Schematics: Has In Inventory Container: 'Has In Inventory Container' node available in 'Inventory > Container' nodes. Checks if a combatant's inventory container has defined items, equipment, etc., or checks if a defined slot contains the item.
- Schematics: Has In Inventory Container Fork: 'Has In Inventory Container Fork' node available in 'Inventory > Container' nodes. Checks if a combatant's inventory container has defined items, equipment, etc., or checks if a defined slot contains the item. The first found item's 'Next' slot will be executed.
- Schematics: Check Status: 'Needed' setting available for combatants. Either 'All' or only 'One' of the combatants must match the defined conditions. Defaults to 'All' (previous behaviour).
- Schematics: Use Battle Action: 'Ability Type' and 'Item Type' action types available. Uses the first useable ability/item of a defined type.
- Unity UI: HUD Equipment Slot Content Provider Component: 'HUD Equipment Slot Content Provider' component available. Uses a combatant's defined equipment slot as content.
- Unity UI: HUD Equipment Slot Content Component: 'HUD Equipment Slot Content' component available. Displays an equipment slot's equipment or an empty slot (both via shortcut UI) and allows drag/drop/click interaction to change the equipped equipment. Works similar to the 'HUD Shortcut Slot Content' component.
- Unity UI: HUD Class Slot Content Provider Component: 'HUD Class Slot Content Provider' component available. Uses a combatant's defined class slot as content.
- Unity UI: HUD Class Slot Content Component: 'HUD Class Slot Content' component available. Displays a class slot's class or an empty slot (both via shortcut UI) and allows drag/drop/click interaction to change the equipped class. Works similar to the 'HUD Shortcut Slot Content' component.
- Unity UI: HUD AI Behaviour Slot Content Provider Component: 'HUD AI Behaviour Slot Content Provider' component available. Uses a combatant's defined AI behaviour slot as content.
- Unity UI: HUD AI Behaviour Slot Content Component: 'HUD AI Behaviour Slot Content' component available. Displays an AI behaviour slot's AI behaviour or an empty slot (both via shortcut UI) and allows drag/drop/click interaction to change the equipped AI behaviour. Works similar to the 'HUD Shortcut Slot Content' component.
- Unity UI: HUD AI Ruleset Slot Content Provider Component: 'HUD AI Ruleset Slot Content Provider' component available. Uses a combatant's defined AI ruleset slot as content.
- Unity UI: HUD AI Ruleset Slot Content Component: 'HUD AI Ruleset Slot Content' component available. Displays an AI ruleset slot's AI ruleset or an empty slot (both via shortcut UI) and allows drag/drop/click interaction to change the equipped AI ruleset. Works similar to the 'HUD Shortcut Slot Content' component.
- Unity UI: HUD Quest List Component: 'Limit Quest Count' setting available. Optionally limit the number of displayed quests to a defined amount.
- Unity UI Setup: Context Menu: Equipment, Class, AI Behaviour, AI Ruleset: Added new singe slot variants for equipment/class/AI behaviour/AI ruleset slot creations. Creates a new child object with the related content provider and content components added.
- Move AI Hiding Area Component: 'Move AI Hiding Area' component available. Combatants entering the area's trigger will be hidden from move AI detection.

Fixes:
- Menu Screens: Status Value Distribution: Fixed an issue where changing the menu user didn't reset the (unconfirmed) status changes.
- Turn Based Battles: Multi Turns: Fixed an issue where combatants doing nothing could be skipped in the turn order.
- Battle End: Loot Dialogues: Fixed an issue where not combining level ups with combatant gains still combined level ups that exceeded one level up.
- Battle System: Fixed an issue where removing a target from registered actions (e.g. due to the combatant's death) could cause errors in some situations.


-------------------------------------------------------------------------------------------------------------------------------------------------------
ORK Framework 3.8.0
-------------------------------------------------------------------------------------------------------------------------------------------------------

New:
- Item Selections: Item Type: 'Item Type' item selection type available (e.g. used in loot tables, item collectors, quest task requirements, etc.). Uses a random item, equipment, currency, crafting recipe, AI behaviour or AI ruleset of a defined item type. Depending on where it's used, it'll generate a random item, get a random item of the type from an inventory, check for an item of the type being in an inventory, etc.
- Status Values: Bonus Display: 'Bonus Display' settings available in the 'Information Overrides' settings. Optionally override how status value bonuses are displayed in bonus texts (using the '<bonus>' text code, e.g. in an equipment's description).
- Status Effects: Bonus Display: 'Bonus Display' settings available in the 'Information Overrides' settings. Optionally override how status effect bonuses are displayed in bonus texts (using the '<bonus>' text code, e.g. in an equipment's description).
- Attack Modifiers, Defence Modifiers: Attributes: Bonus Display: 'Bonus Display' settings available in the individual modifier attributes. Optionally override how attribute bonuses are displayed in bonus texts (using the '<bonus>' text code, e.g. in an equipment's description).
- Ability Types, Item Types: Use Range: 'Use Range' settings available. Ability/item types can optionally override the default use range for all abilities/items using them as their primary type. Abilities/items can override the type's and default use range.
- Abilities, Equipment: Levels: Override Content: 'Override Content' settings available. Optionally override the name, short name or icon (in addition to the already available description override) of an ability/equipment per level.
- Abilities, Equipment: Levels: Override Portraits: 'Override Portraits' settings available. Optionally override the ability/equipment's portraits per level. If the portrait type isn't defined for a level, it'll fall back to the ability/equipment's original portraits.
- Abilities, Items: Status Changes: 'Move Range' status change type available. Changes the grid move range of the user or target of the status change.
- Currencies, Items, Equipment, AI Types, Crafting Types, Ability Types, Classes, Combatant Types, Quest Types: 'Secondary Item Type' settings available. Optionally add secondary item types to list items under multiple item types in menus/shops or other type checks. The primary item type (regular 'Item Type' setting) is the actual type the items belong to and is also used for the item type text codes.
- Abilities: 'Secondary Ability Type' settings available. Optionally add secondary ability types to list abilities under multiple ability types in menus or other type checks. The primary ability type (regular 'Ability Type' setting) is the actual type the ability belongs to and is also used for the ability type text codes.
- Crafting Recipes: 'Secondary Crafting Type' settings available. Optionally add secondary crafting types to list recipes under multiple crafting types in menus or other type checks. The primary crafting type (regular 'Crafting Type' setting) is the actual type the recipe belongs to and is also used for the crafting type text codes.
- AI Behaviours, AI Rulesets: 'Secondary AI Type' settings available. Optionally add secondary AI types to list behaviours/rulesets under multiple AI types in menus or other type checks. The primary AI type (regular 'AI Type' setting) is the actual type the behaviour/ruleset belongs to and is also used for the AI type text codes.
- Quests: 'Secondary Quest Type' settings available. Optionally add secondary quest types to list quests under multiple quest types in menus or other type checks. The primary quest type (regular 'Quest Type' setting) is the actual type the quest belongs to and is also used for the quest type text codes.
- Quest Tasks: Requirements: Enemy Kills: 'Allow Secondary Types' setting available when using combatant types. Optionally also count combatants with the defined combatant type as secondary type.
- Logs: 'Secondary Log Type' settings available. Optionally add secondary log types to list logs under multiple log types in menus or other type checks. The primary log type (regular 'Log Type' setting) is the actual type the log belongs to and is also used for the combatant type text codes.
- Combatants: 'Secondary Combatant Type' settings available. Optionally add secondary combatant types to list combatants under multiple combatant types in menus or other type checks. The primary combatant type (regular 'Combatant Type' setting) is the actual type the research tree belongs to and is also used for the research type text codes.
- Research Trees: 'Secondary Research Type' settings available. Optionally add secondary research types to list research trees under multiple research types in menus or other type checks. The primary research type (regular 'Research Type' setting) is the actual type the research tree belongs to and is also used for the research type text codes.
- Battle System: Active Time: 'Action Blocks Choosing' setting available. Performing actions can optionally block combatants from starting to select actions. This doesn't prevent already choosing combatants from continuing to do so.
- AI Rulesets: Rules: Block Ability, Block Item: 'Use Sub-Types' setting available. Optionally also block sub-types of the defined ability/item types.
- Loot: Loot Tables: Level Check Settings: Advanced 'Level Check Settings' available, replacing the previous level range. You can now use a regular value check for the base/class level instead of a fixed level range check. Additionally, the level check is now optional. Previous settings will be updated automatically to work as before.
- Loot: Loot Tables: Chance: 'Chance' settings available. Optionally use a chance check to determine if a loot table is used or not.
- Menu Screens: Equipment (Single Slot): 'Equipment (Single Slot)' menu part available. Displays a list of equipment that can be equipped on a single, defined equipment slot. The current equipped equipment can be added and highlighted. E.g. use this menu part to create an equipment quick selection menu for a single slot (e.g. weapon switching).
- Menu Screens: Status Value Distribution: New text codes available. Use '<startvalue>' to add the original/start value (from opening the menu screen) and '<change>' to add the change (to the start value) in value inputs.
- Menu Screens: Sub Menus: Unequip: 'Unequip' menu action available. Unequips an equipment, AI behaviour or AI ruleset. Only used for things already equipped (e.g. when listing the equipped equipment in an equipment menu part.
- Menu Screens: Sub Menus: Use: 'Allow Unequipping' setting available. Optionally allow using the 'Use' action for already equipped equipment, AI behaviours or AI rulesets. Will display the 'Unequip' button (when not using 'Own Button Content') and unequip the equipped content.
- Menu Screens, Battle Menus, Shop Layouts: Description: New text code available for 'General Description'. Use '<additionalcontent=KEY>' to add additional content of the selected input to the description display. 'KEY' needs to be replaced with the 'Content ID' of the additional content that should be added.
- Text Display Settings: Bonus Display: 'Status Value Bonus Display (Combined)' settings available. Add a combined value+percent bonus display using the '<statuscombined>' text code. Each status value will list it's value and percent bonus before the next status value is listed. Uses the change texts of the regular 'Value' and 'Percent' status value bonus displays (or the status value's overrides).
- Text Display Settings: Bonus Display: Status Values, Attack Modifiers, Defence Modifiers: New text code available. Use '<changeabs>' to show the value change as an absolute value (i.e. no negative value, e.g. changin -5 to 5).
- Battle Grid Highlights: Custom Highlights: 'Custom Highlights' settings available. Define 5 custom grid highlights that can be used by the 'Highlight Grid Cell' node in schematics.
- Schematics: Highlight Grid Cell: 'Highlight Grid Cell' node available in 'Battle > Grid Highlight' nodes. Start or stop highlighting grid cells with a defined cell highlight.
- Schematics: Change Grid Cell Type: 'Change Grid Cell Type' node available in 'Battle > Grid Cell' nodes. Changes or resets the grid cell type of a cell (or combatant's cell). If the cell's prefab is shown, the prefab will be destroyed and the new prefab is spawned.
- Schematics: Reset Grid Cell Types: 'Reset Grid Cell Types' node available in 'Battle > Grid Cell' nodes. Resets all grid cell type overides of the current battle's grid or a grid on a defined game object.
- Schematics: Select Combatant Battle Spot: 'Select Combatant Battle Spot' node available in 'Value > Selected Data' nodes. Uses a combatant's battle spot (game object) to change selected data.
- Schematics: Store Combatant Count: 'Use Int Variable' setting available. Optionally store the count as an int variable instead of a float variable.
- Inspectors: Move AIs: Having a game object with a move AI selected will now show the detection ranges/angles in the scene.
- Unity UI: ORK HUD Status Text Content: Ability, Equipment, Class, Combatant Information: New text code available. Use '<bonus>' text code to show the displayed content's status bonuses (same as using them in the content's description).
- Unity UI: HUD List Components: 'Separator Prefab' setting available in all HUD list components (e.g. 'HUD Status Value List'). Optionally use a prefab to create separator elements between two listed HUD elements. The separator will only be spawned between two elements, i.e. not before the first or after the last listed element.
- HUD Conditions: Status Value Preview Change: 'Status Value Preview Change' condition type available. Checks if the HUD user is a status value with a positive/negative preview change or no change (requires a status value).
- HUD Conditions: Attack Modifier Attribute Preview Change: 'Attack Modifier Attribute Preview Change' condition type available. Checks if the HUD user is an attack modifier attribute with a positive/negative preview change or no change (requires an attack modifier attribute).
- HUD Conditions: Defence Modifier Attribute Preview Change: 'Defence Modifier Attribute Preview Change' condition type available. Checks if the HUD user is a defence modifier attribute with a positive/negative preview change or no change (requires a defence modifier attribute).
- HUD Conditions: Is In Bestiary: 'Is In Bestiary' condition type available. Checks if the HUD user is added to the bestiary (requires a combatant).
- HUD Conditions: Is Bestiary Complete: 'Is Bestiary Complete' condition type available. Checks if the HUD user's bestiary entry is complete (requires a combatant).

Changes:
- Combatants: Object Variables: Using object variables now initializes them with the combatant's status instead of when first using them (or spawning the combatant).
- Status Effects: Status Value Changes: A combatant being killed by a status effect now sets the effect's user (the combatant that added the effect) as the killer of the combatant.
- HUD Conditions: Status Preview Blink: The 'Status Preview Blink' HUD condition no longer requires any HUD user and only checks the overall preview blink state. Previously the condition required a combatant as HUD user.
- Schematics: Grid Nodes: The 'Battle > Grid' nodes' have been split into 'Battle > Grid', 'Battle > Grid Cell' and 'Battle > Grid Highlight' nodes.

Fixes:
- Battle System: Active Time: Fixed an issue where not using the full timebar for actions could lead to the used timbar being wrong, blocking other actions.
- Turn Based Battles, Phase Battles: Fixed an issue where the 'None' action didn't end the combatant's turn.
- Battle Menus: User Highlights: Fixed an issue where the user highlights wheren't removed when using an action from a sub battle menu (via 'Battle Menu' option to call another battle menu).
- Menu Screens: Equipment: Sub Menu: Fixed an issue where the 'Use' sub menu item would duplicate an already equipped equipment (when it's listed with the rest of the equipment). The 'Use' item can't be used when called on an already equipped equipment.
- HUDs: Turn Order: Fixed an issue where the turn order could become mixed up.
- Schematics: Refresh Menu Screen: Fixed an issue where only certain menu parts where refreshed instead of all parts of a menu screen.
- Unity UI Components: Added an automatic workaround for Unity UI components going missing when a project's library is rebuild.
- Move AIs: Detection: Fixed an issue where 'Movement' detection not limiting to only horizontal speed checks was only checking vertical speed.
- Groups: Regeneration: Fixed an issue where regenerating the group could cause an error (e.g. via the 'Regenerate' node in schematics).
- Cursor Settings: Target Selection Cursor: Fixed an issue where using 'Over Origin Shortcut' setting could cause an error.
- Bestiary: Fixed an issue where status change previews could add attack modifier bestiary information.


-------------------------------------------------------------------------------------------------------------------------------------------------------
ORK Framework 3.7.0
-------------------------------------------------------------------------------------------------------------------------------------------------------

New:
- Classes: Class Slots: 'Class Slot Settings' available. Define on which class slots a class can be equipped on. Either all class slots, only defined slots or all slots excluding defined slots. Defaults to equipable on all class slots.
- Classes: Schematics: 'Schematic Settings' available. Optionally use schematics when initializing the class, adding/removing it to a combatant or equipping/unequipping it on a class slot.
- Classes: Custom Shortcut UI: 'Custom Shortcut UI' settings available. Optionally override the default shortcut UI setup for a class.
- Classes: Portraits: 'Portaits' settings available. The class can display portraits in menu screens when it's selected.
- Classes: Notifications, Console Texts: 'Notifications' and 'Console Texts' settings available. Classes can override the default notifications and console texts.
- Classes: 'Is Group Class' setting available. The class is available to all members of a group when added to a combatant. Each combatant still use their own unique instances of the class, increasing the class level on their own.
- Classes: 'Item Type' and 'Buy Price' settings available. Defines the price a class is sold for in shops and the item type under which it'll be listed.
- Status Effects: Effect States: 'Block Class Slots' setting available. Blocks changes to class slots while the status effect is applied to a combatant.
- Research Trees: Class: 'Class' research item type available. Adds a class to the user's available classes.
- Research Trees: Class Slot: 'Class Slot' research item type available. Adds a class slot to the user.
- Available Equipment: 'None' type available. Doesn't add any equipment slots and allows no equipment or item types. E.g. use this as setup of a combatant if the available equipment comes from the class.
- Menu Screens: Class Slot: 'Class Slot' menu part available. Displays the class slots of a combatant. The class slots a combatant can access are displayed, classes can be equipped at each class slot.
- Menu Screens: Class (Single): 'Class (Single)' menu part available. Displays the classes available to a combatant and highlights the current single-class. The single-class of the combatant can be changed by selecting a different class.
- Menu Screens: Equipment, AI Behaviour, AI Ruleset: 'Add Equipped' settings available. Optionally add the slot's currently equipped content (equipment, AI behaviour/ruleset) to the displayed list. The equiped content's input can optionally be highlighted by a defined color.
- Menu Screens: Equipment, AI Behaviour, AI Ruleset: 'Equip Back' and 'Unequip Back' settings available. Defines if equipping or unequipping something will return to the slot UI box. By default enabled.
- Menu Screens: Group: Combatant Choice Layout: 'Own Group 2 Layout' settings available when using 'Own Layout' for combatants. Optionally use a different combatant choice layout for the 2nd group box.
- Menu Screens: Group: Sort Choice Layout: 'Highlight Sorting Input' settings available. Optionally highlight the input of the combatant that's being sorted with a defined color.
- Menu Screens: Group: Action: Schematic: 'No Selection' setting available. Optionally use the schematic immediately without selecting a group member.
- Menu Screens: Group: Action: Schematic: 'Close Menu Screen' settings available. Optionally close the menu screen when using the schematic.
- Menu Screens: Parts: Titles: New text codes available to add content information of the menu screen's user. Available in the optional title contents of most menu screen parts.
- Menu Screens: Parts: Message Content: 'Message Content' settings available for UI boxes of most menu screen parts. Add a messate/text content to menu screen parts.
- Battle Menus: Options: 'Class (Single)' option type available. Lists the combatant's classes and allows changing the current single-class.
- Battle Menus: Options: 'Class Slot' option type available. Lists the combatant's class slots and allows changing the equipped class.
- Battle Menus: Sub-Menus: 'Class Slot Box' and 'Class Box' settings available. Defines UI boxes used to list class slots and classes in 'Class' type options.
- Battle Menus: Headers: 'Class Slot Header Texts' and 'Class Header Texts' settings available. Defines the headers used when listing class slots and classes in 'Class' type options.
- Notifications: Class Notifications: 'Class Notifications' available. Optionally show notifications when a class or class slot is added or removed to/from a combatant. Only used for player combatants.
- Shortcut Settings: Default UI Settings: 'Default Class UI' settings available. Define the default UI setup used to display classes. Classes can override the default UI.
- Console Settings: Class Texts: 'Class Texts' available. Optionally show console texts when a class or class slot is added or removed to/from a combatant. Only used for player combatants.
- Shops: Classes: 'Classes' settings available. Add classes that will be sold in a shop.
- Formulas: Selected Data Level: 'Selected Data Level' node available in 'Selected Data' nodes. Uses the level of something stored in selected data as value. E.g. an ability, equipment, combatant or class's level.
- Formulas: Class Slot Level: 'Class Slot Level' node available in 'Combatant' nodes. A combatant's current or maximum class level of a class equipped on a class slot is used.
- Formulas, Schematics: Equipment Fork: 'Check Item Type' settings available. Optionally check for an item type being equipped instead of a defined equipment.
- Status Conditions: 'Class Equipped' status check available. Checks if a class is or isn't currently equipped by a combatant (either on any class slot or a defined class slot).
- Status Conditions: 'Class Slot Available' status check available. Checks if a class slot is available on the combatant or not.
- Status Conditions: 'Class Slot Equipped' status check available. The level of a class equipped on a defined class slot will be compared to a defined value.
- Status Conditions: 'Class Slot Level' status check available. Checks if a combatant's class slot is equipped with a class or not.
- Status Conditions: 'Equipment Slot Available' status check available. Checks if an equipment slot is available on the combatant or not.
- Status Conditions: 'Equipment Slot Equipped' status check available. Checks if a combatant's equipment slot is equipped with an equipment or not.
- Float Values: Class Slot Level: 'Class Slot Level' value type available. Uses the level of a class equipped on a combatant's class slot as value.
- UI: Content Layouts: Shortcut UI: 'Use Empty UI' setting available when adding 'Shortcut UI' to a content layout. Optionally use the 'Empty UI' when no shortcut is displayed. E.g. in 'Equipment' menu screen parts for empty equipment slots.
- Schematics: Add Class: 'Add Class' node available in 'Combatant > Class' nodes. Adds classes to a combatant. Added classes are available to equip on class slots and can also be switched to as the single-class of the combatant.
- Schematics: Remove Class: 'Remove Class' node available in 'Combatant > Class' nodes. The removed class will be unequipped from all class slots and the single-class of the combatant.
- Schematics: Add Class Slot: 'Add Class Slot' node available in 'Combatant > Class' nodes. Class slots can be used to equip classes on.
- Schematics: Remove Class Slot: 'Remove Class Slot' node available in 'Combatant > Class' nodes. The removed class slots will unequip their current class.
- Schematics: Change Class Slot: 'Change Class Slot' node available in 'Combatant > Class' nodes. Changes the classes equipped on class slots of a combatant.
- Schematics: Change Class: 'Use Selected Data' settings available. Optionally change to a class stored in selected data.
- Schematics: Add To Shop: 'Classes' settings available. Add classes that will be sold in a shop.
- Schematics: Select Class: 'Select Class' node available in 'Value > Selected Data' nodes. Uses classes of a combatant as selected data.
- Schematics: Select Equipped Class: 'Select Equipped Class' node available in 'Value > Selected Data' nodes. Uses classes currently equipped on a combatant's class slots as selected data.
- Schematics: Refresh Menu Screen: 'Refresh Menu Screen' node available in 'UI > Menu' nodes. Marks all parts of a menu screen to be refreshed in the next frame.
- Schematics: Lock Battle Combatants: 'Lock Battle Combatants' node available in 'Battle > Spawn' nodes. Locks or unlocks combatants participating in battle from being removed from the battle/game. Otherwise, destroying their game objects would remove them from the game (excluding player group members).
- Schematics: Destroy Battle Combatants: 'Lock Combatants' setting available. Lock the battle combatants from being removed from the battle/game when destroying them. Same as using the 'Lock Battle Combatants' node.
- Unity UI: HUD Click Component: 'Close Current Menu Screen' click type available. Closes currently open menu screens. Optionally clears the menus before closing, i.e. not returning to previous menus.
- Unity UI: HUD Class Slot List Content Component: 'HUD Class Slot List Content' component available. Lists the class slots of a combatant.
- Unity UI: HUD Class Slot Content Provider Component: 'HUD Class Slot Content Provider' component available. Uses a combatant's defined class slot as content.
- Unity UI: ORK HUD Status Text Content, ORK HUD Value Bar Content: Status Value: The 'Status Value' type now supports displaying the experience status value of a class when the class or a class slot with an equipped class is the HUD user. E.g. can be used to show a class's experience as a value bar on a menu button displaying a class or a tooltip HUD.
- Unity UI: HUD Ability List Content Component: Now supports showing abilities of a class when the provided content comes from a class or class slot (e.g. tooltip HUD for a class).
- Unity UI: ORK HUD Value Bar Content: Cast Time, Delay Time, Reuse Time, Status Effect Duration: 'Unused Filling' setting available. Defines how the bar is filled if no time/duration is used by the displayed content. 'Auto' sets the bar based on the inverted setting, 'Empty' and 'Full' will set the bar to empty/full. E.g. use this to always fill an icon if no time/duration is used. Defaults to 'Auto'.
- Unity UI Setup: Context Menu: Status Value: Added new 'no content provider' variants for status value creations. Uses the content provider of the HUD the status value is added to instead of creating a new one for the status value. E.g. useful for tooltip HUDs to display the tooltip's status value or class's status value.

Changes:
- Classes, Combatants: Combatants no longer require to use a class. Not having a class selected in the combatant's status settings will no longer select the default class (first class in the editor list).
- Classes: Save Games, Scripting: Class data that was previously split up on different parts of the combatant (e.g. status, abilities, shortcut lists) is now combind in the 'ClassInstance' class (per class on a combatant). Old save data is automatically updated when loading the game.
- Status Conditions: The 'Class' status check has been renamed to 'Class (Single)' to not get confused between single-class and class slot checks.
- Schematics: Change Class: The 'Change Class' node has been moved to 'Combatant > Class' nodes.
- Schematics: Change Class: The 'Reset Attack/Defence Modifier' settings have been removed. Attack/defence modifier start values are now always reset when changing classes.

Fixes:
- Inventory Containers: Fixed an issue where remembering lost items (due to slot count being reduced) could cause an error.
- Status Developments: Fixed an issue where 'Curve' development type didn't allow reducing status values.
- Abilities, Ability Types, Items, Item Types: Battle Info Text: Fixed an issue where using 'Own Battle Info' to override the default battle info texts where always loaded with a new setup instead of the saved settings.
- Unity UI: HUDs: Fixed an issue where HUD content components could enable the closed HUD's game object when added directly on the HUD's root.
- HUDs: Tooltip: Fixed an issue where the 'Status Value' tooltip check didn't work.


-------------------------------------------------------------------------------------------------------------------------------------------------------
ORK Framework 3.6.1
-------------------------------------------------------------------------------------------------------------------------------------------------------

Fixes:
- Battle System: Turn Based: Fixed an issue where combatant turns didn't start when using 'Multi Turns' mode or other modes with 'Auto Start Turn' disabled.


-------------------------------------------------------------------------------------------------------------------------------------------------------
ORK Framework 3.6.0
-------------------------------------------------------------------------------------------------------------------------------------------------------

New:
- Turn Based Battles, Phase Battles, Abilities, Items: Action Costs: 'End Turn' setting available for action costs. Ends the combatant's turn after using the action (even if more actions per turn are available). Only used in 'Turn Based' and 'Phase' battle systems.
- Notifications: 'Can Cancel' setting available. Optionally allow closing the notification via canceling (e.g. cancel button). By default disabled.
- HUDs: Combatant: 'Player Group Scope' setting available. Defines which part of the player group will be displayed, e.g. battle group, battle reserve or the whole group. This replaces the 'Only Battle Group' setting. Old settings will be updated automatically.
- HUDs: Turn Order: 'Add Finished Combatants' setting available. Optionally add the combatants that already finished their turns to the turn order list. They'll be added at the start of the list, before the (optional) selecting combatant) and the remaining combatants.
- Status Conditions: 'Faction' status check available. Checks if a combatant is part of a defined faction or not. Available in all status conditions throughout the framework.
- Available Equipment Templates, Combatants, Classes: Item Types: 'Use Sub-Types' setting available (per item type). Allows/excludes the sub-types of the defined item type.
- Menu Screens: Information: 'Show Ok Button' and 'Show Cancel Button' settings available. Optionally show the ok/cancel buttons of the UI box.
- Menu Screens: Ability, Equipment, Inventory, Inventory Container: Sub Menus: 'Use Content Icon' setting available. Uses the displayed content's icon as icon content.
- Battle Menus: Options: 'Schematic' option type available. Starts a schematic, using the battle menu user as 'Machine Object' and 'Starting Object'. The battle menu can be closed before and reopened after the schematic. The schematic can also reopen the battle menu using a 'Reopen Battle Menu' node.
- Battle Menus: Options: 'Status Changes' settings available for battle menu options/commands. Optionally use status changes when accepting a command, canceling out of an accepted command or closing the battle menu after accepting a command (e.g. by finishing the action selection or changing to a different user). Not used on any sub-menu items called by the option/command, i.e. only by the 'root' command.
- Battle Menus: Options: 'Use Conditions' available. Optionally define conditions that must be valid to be able to use the battle menu command. If the conditions are invalid, the button will be inactive. Also affects buttons of sub-menu items that where opened by the command.
- Combatants: Creation: 'Is Passive' settings available everywhere a combatant can be added (e.g. combatant spawners, combatant groups, schematic nodes, etc.). Sets the combatant as passive and not part of the battle order, i.e. not use any actions but can still be targeted. Passive combatants can optionally also be set to not require being defeated to end a battle.
- Status Effects: End After: 'Action Start' and 'Action End' types available. End an effect after a defined number of actions where started or ended by the effect's target. Optionally define which actions can reduce the duration.
- Status Effects: End After: 'Battle Turn Start' and 'Battle Turn End' types available. End an effect after a defined number of battle turns start or end. Battle turns are only used by 'Turn Based', 'Active Time' and 'Phase' battles, increasing turns independent of combatants. Turn end and start happen at the same time, as when a battle turn ends, the next starts immediately.
- Status Effects: Status Value Changes: 'Action Start' and 'Action End' set on types available. Change the status value each time an action is started or ended by the effect's target. Optionally define which actions will be used.
- Status Effects: Status Value Changes: 'Battle Turn Start' and 'Battle Turn End' set on types available. Change the status value each time a battle turn starts or ends. Battle turns are only used by 'Turn Based', 'Active Time' and 'Phase' battles, increasing turns independent of combatants. Turn end and start happen at the same time, as when a battle turn ends, the next starts immediately.
- Status Effects: Schematics: 'Action Start' and 'Action End' use on types available. Use the schematic each time an action is started or ended by the effect's target. Optionally define which actions will be used.
- Status Effects: Schematics: 'Battle Turn Start' and 'Battle Turn End' use on types available. Use the schematic each time a battle turn starts or ends. Battle turns are only used by 'Turn Based', 'Active Time' and 'Phase' battles, increasing turns independent of combatants. Turn end and start happen at the same time, as when a battle turn ends, the next starts immediately.
- Abilities, Items: Hit Chance, Critical Chance: 'Shared Chance' settings available. Optionally share the hit/critical chance for all targets (and optionally the user). The chance is calculated for the first target, using the same outcome for all targets (and optionally the user).
- Abilities, Items: Status Changes: 'Variable' status change type available. Changes variables - e.g. use 'Object' variables on the user or target of the status change.
- Ability Developments, Classes, Combatants: Learn Ability: 'Use Conditions' settings available. Optionally learn an ability at level up only when defined conditions are valid. The 'Learn at Level' can be used as a minimum learn level, which allows learning an ability at a later level when the conditions fail.
- Inventory Settings: General Settings: 'Update Size By Variables' setting available. Optionally update inventory space and inventory container slot counts by changes to global variables or object variables of the inventory's owners (combatants).
- Factions: 'Combatant Layer' settings available. Optionally place all combatants of a faction on a defined layer.
- Research Trees: Level Up: 'Level Up' research item type available. Increases the level or class level of a combatant or group by a defined number of levels. Can optionally display the standalone level up dialogue.
- Battle System: Turn Based: Schematics: 'Battle Turn Start Schematic' and 'Battle Turn End Schematic' settings available. Optionally execute schematics at the start and end of a battle turn. Battle turns change after all combatants selected and performed their actions.
- Battle System: Active Time: Active time battles now support battle turns. A battle turn lasts until each combatant (that's able to) has performed 1 action. A combatant that's incapacitated (died or status effect blocking all actions) will not be taken into account until the next battle turn (when able to perform actions again). Battle turns can be used in status effects, nodes and displayed via text codes.
- Battle System: Turn Based, Phase: 'Auto End Turn' settings available. Optionally automatically end a combatant's turn if no useable action (defined in these settings) is available. Please note that a combatant's turn also ends if not enough actions per turn remain (see the 'Action Settings').
- Battle End: Standalone Level Up Dialogue: 'Standalone Level Up Dialogue' settings available. Optionally show a level up dialogue outside of battle gains. Can automatically be displayed for experience changes.
- Battle Texts, Abilities, Items: Action Info Notifications: You can now define define separate action info notifications for player, allies and enemies. Previous settings are updated automatically.
- Battle AIs: Check Value: 'Check Value' node available in 'Base' nodes. Checks a value with another value, e.g. the result of a formula.
- Text Display Settings: Number Formatting: 'Attack Modifier Change Format' and 'Defence Modifier Change Format' settings available. Define how changes to attack/defence modifier attribute values are displayed, e.g. used by bonus displays.
- Attack Modifiers, Defence Modifiers: 'Own Change Format' settings available. Optionally override the default value change format defined in 'UI > Text Display Settings'.
- Status Bonuses: Attack/Defence Status Effects: 'Use Critical Effects' settings available. Optionally use separate attack/defence status effects for normal and critical hits. Available in all attack/defence status effect settings.
- Status Bonuses: Attack/Defence Status Effects: 'Block Effect Change' settings available. Define attack/defence status effects that will be used when an attack was blocked. When using 'Use Critical Effects', critical hits define their own block effect changes.
- Combatant Scopes: 'Non Battle Reserve' scope available. Uses a members of a group that aren't part of the combatant's battle group or battle reserve. Available everywhere combatant scope settings are used, e.g. 'Combatant' type HUDs, status conditions, selected data nodes, etc.
- Schematics, Formulas, Battle AIs: Check Battle Turn: 'Check Battle Turn' node availablel (in 'Battle > Battle' nodes in schematics and 'Base' nodes in formulas and battle AIs). Checks the current battle turn (independent of combatant turns). Please note that battle turns are only used in 'Turn Based' and 'Phase' battles.
- Schematics, Formulas, Battle AIs: Select Item: 'Use Inventory Container' settings available. Optionally select items from a defined inventory container instead of the whole inventory. No items will be selected if inventory containers are not used by the combatant (e.g. feature not used or only for player).
- Schematics: Level Up: 'Show Level Up Dialogue' setting available. Optionally show the standalone level up dialogue (defined in 'Battles > Battle End') when increasing levels.
- Schematics: Level Up: 'Level Change' setting available. You can now increase the level by more than 1, but the minimum increase will be 1.
- Schematics: In Use Range: 'Store Users' and 'Store Targets' settings available. Optionally store the users or targets that are within range into selected data.
- Schematics: Reopen Battle Menu: 'Reopen Battle Menu' node available in 'Battle > Battle' nodes. Reopens the battle menu that started the schematic. Only available if the schematic was started by a battle menu (via 'Schematic' option type).
- Schematics: In Grid Action Range, Select Grid Action Range: 'Grid Move Selection Aware' setting available. The move range takes the currently used grid move selection into account. I.e. the grid move selection user's cell will be passable, but the selected cell will be blocked.
- Schematics: Start Grid Move Selection: 'Start Grid Move Selection' node available in 'Battle > Grid Path' nodes. Starts a grid move selection, allowing the player to select a grid move path. Sets the current action's grid path and can only be used if the schematic was started by a battle action.
- Schematics: Consume Grid Path Cost: 'Current Cell' setting available. Only consume the costs of the current cell (i.e. the next cell that's still stored in the path). The costs will also be removed from the total costs.
- Schematics: Is Battle Action, Filter Selected Combatants: 'Use Ability Type' and 'Use Item Type' settings available. Optionally check for abilities/items of a defined ability/item type.
- Schematics: Add To Inventory, Remove From Inventory, Drop Items, Add To Item Box, Remove From Item Box: 'Set Quantity' settings available when 'Use Selected Data' is enabled. Optionally define the used quantity of the items stored in selected data, e.g. only removing 1 item from the inventory, even if more are stored in selected data.
- Unity UI: HUD Condition Component: 'Player Enemy' condition type available. Checks if the HUD user is an enemy of the player.
- Unity UI: ORK HUD Text Content Component, ORK HUD Status Text Content Component: 'Use Time Update' settings available. Optionally update the content in regular intervals.
- Unity UI: ORK HUD Status Text Component: 'Action Cost' status type available. Shows the action costs of a shortcut. In 'Turn Based' and 'Phase' battles this displays the actions per turn. In 'Active Time' battles this displays the timebar cost.
- Unity UI: Content Provider: HUD Shortcut: 'HUD Shortcut' content provider component available. Uses a defined shortcut of a combatant (e.g. an ability or item) as content for it's HUD components. The HUD's content/user must be a combatant.

Changes:
- UI: Menus: Most menus will now allow showing ok/cancel buttons in any case. The ok/cancel buttons of the UI box handle their display according to their own setup (e.g. hidden by inputs or inactive state). For the 'Unity UI' module, this is handled by the 'UI Botton (Ok, Cancel)' component of the UI box's ok/cancel buttons.
- Rounding: All 'None' roundings have been changed to 'Default', using the default rounding defined in 'Game > Game Settings'. The 'None' rounding is still available (the new default rounding in 'Game > Game Setting' defaults to 'None').
- Menu Screens: Information: Using 'Auto Update' blocks using the typewriter when updating the content of the UI box.
- HUDs: Combatant: The 'Only Battle Group' setting has been replaced with the 'Player Group Scope' setting. Previous settings will be updated automatically.
- Battle System: Turn Based: 'Actions Per Turn' and 'Action Time' are now initialized when the combatant starts a new turn, not when starting to select actions. E.g. using 'Auto Start Turn' to start the new turn at the start of the battle turn will now initialize their 'Actions Per Turn' and 'Action Time' values.
- Battle Menus: Options: 'Conditions' has been renamded to 'Add Conditions'.
- Battle AIs: Selected Data Nodes: 'None' user has been replaced by 'Found Targets'. Uses the currently found targets as user of the selected data operation.
- Quests, Quest Tasks: Experience/Normal Status Value Rewards: 'Whole Group' and 'Only Battle Group' settings have been replaced by a 'Combatant Scope' selection. Previous settings are updated automatically.
- Combatants: Noraml Status Value Rewards: 'Whole Group' and 'Only Battle Group' settings have been replaced by a 'Combatant Scope' selection. Previous settings are updated automatically.
- Formulas: Status Value, Random Status Value, Battle Statistic: 'Whole Group' and 'Only Battle Group' settings have been replaced by a 'Combatant Scope' selection. Previous settings are updated automatically.
- Schematics: Regenerate, Revive, Change Status Value, Change Status Effect, Level Up, Initialize To Level, Check Status: 'Whole Group' and 'Only Battle Group' settings have been replaced by a 'Combatant Scope' selection. Previous settings are updated automatically.
- Schematics: End Turn: If the schematic is animating a battle action and the combatant is the action's user, the combatant's turn will end after the action.
- Unity UI: ORK HUD Text Content Component, ORK HUD Status Text Content Component: 'Update Every' is no longer displayed when a time-based text code is used in the text. Instead it's now displayed (and used when 'Use Time Update' is enabled).

Fixes:
- Abilities, Items: Target Selection: Using 'Self' target type after previously selecting 'None' target range (setting hidden for 'Self') caused an error when using the ability/item.
- Items: Variables: Fixed an issue where 'Use In Custom Content' didn't replace variable text codes in custom content with the item instance's variables.
- Inventory Containers: Changes to the combatant's status didn't have an impact on slot counts.
- Save Games: Shortcut Slots: Fixed an issue where loading a saved game didn't load grid move and grid orientation shortcuts correctly.
- HUDs: Value Bars: Cast Time, Delay Time, Reuse Time, Status Effect Duration: Fixed an issue where the value bar didn't reach full or empty state due to the last time update no longer registering (e.g. cast time ended).
- HUDs: Console: Fixed an issue where the 'Auto Scroll Down' settings where hidden.
- HUDs: Shortcut Slots: Fixed an issue where shortcut slot tooltips didn't show status change previews.
- Battle Menus: Description: Fixed an issue where special actions (e.g. grid move, defend, etc.) didn't provide content for text codes when displaying custom description content.
- Combatant Highlights: HUDs: Fixed an issue where blinking HUDs could be stopped by flashes or other HUD fades and not restored after the other fade finished. E.g. a battle menu's user highlight being stopped by a flash or target selection highlight.
- Camera Controls: Transitions: Fixed an issue where transitioning rotations started from the wrong rotation.
- Battle AIs, Action Targeting: Fixed an issue where using battle AIs in 'Queue' mode could result in all out of range targets being used for 'Single' target range actions. E.g. an ability that should target a single combatant targeted multiple instead.
- Schematics: Determine Action Result: Fixed an issue where blocking wasn't predetermined.
- Schematics: Has In Inventory Fork: Fixed an issue where the node checked for not being in inventory.
- Unity UI: HUD Shortcut Slot Content Component: Fixed an issue where state change colors where overruled by fading in/out of the HUD.
- Unity UI: HUD Shortcut Slot Content Component: Fixed an issue where closing the HUD while displaying a tooltip (cursor over) didn't remove the tooltip.


-------------------------------------------------------------------------------------------------------------------------------------------------------
ORK Framework 3.5.0
-------------------------------------------------------------------------------------------------------------------------------------------------------

New:
- Abilities, Items: User Critical Changes, Target Critical Changes: 'Use Regular Changes' settings available. Optionally use the regular (non-critical) user/target changes with a different damage multiplier (e.g. 2 for double damage, 1.5 for 50% more damage, etc.). You can still add additional status changes that'll be used together with the regular changes.
- Status Values: Flying Texts: 'Source Content' and 'Attribute Source Content' settings available. Optionally use different content for the flying text when the status value change has a source or a source and an attack modifier attribute. Additional text codes are available to add source/attribute content information to the flying texts.
- Status Effect Casts: 'Override Duration' settings available. Optionally override the status effect's time/turn duration value. The status effect's duration setup is used as is, only replacing the duration value. E.g. used by ability or item status changes.
- Status Effect Casts: 'Add Random' setting available when adding by status effect type. Adds a random status effect of the defined type to the target.
- Status Development: Upgrades: The 'Change' setting now uses a float value selection. E.g. allows using formulas or other value types instead of a defined value.
- Combatants, Status Values: 'Rounding' setting available when setting the start value of a status value in 'Percent'. Define if and how the change value will be rounded.
- Combatants, Equipment: Random Status Value Bonuses: 'Rounding' setting available when setting the value in 'Percent'. Define if and how the change value will be rounded.
- Research Trees: Research Item: Status Value: The 'Change' setting now uses a float value selection. E.g. allows using formulas or other value types instead of a defined value.
- Battle System: Group Advantages, Bonus Settings: Status Values: The 'Value' setting of status value changes now uses a float value selection. E.g. allows using formulas or other value types instead of a defined value.
- Battle System: Group Advantages, Bonus Settings: Status Values: 'Rounding' setting available when setting the value in 'Percent'. Define if and how the change value will be rounded.
- Battle Grid Settings: Move Command: 'Cost Status Value' settings available. Grid movement can now change a single defined 'Consumable' type status value. Grid cell types define the cost for that status value when moving to/over their cells.
- Battle Grid Cell Types: Costs: 'Status Value Cost' settings available. Moving to/over grid cells can now change a 'Consumable' type status value. Which status value is used is defined in 'Battles > Battle Grid Settings' in the 'Move Command' settings.
- Battle End: Simple Loot Dialogue: New text codes available in the 'Battle Gain Notification'. Display the total experience and total normal status value gains, combined from all gains of all combatants.
- Inventory Settings: Drop Item Settings: 'Prefab Type' setting available. Defines if the dropped item's prefab is used, either spawned by the item collector ('Collector Spawn') like for a regular item collector, or using it as the drop item's root game object, adding the item collector to it.
- Menu Screens: Status Value Distribution: Accept, Cancel: 'Accept Only Here' and 'Cancel Only Here' settings available in the 'Accept' and 'Cancel' inputs. Optionally only allow accepting/canceling the dialogue via the added input buttons.
- Menu Screens: Status Value Distribution: Accept Question Dialogue: 'Accept Question Dialogue' settings available. Optionally show an accept question dialogue when accepting the status value distribution.
- Menu Screens, Battle Menus, etc.: Content Layout: 'User/Target Changes' text codes available. Display the status changes for the user and target of abilities and items using text codes. The text codes are replaced by the information defined in 'UI > Text Display Settings > Status Change Display'.
- Menu Screens, Battle Menus, etc.: Content Layout: 'Use Count' text codes available. Display the use count and max use count of an ability using text codes.
- Battle Menus: 'Use Default Selection' settings available. Optionally define the index of the choice that will be selected when opening the battle menu.
- Shortcut Settings: 'Assign Once Per List' settings available. Optionally only allow the same shortcut to be assigned once per shortcut list, either preventing adding it again or removing previous assigned slots. The individual types of shortcuts (abilities, items, etc.) are limited with individual settings. E.g. the same ability can only be added once to a list.
- Text Display Settings: Status Change Display: 'Status Change Display' settings available. Define how status changes of an ability/item on the user and target are displayed. E.g. used by user/target change text codes and the 'Target Information Layout' used by target information dialogues (can optionally override it).
- Text Display Settings: Status Preview: 'Grid Move Status Value Cost' setting available in 'Other Action Previews' settings. Shows status value costs from grid move path selections in previews.
- Control Maps: Tooltip: 'Prioritize Tooltip' setting available when a control map key shows the tooltip for the action. Optionally prioritize the tooltip and prevent other tooltips from overruling it.
- Battle AI: Select Selected Data: 'Select Selected Data' node available in 'Selected Data' nodes. Uses the first, last, a random or all content from another selected data as selected data.
- Battle AI: In Grid Action Range: 'In Grid Action Range' node available in 'Position' nodes. Checks if a combatant is within move range and use range of a selected action of the user, a battle range template or a custom range.
- Battle AI: In Use Range: 'Selected Data' use range type available. Uses an ability or item stored in selected data for the use range check.
- Battle AI: In Use Range: 'Check Out Of Range' setting available. Checks for targets being out of range instead of in range.\n
- Battle AI: In Use Range: 'Fail Uses Failed Targets' setting available. Optionally uses the failed targets as found targets if no valid target was found (e.g. storing the out of range targets if no target was in range).
- Battle AI: Grid Move: 'Use Range' range type available in 'Target Cell Settings' and 'Avoid Enemies/Allies' settings. Use the use range of an action (e.g. an ability or item) to determine which cells are used.
- Target Settings: Target Dialogues: Target Information Layout: 'Own Status Change Display' settings available. Optionally override the default status change display texts defined in 'UI > Text Display Settings'. Previous settings are automatically updated to override with the old setup.
- Schematics: In Grid Action Range: 'In Grid Action Range' node available in 'Battle > Grid' nodes. Checks if a combatant/cell is within move range and use range of a selected action of the user, a battle range template or a custom range.
- Schematics: Select Grid Action Range: 'Select Grid Action Range' node available in 'Battle > Grid' nodes. Uses combatants as selected data that are within move range and use range of a combatant's action, a battle range template or a custom range.
- Schematics: In Use Range, Grid Move Out Of Range: 'Selected Data' use range type available. Uses an ability or item stored in selected data for the use range check.
- Schematics: Random Status Effect: 'Random Status Effect' node available in 'Combatant > Status' nodes. Changes a random status effect on a combatant, e.g. adding a random effect or removing a random effect of the combatant. Optionally limited to defined status effect types. While the regular 'Change Status Effect' node can also do this, the 'Random Status Effect' node supports using all effects or multiple effect types.
- Schematics: Change Status Value: 'Rounding' setting available when setting the value in 'Percent'. Define if and how the change value will be rounded.
- Unity UI: HUDs: HUD Ability List: 'HUD Ability List' component available. Displays a list of abilities of a combatant, can optionally be limited to defined ability types and other options (e.g. active/passive abilities, base attacks, etc.).
- Unity UI: HUDs: ORK HUD Status Content: 'User/Target Changes' text codes available in 'Ability' and 'Item' status types. Display the status changes for the user and target of abilities and items using text codes. The text codes are replaced by the information defined in 'UI > Text Display Settings > Status Change Display'.
- Unity UI: ORK HUD Text Content: New text codes available for the general 'ORK HUD Text Content' component. Display quantity, quantity in inventory and general info (e.g. use cost, quantity, etc.) of shortcuts displayed in the HUD (e.g. an item or ability).
- Unity UI Setup: Context Menu: 'Ability List' entries available in 'ORK Framework > HUD > Ability'. Creates 'HUD Ability List' component game objects with different layoug configurations (e.g. vertical layout).
- Combatant Spawner Components: Combatants: 'Formation Settings' available when using combatant groups. Use a defined formation for the group, also affecting the spawn positions of the group members (around the spawned leader).

Changes:
- Abilities, Items: Status Value Changes: Changing status values in 'Percent' no longer casts the calculated percent value to int, only rounding the final result (based on the settings).
- Abilities, Items: The 'Timebar Cost', 'Turn Action Cost' and 'Phase Action Cost' settings have been moved to 'User Settings > Use Cost' settings as 'Override Action Costs' settings.
- Inventory Settings: Drop Item Settings: 'Spawn Prefab' setting has been replaced by the 'Prefab Type' setting. Previous settings will be updated automatically.
- Text Display Settings: The 'Status Texts' foldout has been split up into it's separate sub-foldouts to make the text display settings less crowded.
- Schematics: Switch Equipment: Unequipping from their current equipment slots is now forced, ignoring not allowing unequipping on a slot.
- Battle Grids: Cell Selections: Resetting the camera control target will now also reset the selected cell to the current user's cell.
- Status Values: Change Schematics: The status change information is now available as both 'Int' and 'Float' local variables. Previously it was only available as 'Int' variables.

Fixes:
- Combatants, Menu Screens: Fixed an issue where combatant updates (e.g. updating status after equipment changes) where not processed when switching group members in pausing menu screens.
- Shortcut UI: Special actions (e.g. grid move, defend or escape) didn't use shortcut UI. They now use the 'Default Shortcut UI'.
- Unity UI: HUD Shortcut Slot Content Component: Fixed an issue where the 'State Changes' settings where hidden when not enabling the 'Enable Dragging' setting.
- Editor: Equipment: Fixed an issue where displaying bonuses in the description via the '<bonus>' text code could lead to an error in equipment selection popups if one of the bonuses uses conditions.
- HUDs: Actions Per Turn: Fixed an issue where displaying preview values in the 'Actions Per Turn' HUD elements using 'Invert Used Actions' where using the wrong (non-preview) values.
- HUDs: Turn Order: Fixed an issue where in some cases the order could become wrong.
- Menu Screens: Status Value Distribution: Fixed issues with canceling preventing future status value changes in the menu screen.
- Menu Screens: Status Value Distribution: Fixed an issue with 'Can Decrease' not allocating correct points for reducing a status value below it's initial value.
- Battle System: Fixed an issue where a battle action with no targets in range while not allowing move AI could halt the battle (e.g. in 'Turn Based' type battle systems).
- Attack Modifiers: Flying Text Colors: Unity UI: Fixed an issue where the flying text color override didn't work.
- Add Combatant Component, Combatant Spawner Component, Item Collector Component: Fixed an issue that could cause errors if no player combatant was yet added to the player group.
- Schematics: End Phase: Fixed an issue where the currently selecting combatant was still able to select actions after the action (using the 'End Phase' node) finished.
- Research Trees: Abilities: Learning an ability as a group ability will now also set the research as completed on all group members.


-------------------------------------------------------------------------------------------------------------------------------------------------------
ORK Framework 3.4.0
-------------------------------------------------------------------------------------------------------------------------------------------------------

New:
- Combatants: Reaction Portraits: 'Reaction Portrait' settings available. The reaction portrait of a combatant can change the used portrait type based on things happening, e.g. a status value change or a status effect being applied. Reaction portraits can be displayed in HUDs via 'Reaction Portrait' elements (depending on the UI system). Default settings for all combatants are in the general settings of combatants, each combatant can override them in their UI information override settings. 
- Attack Modifiers: 'Strength Flying Text', 'Weakness Flying Text' and 'Recovery Flying Text' settings available. Optionally override status value flying texts for attack modifier attributes that are recognized as a strength, weakness or recovery attribute of the combatant. Can be set up for all attributes of the attack modifier, each attribute can additionally define it's own settings.
- Abilities, Items: Status Value Changes: The 'Efficiency' setting now uses a float value selection. E.g. allows using formulas or other value types instead of a defined value.
- Abilities, Items, Status Effects: Status Value Changes: The 'Percent' setting for absorbing damage now uses a float value selection. E.g. allows using formulas or other value types instead of a defined value.
- Inventory Settings: Drop Item Settings: 'Prefab Settings' available. Define if the prefab of the dropped item will be spawned. By default enabled (previous behaviour).
- Inventory Settings: Drop Item Settings: 'Destroy Object' setting available. Define if the dropped item's game object will be destroyed after collection. By default enabled.
- Inventory Settings: Drop Item Settings: 'Show Dialogue' setting available. Define if the item collection dialogue is shown. By default enabled.
- HUDs: Shortcut Slots: Dragging Notification: 'Dragging Notification' settings available when shortcut slots allow dragging. Optionally use a custom dragging notification to display dragged content coming from the shortcut slot (e.g. via the 'HUD Shortcut Slot Content' component in the Unity UI system). The default dagging notification is set up in 'UI > UI Settings'.
- Start Menu: Menu Options: Exit: 'Close Application Question' settings available. Optionally display a question dialogue before closing the application.
- Start Menu: Menu Options: 'Schematic' option type available. Starts a schematic, optionally closing the start menu. Use a 'Call Start Menu' node to open the start menu again (in case it's closed).
- Start Menu: Menu Options: 'Global Machine' option type available. Starts a global machine, optionally closing the start menu. Use a 'Call Start Menu' node to open the start menu again (in case it's closed).
- Menu Screens: Group Combatant: 'Close If Empty' setting available. Optionally close the group combatant part when no combatant is displayed.
- Menu Screens: Combatant, Group Combatant: Entry Page: 'Add Tooltip' setting available. Optionally add the combatant as tooltip content. Also available in other combatant displays with 'Entry Page' settings (e.g. shops).
- Menu Screens: Combatant: 'Show Selected Combatant' setting available when displaying 'Current' scope (i.e. the menu user). Optionally show the menu screen's currently selected combatant from an open combatant selection or another 'Combatant' menu part used to select combatants. When no combatant selection is active, displays the menu's current user.
- Menu Screens: Sub-Menus: 'At Input Position' settings available. Optionally place the sub-menu's UI box based on the position of the selected input that called the sub-menu. Sub-menus are available in 'Ability', 'Equipment', 'Inventory' and 'Inventory Container' menu parts.
- Menu Screens: Menu Actions, Sub-Menus: 'Merge' menu action type available. Merges a stack of items/equipment/etc. with another matching stack. The merging stack can optionally be highlighted in a color while selecting the other stack. Currently only supported in 'Inventory', 'Inventory Container' and 'Equipment' menu parts.
- Menu Screens: Menu Actions, Sub-Menus: 'Move (Inventory Container Slot)' menu action type available. Moves a stack of items/equipment/etc. to another inventory container slot (or swaps with it's content). The moving stack can optionally be highlighted in a color while selecting the other stack. Currently only supported in 'Inventory Container' menu parts.
- Menu Screens: Menu Actions, Sub-Menus: Assign Shortcut: 'Use Assigned Button' settings available. Optionally use a different button content setup when a slot is already assigned with a shortcut. Allows showing content information or use 'Shortcut UI' or HUD templates to display information about the assigned shortcut.
- Shop Layouts: Purchase Mode Settings: 'Purchase Mode' settings available. Define what happens when purchasing items, equipment, AI behaviours or AI rulesets. 'Buy' will simply add the purchased content to the inventory, 'Buy And Use' opens a combatant selection to use it after storing it in the inventory (e.g. equipment will be equipped, items used, etc.).
- Text Display Settings: Combatant Choice Layouts: 'Add Tooltip' setting available. Optionally add the combatant as tooltip content. Also available in all combatant choice override settings (e.g. combatant selections).
- Battle AIs: Check Action Time: 'Check Action Time' node available in 'Combatant' nodes. Checks a combatant's action time.
- Schematics: Combatant Reaction Portrait: 'Combatant Reaction Portrait' node available in 'Combatant > Combatant' nodes. Changes the reaction portrait of a combatant for a defined time.
- Schematics: Sort Selected Combatants: 'Sort Selected Combatants' node available in 'Value > Selected Data' nodes. sort combatants stored in selected data by defined metrics, e.g. by distance to a position or a status value.
- Schematics: Select Combatant Objects: 'Distance Sort' settings available. Optionally sort the selected combatants by their distance to the user/position.
- Schematics: Change Task Enemy Kill Count: 'Change Task Enemy Kill Count' node available in 'Game > Quest' nodes. Changes the current value of a task's enemy kill requirements.
- Schematics: Change Task Item Count: 'Change Task Item Count' node available in 'Game > Quest' nodes. Changes the current value of a task's item requirements.
- Schematics: Change Task Count: 'All Tags' setting available. Optionally change the count of all tags instead of a defined tag.
- Schematics: Start Item Collector: 'Only Set Collected' setting available. Optionally only set the item as collected instead of actually collecting it.
- Schematics: Start Item Collector: 'Scope' setting available. Define the scope where the item collector will be searched on the used game object (e.g. in child objects).
- Schematics: Add Temporary Cell Event: 'User Combatant' settings available. Optionally define a user object that'll be used as the user of the cell event instead of creating a new combatant. Will be used by 'Ability' and 'Status Effect' events as the user and for all event types for enemy/ally checks.
- Schematics: Get Combatant Gains: 'Loot Storage' settings available. Define where the loot from the used combatant will be stored into. Can be stored in the 'Battle Loot' (previous behaviour), an item box via 'Item Box ID', an item box via a game object's item box ('Item Box Object') or into 'Selected Data'.
- Schematics: Create Item Box: 'Create Item Box' node available in 'Inventory > Item Box' nodes. Adds an 'Item Collector' component using 'Box' collection type to a game object, optionally using and setting a box ID.
- Schematics: Start Grid Cell Selection: 'Start Grid Cell Selection' node available in 'Battle > Grid' nodes. Starts a grid cell selection, allowing the player to select a grid cell similar to target cell selections. The selected cell(s) will be stored in selected data.
- Schematics: Change Action Time: 'Use Maximum' setting available. Changes the maximum action time instead of the current action time.
- Schematics: Check Action Time: 'Use Maximum' setting available. Checks the maximum action time instead of the current action time.
- Unity UI: HUDs: HUD Combatant Reaction Portrait: 'HUD Combatant Reaction Portrait' component available. Displays a combatant's reaction portrait and updates when it changes.
- Unity UI Setup: Context Menu: 'Combatant Reaction Portrait' entries available in 'ORK Framework > HUD > Content'. Creates 'HUD Combatant Reaction Portrait' component game objects with different portrait configurations.
- Item Collector Components: 'Mount On Prefab' setting available when using 'Mount Prefab' settings. Optionally mount the item collector on the spawned prefab instead of the prefab on the item collector.
- Scripting: Combatants: 'AttackedByCombatant' event handler available to register in the combatant's 'Battle' property. Notifies listeners when the combatant was attacked by another combatant, providing the attacker as an argument. Will only be fired the first time each combatant attacks (i.e. previously unknown attacker).
- Scripting: Factions: Event handlers available to get notified of faction changes. 'MemberWasKilled', 'ItemWasTaken', 'CurrencyWasTaken' and 'SympathyChanged' handlers  provide the faction that was changed and the faction that changed as arguments. Faction sympathy and the event handlers can be accessed via 'ORK.Game.Faction' in a running game.
- Scripting: 'ITargetRaycastCursorPrefab' interface available. Used by raycast targeting to pass on the user and action to raycast cursor prefab instances.
- Scripting: 'ITargetSelectionCursorPrefab' interface available. Used by target selections (combatant highlights) to pass on the user, selected target and action to target cursor prefab instances.
- Scripting: 'ICombatantHighlightCursorPrefab' interface available. Used by combatant highlights (including target selections) to pass on the selected combatant to cursor prefab instances.
- Affect Range Scale Target Raycast Cursor Component: 'Affect Range Scale' target raycast component available. Implements the 'ITargerRaycastCursorPrefab' interface and changes the game object's scale based on the affect range of the raycast action.
- Affect Range Scale Target Selection Cursor Component: 'Affect Range Scale' target selection component available. Implements the 'ITargetSelectionCursorPrefab' interface and changes the game object's scale based on the affect range of the action.
- Radius Scale Combatant Highlight Cursor Component: 'Radius Scale' combatant highlight component available. Implements the 'ICombatantHighlightCursorPrefab' interface and changes the game object's scale based on the radius of the highlighted combatant.
- Use Range Scale Combatant Highlight Cursor Component: 'Use Range Scale' combatant highlight component available. Implements the 'ICombatantHighlightCursorPrefab' interface and changes the game object's scale based on the use range of the user's currently selected action (in the battle menu).

Changes:
- Save Game Settings: The 'No Auto Remove Scene' settings have been moved to the Makinom save data (i.e. now part of Makinom's features). Your old settings will automatically be updated.
- Schematics: Change Task Count: You can now also change all tasks of a quest, quest type or all tasks instead of only a defined task.
- Scripting: The 'IComponentSaveData' interface has been moved from ORK to Makinom. Use the namespace 'GamingIsLove.Makinom' instead of 'GamingIsLove.ORKFramework' to access it.

Fixes:
- Menu Screens: Combatant: Fixed an issue where using the 'Combatant' menu part as a combatant selection could throw an error when canceling out of selecting combatants.
- Menu Screens: Group Combatant: Fixed an issue where the 'Group Combatant' menu part wasn't closed when it's displaying battle group or non-battle group box was closed.
- Menu Screens: Status Value Distribution: Fixed an issue where a status value's input couldn't be decreased to their initial value when 'Can Decrease' is disabled.
- Menu Screens: Equipment: Fixed an issue where equipping the last equipment, leaving only the 'Unequip' button, caused the unequip preview to remain active.
- Menu Screens: Equipment: Fixed an issue where a sub-menu could be opened for the 'Unequip' button.
- HUDs: Shortcut Slots: Fixed an issue where shortcuts from non-slot sources (e.g. a menu screen) where not assigned.
- Combatant Spawners: Fixed an issue where using 'Remember Combatants' and 'Use Appearing Chance' was using the appearing chance for remembered combatants instead of only for the first spawn.
- Status Effects: Fixed an issue where removing a status effect that removes other effects on remove could lead to stack overflow exceptions.


-------------------------------------------------------------------------------------------------------------------------------------------------------
ORK Framework 3.3.1
-------------------------------------------------------------------------------------------------------------------------------------------------------

Changes:
- Schematics: Change Status Effect: The 'Change Status Effect' node's info text now shows more details about the status effect changes.

Fixes:
- Schematics: Select Combatant Objects: Fixed an issue where using 'All' user combatants wasn't using any combatant, selecting no game objects.
- Editor: Status Effect Casts: Fixed an issue where using 'Remove' or 'Toggle' didn't show all available settings.
- Battle Menus: Fixed an issue where battle menu overrides from combatants or classes didn't change the battle menu when it was already initialized before (which always happens).
- Battle Menus: Fixed an issue where some commands (e.g. 'Grid Move' or 'Defend') didn't display tooltips.
- Battle Components: Fixed 'Battle' components throwing an error when they are started without defined combatants.


-------------------------------------------------------------------------------------------------------------------------------------------------------
ORK Framework 3.3.0
-------------------------------------------------------------------------------------------------------------------------------------------------------

New:
- Game Controls: Group Member Keys: 'Keep Scale' setting available. Spawning a new player will keep the scale of the currently spawned player. Only used when a new player is spawned (and an old is still present), but used in all cases that changes the player (i.e. not limited to the group member keys).
- Player Controls: Top Down 2D: 'Rotate Z Axis' setting available. Optionally set the Z-axis rotation of the player's game object using the movement direction.
- Player Controls: Button: Animator Root Motion: 'Use Damping' settings available for forward/vertical speed parameters. Optionally use a damp time to change to the new value over time.
- Animations: Mecanim: 'Use Damping' settings available for all auto parameters setting float parameters. Optionally use a damp time to change to the new value over time.
- Animations: Mecanim: 'Set Float Parameter' setting available for 'Auto Rotation Parameters'. Optionally set a float parameter instead of an int parameter for 'Direction 4' and 'Direction 8' rotations types. E.g. using blend trees only supports using float parameters.
- Menu Screens: Menu Actions: Use, Give: 'Use Combatant Part' setting available. Optionally use a 'Combatant' menu part as combatant selection for 'Use' and 'Give' target selections (e.g. in 'Inventory' menu parts).
- Cursor Settings: Default Cursor: 'Reset to System Default' setting available. Reset to the system default cursor when no default cursor is found.
- Status Bonuses: Attack/Defence Modifier Bonuses: 'Change Factor' settings available. Optionally use a change factor to multiply an attribute's value with. E.g. a factor of 0 will result in immunity to an attribute (i.e. value of 0), 0.5 will halve the value, 2 will double the value. Available in ann attack/defence modifier bonus settings.
- Text Display Settings: Bonus Display: Attack/Defence Modifier Bonus Display: 'Immunity Text' and 'Factor Value Text' settings available. Define the texts used to display immunity (factor 0) and factor changes (i.e. any factor not 1 or 0).
- Status Effects: Status Value Changes: 'Min/Max Change' settings available. Optionally limit a status value change to a minimum or maximum change value. The limit will be used before attack/defence modifiers.
- Inventory Settings: Drop Item Settings: 'Condition Settings' available for the item collector setup. Define the conditions that have to be valid to collect drop items.
- Abilities, Items, Battle System: Reuse Time: 'Multi Use' setting available. Optionally allow using multiple reuse times. If disabled only the first available reuse time (for the current battle system) will be used.
- Shops: Equipment: 'Sell Only Equipable' setting available. Only equipment that can be equipped by the current shop user (combatant) can be purchased.
- Turn Based Battles, Phase Battles: Battle Menu Call: 'Reopen In Turn' setting available when not auto calling the battle menu. Optionally reopen the battle menu when a combatant can continue to select actions during it's turn.
- Battle AIs: Use Last Targets, Use Attacked By, Use Killed By, Use Combatant: 'Selected Data' target origin available. Use combatants stored in selected data to find targets.
- Battle Grid Settings: Move Command: 'User Cell Cancels' setting available when using 'Allow Cancel'. Selecting the user's cell will cancel the cell selection.
- Battle Grid Highlights: Highlights are now an extensible system. You can add different highlights as needed, 'Prefab', 'Blink' and 'Line Renderer' highlight types are available ('Blink' now also supports using different properties). Add custom highlights by extending from the 'BaseGridHighlightType' class. Previous settings are automatically updated.
- Combatant Highlights: Highlights are now an extensible system. You can add different highlights as needed, 'Prefab', 'Fade Color' and 'Fade HUD Color' highlight types are available ('Fade Color' now also supports using different properties). Add custom highlights by extending from the 'BaseCombatantHighlightType' class. Previous settings are automatically updated. E.g. used by target highlights or battle menu user highlights.
- Schematics: Join Group, Join Battle Group, Join Battle Reserve, Join Battle: 'Store Selected Data' settings available. Optionally store the combatant that joined into selected data.
- Schematics: Leave Group, Leave Battle Group, Leave Battle Reserve, Leave Battle: 'Store Selected Data' settings available. Optionally store the combatant that left into selected data.
- Schematics: Change Battle Group: 'Change Battle Group' node available in 'Group > Battle Group' nodes. Changes a combatant in the battle group for another combatant (not in the battle group).
- Schematics: Check Equip Item Type: 'Check Equip Item Type' node available in 'Inventory > Equipment' nodes. Checks the item types of equipment currently stored in selected data.
- Schematics: Check Equip Status Value, Store Equip Status Value: 'Include Bonuses' settings available. Optionally include status value bonuses coming from the equipment's custom bonus and status bonus templates. Otherwise only uses the status values added directly to the equipment instance (e.g. via random bonuses of changed by schematics).
- Schematics: Add Combatant: 'Use Selected Data' settings available. Optionally use combatants stored in selected data and add them to a game object instead of creating a new combatant.
- Schematics: Filter Selected Combatants: 'Filter Selected Combatants' node available in 'Value > Selected Data' nodes. Filters combatants stored in selected data and only keeps combatants matching defined conditions. E.g. check for being enemy of another combatant, distance to a position, waiting for or performing an action and various other conditions.
- Schematics: Select Combatant Objects: 'Store Combatants' setting available. Optionally store the combatants instead of their game objects into selected data.
- Schematics: Is Battle Action: 'Action Check' settings available. Optionally check for the schematic being started by a defined battle action instead of any battle action.
- Schematics: Drop Items: 'Use Selected Data' settings available. Optionally drop items stored in selected data instead of defined items.
- Schematics: Dialogue Nodes: 'In Pause' setting available. Defines if a UI box can be controlled when the game is paused. Defaults to 'Auto', allowing control if the game is paused when displaying the UI box.
- Scripting: Item Collector Component: The new 'PrefabInstance' property gives access to the (possibly) spawned prefab instance (game object) of the item.
- Scripting: Access Handler: New access handler functions available for resetting and changing the durability of an equipment.
- Unity UI: Content Provider: HUD Quest: 'HUD Quest' content provider component available. Uses a defined quest as content for it's HUD components.
- Unity UI: ORK HUD Value Bar Content Component: Actions Per Turn: 'Max Action Bar' setting available. Optionally use the maximum actions per turn as the value bar's maximum value instead of the current action's per turn.
- Inpector: ORK Handler: The ORK handler inspector (on the '_Makinom' game object) now shows the number of battle actions currently queued (waiting to execute), casting and active in the battle system.

Changes:
- Menu Screens: Combatant Settings: The settings for available users are now independent from allowing changing the menu user, since the settings also impact the available combatants in combatant selections for opening the menu screen.
- Target Settings, Abilities, Items: Target Raycast: Mouse an input key cursor changes now update the same cursor position, mouse changes are only used if the mouse is moved (i.e. no longer overriding input key changes when input stops).
- Schematics: Actors: 'User Base', 'Target Base' and 'Target Center' type actors are now supported in schematics not used to animate battle actions. The 'User Base' can be used if the 'Machine Object' of the schematic is a combatant. The 'Target Base' and 'Target Center' can be used if the 'Starting Object' of schematic is a combatant (or multiple combatants).
- Schematics, Formulas: Status Fork: The 'Status Fork' node's next slots now show which condition they are used by.
- Schematics, Formulas, Battle AI: Check Status: The 'Check Status' node's info text now shows more details about the set up status conditions.
- Editor: Animations: Mecanim: Loading animations from a prefab (via it's 'Animator' component's animator controller) now uses a selected animator controller directly to load animations. Supports animator controllers and animator override controllers.
- HUDs: Unity UI: ORK HUD Text Content, ORK HUD Status Text Content: Displaying current area information via text codes will now automatically update the text when the current area changes.
- Battles: Change Member: Changing a group member during battle will not replace the old combatant being a target in actions waiting to be executed and casting. Already performing actions will not use the new combatant.

Fixes:
- Battle AI, Formulas, Schematics: Select Combatant: Fixed an issue with the checked combatant when using filters. This changes the object selection in 'Combatant Status', 'General Condition Template' and 'Variable' conditions (when using 'Object' variable origins). Please note that you might need to redo the selection in some cases. The checked combatant is the 'User' object.
- Turn Based Battles, Status Effects: Fixed an issue where using 'Auto Start Turn' in a 'Turn Based' type battle system caused to reduce 'Turn Start' effects by 2 turns.
- Active Time Battles: Fixed an issue where the battle could be stalled if a casted action and a combatant starting to select actions appeared at the same time.
- Inventory Settings: Drop Item Settings: 'Place On Ground' didn't work.
- Shops: Shops now correctly support item stacking.
- Battle Menus: Fixed an issue where using a 'Battle Menu' option with a battle menu that called other sub-menus (e.g. abilities or items) stopped back/cancel from working.
- HUDs: Console: Fixed an issue where toggling the HUD on/off could lead to content not being displayed.
- Menu Screens: Research: Fixed an issue where the tree/item box didn't display the title settings when enabling 'Show Title'. This caused an additional error in-game due to the not set up settings.
- Menu Screens, Shops: Fixed an issue where using 'Block Control' didn't block the controls.
- Menu Screens: Fixed an issue where displaying all types while merging sub-types and displaying empty types didn't work, displaying all types and sub-types instead.
- Battle AI: Grid Move: Fixed an issue where the 'Reachable' next slot was never used.
- Add Combatant Component: Move AI: Fixed an issue where using 'Own Move AI' was hiding the move AI selection instead of showing it.
- Control Maps: Fixed an issue where using 'Use Cursor Over Target' on abilities/items with 'None' target range had the target below the cursor twice in battle animation schematics.
- Combatant Spawners: Fixed an issue where teleport battles immediately respawned combatants instead of using their respawn time when returning to the scene.
- Abilities, Items: Status Changes: Status Value: Fixed an issue with attack modifier damage calculation types not being used correctly with multiple attributes of the same modifier.
- Battle Grids: Change Member: Fixed an issue where changing members in battle (via battle menu command) didn't set the combatant's grid cell.
- Editor: Battle AIs: Fixed an issue where removing a battle AI didn't update the node editor to display the next selected battle AI.
- Save Games: Research Trees: Fixed an issue where a combatant's research trees where not loaded correctly.
- Schematics: Select Loot: Fixed an issue where loot settings where hidden.
- Auto Machines, Interactions: Notify Start: Fixed an issue where getting notified by the 'Machine Object' combatant didn't work.


-------------------------------------------------------------------------------------------------------------------------------------------------------
ORK Framework 3.2.0
-------------------------------------------------------------------------------------------------------------------------------------------------------

New:
- Makinom: Includes and requires Makinom 2.3.0.
- Unity: Domain Reload: Entering play mode without reloading the domain is now supported. You can find Unity's play mode settings in the Unity menu 'Edit > Project Settings... > Editor'.
- Inventory Settings: Item Collection: 'Default Start Settings' available. Optionally set up default start settings for newly created 'Item Collector' components.
- Combatants: AI Settings: Aggression: 'React Move AI' setting available when reacting to group or faction aggression. The combatant's move AI will react to the aggressor and detect it when using 'Detect On Aggression' (move AI settings).
- Combatants: AI Settings: Aggression: 'Notify Others' setting available when reacting to group or faction aggression. Further notifies other group/faction members when becoming aggressive due to reacting to another combatant's aggression state change.
- Move AIs: Waypoint Settings: 'Start in Waypoint Mode' setting available. Optionally start following waypoints when spawned - otherwise starts with an idle schematic. By default enabled (starting with waypoints).
- Move AIs, Battle Range Templates, Group/Individual Target Settings: Line Of Sight: 'Offset' settings available for user and target game objects. Optionally add an offset to the position of the user's and target's game object for the raycast.
- Schematics: Select Item Collector: 'Select Item Collector' node available in 'Value > Selected Data' nodes. Uses items from 'Item Collector' components as selected data.
- Schematics: Change Aggression State: 'Notify Others' setting available. Notifying other group/faction members is now optional. By default enabled (previous behavoiur).
- Schematics: Change Aggression State: 'Aggressor' settings available when using 'Is Aggressive'. Optionally use a defined combatant as the aggressor, allowing the combatant's move AI to react to the aggressor.
- Unity UI: Content Provider: HUD Combatant Object: 'HUD Combatant Object' content provider component available. Uses the combatant from a game object (or the content provider's own game object) as content for it's HUD components. E.g. use this to add a world space canvas to a combatant's game object.
- Unity UI: Content Provider: HUD Combatant Status Effect: ' HUD Combatant Status Effect' content provider component available. Uses a defined status effect of a combatant as content for it's HUD components. Needs a combatant provided to it, e.g. from it's parent HUD.
- Unity UI Setup: Context Menu: 'Text+Icon Content', 'Text Content' and 'Icon Content' entries available in 'ORK Framework > HUD > Content'. Creates 'ORK HUD Text Content' component game objects with text and icon, text only or icon only configuration.
- HUD Click Component: 'Use Shortcut' click type available. Tries to use the HUD content's shortcut. E.g. combine a 'HUD Click' component with a 'HUD Combatant Ability' or 'HUD Combatant Item' component to use the displayed ability/item on click.
- ORK HUD Status Text Component: '<quantityinventory>' text code avaialble in 'Item', 'Equipment' and 'Shortcut' status types. Displays the total quantity of an item, equpipment, etc. in the user's (or player's) inventory.

Changes:
- Player Controls: Mouse: 'Auto Stop Move' now clears the movement target when the control is disabled.
- Face Camera Component: Facing the camera is now handled in 'LateUpdate'.
- HUDs: Combatant Object: Now shows the 'Auto Display' setting to manage if the HUD is displayed or not. Please note that this is disabled by default, i.e. you have to enable it in your 'Combatant Object' HUDs to display them.
- HUDs: Object: Now shows the 'Auto Display' setting to manage if the HUD is displayed or not. Please note that this is disabled by default, i.e. you have to enable it in your 'Object' HUDs to display them.

Fixes:
- Editor: Game Settings: Fixed an issue where not using bestiary was hiding the 'Game Over' settings.
- Equipment: Durability: Fixe an issue where 'None' outworn action didn't keep the bonuses and abilities of the equipment accessible.
- Unity UI: ORK HUD Text Content: Fixed an issue when displaying an item in 'ORK HUD Text Content' components in a HUD.
- Battle End: Loot Dialogue: Layout Screen: Fixed an issue that caused an error when displaying loot notifications.
- Battle End: Loot Dialogue: Layout Screen: Fixed an issue where not closing the loot dialogue could lead to not closing all UI boxes.
- Status Values: Experience: Fixed an issue where using 'Count To Value' for 'Experrience' type status values starting 'From Minimum' lead to backwards counting.
- Battle Grids, Battle Range Templates: Fixed issues with rotating grid masks in 2D grids.
- Move AI: Fixed an issue where not using idle schematics resulted in not patroling.


-------------------------------------------------------------------------------------------------------------------------------------------------------
ORK Framework 3.1.0
-------------------------------------------------------------------------------------------------------------------------------------------------------

New:
- Makinom: Includes and requires Makinom 2.2.0.
- Player Controls: Button: 'Use 2-Way Speed' settings available. Optionally use either walk or run speed of the combatant, depending on the input being below a defined value. Doesn't scale the speed based on the input value.
- Camera Controls: Top Down Border: 'Border Camera Edge' setting available. Optionally stop when the camera's edge reaches the border instead of the camera's position (center).
- Animations: Mecanim: 'Auto Move Direction Parameters' settings available. Optionally forward the movement direction of the combatant to float parameters of the animator controller. The X-, Y- and Z-axis are set up separately (each can be enabled on it's own).
- Inventory Settings: Drop Item Settings: 'Local Space' settings available in the 'Offset Settings'. Optionally use the offset in local space of the combatant dropping the item. By default enabled.
- Inventory Settings: Drop Item Settings: 'Item Collector Settings' available. Define some settings of the 'Item Collector' component used by drop items.
- Battle Range Templates: Grid Shape: Line Of Sight: 'Ignore Dead' settings available for allied and enemy combatant line of sight checks. Dead combatants can optionally not block the line of sight.
- Combatants: General Settings: 'Object Variable Settings' available. Define default object variable settings for all combatants. Each combatant can optionally override the default settings.
- Combatants: Portraits: 'Status Conditions' and 'Variable Conditions' available for portrait sets. Optionally use conditions to determine which portrait set is used, similar to conditional prefabs.
- Combatants: Portraits: 'Use Random Portrait Set' setting available. Using a random portrait set is now optional - this'll ignore the status/variable conditions of portrait sets. By default enabled (previous behaviour).
- Move AIs: 'Allow During Actions' setting available. Optionally allow using the move AI while the combatant is performing an action. Casting actions is still controlled by the cast time's 'Move While Casting' setting. By default disabled (i.e. previous behaviour, blocking move AI during action).
- Move AIs: Group Settings: 'Use Leader Position' setting available When using 'Follow Leader'. Follows the leader's position instead of the in-range position (based on the follow range). By default disabled (change to the previous default behaviour).
- Target Settings: 'Default Target Raycast' settings available. Define the default target raycast settings used by abilities/items using the 'None' target range (and using raycast targeting). Each ability/item can replace them with a custom setup.
- Battle Grid Highlights: 'Selected Target' highlight settings available. Optionally use a separate highlight on the currently selected targets during target selections. Separate settings for player, ally and enemies available.
- Quest Settings: 'Default Quest Status Icons' and 'Default Task Status Icons' settings available. Define default status icons for quests/tasks (e.g. unavailable, inactive, etc.). Each quest type, quest and quest task can override these icons.
- Quests: Quest Status Icons: The 'Quest Status Icons' settings are now an information override setting. New quests will have them disabled (using the default icons), existing quests will automatically update their data based on your setup.
- Quests: 'Task Status Icons' settings available. Optionally override the default task status icons defined in the quest general settings.
- Quest Types: 'UI Settings' available. Optionally override the default 'Quest Status Icons', 'Task Status Icons', 'Quest Layout', 'Notifications' and 'Console Texts' for quests of a quest type. Each individual quest can still override these settings.
- Status Effect Types: 'UI Settings' available. Optionally override the default 'Effect UI', 'Flying Texts' and 'Console Texts' for status effects of a status effect type. Each individual status effect can still override these settings.
- Ability Types: 'UI Settings' available. Optionally override the default 'Shortcut UI, 'Number Format', 'Battle Info Text', 'Notifications' and 'Console Texts' for abilities of an ability type. Each individual ability can still override these settings.
- Item Types: 'Item UI Settings' available. Optionally override the default 'Shortcut UI', 'Number Format', 'Battle Info Text', 'Notifications' and 'Console Texts' for all items of an item type. Each individual item can still override these settings.
- Item Types: 'Equipment UI Settings' available. Optionally override the default 'Shortcut UI', 'Number Format', 'Notifications' and 'Console Texts' for all equipment of an item type. Each individual equipment can still override these settings.
- AI Types: 'AI Behaviour UI Settings' available. Optionally override the default 'Shortcut UI', 'Number Format', 'Notifications' and 'Console Texts' for all AI behaviours of an AI type. Each individual AI behaviour can still override these settings.
- AI Types: 'AI Ruleset UI Settings' available. Optionally override the default 'Shortcut UI', 'Number Format', 'Notifications' and 'Console Texts' for all AI rulesets of an AI type. Each individual AI ruleset can still override these settings.
- Crafting Types: 'UI Settings' available. Optionally override the default 'Shortcut UI', 'Number Format', 'Recipe Layout', 'Notifications' and 'Console Texts' for all crafting recipes of a crafting type. Each individual crafting recipe can still override these settings.
- Research Types: 'UI Settings' available. Optionally override the default 'Notifications' and 'Console Texts' for all research trees of a research type. Each individual research treee can still override these settings.
- Schematics, Battle AI: Change Move AI Stop Angle: 'Change Move AI Stop Angle' node available in the 'Move AI' nodes. Changes or resets the stop angle of a combatant's move AI. Supports float value selections, e.g. to use a random value. The stop angle is used by hunting and caution modes to optionally move to a specific position around the target.
- Schematics: Remove From Inventory: 'Store Dropped' settings available when dropping items. Optionally store the game objects of dropped items in selected data.
- Schematics: Drop Items: 'Store Dropped' settings available. Optionally store the game objects of dropped items in selected data.
- Schematics: Check Battle Outcome: 'Check Battle Outcome' node available in 'Battle > Battle' nodes. Checks the outcome of the current battle and executes the corresponding next node (e.g. 'None', 'Victory', etc.).
- Battle Grids: Inspector: 'Show Deployment Info' setting available. Highlights deployment cells with a green handle and a small label who can deploy (e.g. 'P' for player, 'E' for enemy or 'X' for all).
- Battle Grids: Inspector: 'Shift Cell Type' setting available when using 'Paint' mode. Allows painting a different cell type while holding shift.
- Battle Grids: Inspector: Holding 'CTRL' while in 'Paint' mode allows picking a cell's cell type for painting. Holding shift will pick the 'Shift Cell Type'.
- Battle Grids: Inspector: The handles of cells will be colored differently while painting. If the cell already has the selected paint cell type (or shift type while holding shift), the handle will be blue, otherwise grey.

Changes:
- Abilities, Items: Target Selection: Raycast: The 'Use Target Raycast' setting has been replaced by a selection. Either use 'None' (no target raycast), 'Default' (the default target raycast settings defined in 'Battles > Target Settings') or 'Custom' (a custom raycast setup). Previous settings will be updated to 'Custom' when raycast targeting was used.
- Combatants: Object Variables: The 'Object Variable Settings' are now an override setting for the default setup in the combatant general settings. Previous setup will be updated automatically to using custom settings if 'Use Object Variables' was enabled.
- Move AIs: Following the group leader now defaults to moving to a position based on the follow range instead of the leader's position. To return to the previous behaviour, enable 'Use Leader Position' in the 'Group Settings' of the move AI.
- Battle Components: The 'Chance' setting of combatants is now also used for 'All Groups/Combatants' or when not using 'Use Chance Selection'. A combatant/group will not be used if the chance check fails. Previously the 'Chance' setting was exclusively for the 'Use Chance Selection' mode.
- Battle Grids: Improvements for grid painting in 2D mode, now showing the brush cursor when no collider was hit (e.g. due to walkable tilemap not having colliders).
- Menu Screens: Group: The 'Group Content Layout' settings have been renamed to 'Content Layout (Non-Combatant)' to better state their purpose. They're only used for the empty and back buttons, as the combatant buttons are handled by the 'Combatant Choice Layout' settings of the menu part.

Fixes:
- Equipment: Random Status Value Bonuses: Fixed an issue where random status value bonuses where lost when equipping an equipment or loading a save game.
- Editor: Filters: Fixed issues with some type filters (e.g. crafting recipes) when displaying filtered data lists.
- Player Controls: Top Down 2D: Fixed an issue where moving diagonally allowed faster movement.
- Camera Controls: Control Target transition: Fixed issues with target transitions not working correctly.
- Battle Grids: Generation: Fixed an issue with separate block/empty cell raycasts hitting more than they should in 2D environments.
- Battle Menus: Sub-Menus: Fixed a potential issue in 'Multi Mode' keeping one of the sub-menu boxes open when closing the battle menu.
- HUD Shortcut Slot List Component: Fixed an issue where 'Defined' list type didn't show any slots.


-------------------------------------------------------------------------------------------------------------------------------------------------------
ORK Framework 3.0.0
-------------------------------------------------------------------------------------------------------------------------------------------------------

New:
- Initial release. Everything shiny and new.

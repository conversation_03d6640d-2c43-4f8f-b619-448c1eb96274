# StateScript Design

Design to be extensible for custom state logics.

## Namespace: StateScript

## Key Classes:

Runtime:

- ISerializable
  base interface for serialization

- ISerializer
  base interface for serialization

- JsonSerializer
  json serializer

- BinarySerializer
  binary serializer

- StateNode : ISerializable
  base class for all nodes
  contains a StateNodeProperty and StateNodeEditorProperty
  base function: OnEnter, OnUpdate, OnExit

- StateNodeProperty
  Maybe contains the ports info? Id is needed. Id is unique in the StateFlow.

- StateNodeEditorProperty
  Editor property only, can store datas like positions in the graph, name, description, etc.

- StateActionNode : StateNode
  Contains a List of StateAction, this node act like a duration node, but it don't actually have a duration, it's duration is defined by each StateAction.
  When all StateAction is completed, the node is completed.

- StateConditionNode : StateNode
  base node that can be used to evaluate a condition. maybe we can extend a StateBranchNode from this.
  This node can have multiple out ports.

- StateListenerNode : StateNode

- StateFlow(or Graph) : ISerializable
  This is the main class that contains all the nodes and connections.
  It has a list of Nodes, Connections, Variables. We need to specify the entry node.
  It also reference to the StateContext.

- StateVariable : ISerializable
  Base class for variables
  Maybe have a name property.
  To avoid boxing, we use inheritance, so we will have StateVarialbeInt, StateVariableFloat, etc.

- StateAction : ISerializable
  Base class for actions, can have a float duration, a float delay;
  Methods like OnEnter, OnUpdate, OnExit.
  It can be added to StateActionNode

- StateCondition
  Base class for conditions, evaluate a bool value.

- StateContext
  This is the user context, it can be inherited by the user. maybe this can be an interface.

How it runs? 

If we have a class like Pawn, it might have a list of StateSlot, each StateSlot can run one StateFlow, so that we can have multiple state logics running at the same time.

Editor:

Well, this part I'm no expert, but it should have class like :

StateNodeInspector within the StateFlowEditorWindow, and for different variables we can specify different drawer.

The StateFlowEditorWindow should have a area for editing variables.

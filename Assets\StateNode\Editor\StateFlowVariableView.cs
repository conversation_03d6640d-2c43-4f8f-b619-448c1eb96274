using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEditor;
using UnityEngine.UIElements;
using UnityEditor.UIElements;
using StateScript;

namespace StateScript.Editor
{
    /// <summary>
    /// UI component for managing StateFlow variables
    /// </summary>
    public class StateFlowVariableView : VisualElement
    {
        public event Action OnVariableChanged;

        private List<StateVariable> _variables;
        private VisualElement _variableContainer;
        private Button _addButton;
        private Button _collapseButton;
        private VisualElement _contentContainer;
        private bool _isCollapsed = false;
        
        public StateFlowVariableView()
        {
            CreateUI();
        }
        
        private void CreateUI()
        {
            style.backgroundColor = new Color(0.25f, 0.25f, 0.25f, 1f);
            style.paddingTop = 5;
            style.paddingBottom = 5;
            style.paddingLeft = 5;
            style.paddingRight = 5;

            // Header with title and collapse button
            var header = new VisualElement();
            header.style.flexDirection = FlexDirection.Row;
            header.style.justifyContent = Justify.SpaceBetween;
            header.style.alignItems = Align.Center;
            header.style.marginBottom = 5;

            var title = new Label("Variables");
            title.style.fontSize = 14;
            title.style.unityFontStyleAndWeight = FontStyle.Bold;

            _collapseButton = new Button(ToggleCollapse) { text = "▼" };
            _collapseButton.style.width = 20;
            _collapseButton.style.height = 20;
            _collapseButton.style.fontSize = 10;
            _collapseButton.style.backgroundColor = new Color(0.4f, 0.4f, 0.4f, 1f);
            _collapseButton.style.borderTopLeftRadius = 3;
            _collapseButton.style.borderTopRightRadius = 3;
            _collapseButton.style.borderBottomLeftRadius = 3;
            _collapseButton.style.borderBottomRightRadius = 3;

            header.Add(title);
            header.Add(_collapseButton);
            Add(header);

            // Content container (collapsible)
            _contentContainer = new VisualElement();
            Add(_contentContainer);

            // Variable container with scroll view
            var scrollView = new ScrollView();
            scrollView.style.maxHeight = 400;
            _variableContainer = new VisualElement();
            scrollView.Add(_variableContainer);
            _contentContainer.Add(scrollView);

            // Add button
            _addButton = new Button(ShowAddVariableMenu) { text = "Add Variable" };
            _addButton.style.marginTop = 5;
            _contentContainer.Add(_addButton);
        }
        
        public void LoadVariables(List<StateVariable> variables)
        {
            _variables = variables;
            RefreshVariableList();
        }

        private void ToggleCollapse()
        {
            _isCollapsed = !_isCollapsed;
            _contentContainer.style.display = _isCollapsed ? DisplayStyle.None : DisplayStyle.Flex;
            _collapseButton.text = _isCollapsed ? "▶" : "▼";
        }
        
        private void RefreshVariableList()
        {
            _variableContainer.Clear();
            
            if (_variables == null)
                return;
            
            for (int i = 0; i < _variables.Count; i++)
            {
                var variable = _variables[i];
                var variableElement = CreateVariableElement(variable, i);
                _variableContainer.Add(variableElement);
            }
        }
        
        private VisualElement CreateVariableElement(StateVariable variable, int index)
        {
            var container = new VisualElement();
            container.style.backgroundColor = new Color(0.3f, 0.3f, 0.3f, 1f);
            container.style.marginBottom = 2;
            container.style.paddingTop = 4;
            container.style.paddingBottom = 4;
            container.style.paddingLeft = 5;
            container.style.paddingRight = 5;
            container.style.borderTopLeftRadius = 3;
            container.style.borderTopRightRadius = 3;
            container.style.borderBottomLeftRadius = 3;
            container.style.borderBottomRightRadius = 3;
            container.style.flexDirection = FlexDirection.Row;
            container.style.alignItems = Align.Center;
            container.style.minHeight = 25;

            // Name field (editable on double-click)
            var nameLabel = new Label(variable.Name.ToString());
            nameLabel.style.fontSize = 11;
            nameLabel.style.unityFontStyleAndWeight = FontStyle.Bold;
            nameLabel.style.width = 80;
            nameLabel.style.marginRight = 5;
            nameLabel.style.overflow = Overflow.Hidden;
            nameLabel.style.unityTextAlign = TextAnchor.MiddleLeft;

            // Make name editable on double-click
            nameLabel.RegisterCallback<MouseDownEvent>(evt =>
            {
                if (evt.clickCount == 2)
                {
                    MakeNameEditable(nameLabel, variable, container);
                }
            });

            // Type dropdown
            var typeDropdown = new DropdownField();
            typeDropdown.choices = new List<string> { "Int", "Float", "Bool", "String", "Vector3" };
            typeDropdown.value = variable.TypeName;
            typeDropdown.style.width = 70;
            typeDropdown.style.fontSize = 10;
            typeDropdown.style.marginRight = 5;
            typeDropdown.RegisterValueChangedCallback(evt =>
            {
                if (evt.newValue != evt.previousValue)
                {
                    ChangeVariableType(variable, evt.newValue, index);
                }
            });

            // Value editor (inline)
            var valueElement = CreateInlineValueEditor(variable);
            valueElement.style.flexGrow = 1;
            valueElement.style.marginRight = 5;

            // Delete button
            var deleteButton = new Button(() => DeleteVariable(index)) { text = "×" };
            deleteButton.style.width = 20;
            deleteButton.style.height = 20;
            deleteButton.style.fontSize = 12;
            deleteButton.style.backgroundColor = new Color(0.6f, 0.2f, 0.2f, 1f);
            deleteButton.style.borderTopLeftRadius = 2;
            deleteButton.style.borderTopRightRadius = 2;
            deleteButton.style.borderBottomLeftRadius = 2;
            deleteButton.style.borderBottomRightRadius = 2;

            container.Add(nameLabel);
            container.Add(typeDropdown);
            container.Add(valueElement);
            container.Add(deleteButton);

            return container;
        }

        private void MakeNameEditable(Label nameLabel, StateVariable variable, VisualElement container)
        {
            var nameField = new TextField();
            nameField.value = variable.Name.ToString();
            nameField.style.fontSize = 11;
            nameField.style.unityFontStyleAndWeight = FontStyle.Bold;
            nameField.style.width = 80;
            nameField.style.marginRight = 5;

            nameField.RegisterValueChangedCallback(evt =>
            {
                if (!string.IsNullOrEmpty(evt.newValue) && evt.newValue != evt.previousValue)
                {
                    variable.Name = new Fixed32String(evt.newValue);
                    OnVariableChanged?.Invoke();
                }
            });

            nameField.RegisterCallback<BlurEvent>(evt =>
            {
                // Replace field with label when focus is lost
                nameLabel.text = variable.Name.ToString();
                var index = container.IndexOf(nameField);
                container.RemoveAt(index);
                container.Insert(index, nameLabel);
            });

            nameField.RegisterCallback<KeyDownEvent>(evt =>
            {
                if (evt.keyCode == KeyCode.Return || evt.keyCode == KeyCode.KeypadEnter)
                {
                    nameLabel.text = variable.Name.ToString();
                    var index = container.IndexOf(nameField);
                    container.RemoveAt(index);
                    container.Insert(index, nameLabel);
                }
            });

            var labelIndex = container.IndexOf(nameLabel);
            container.RemoveAt(labelIndex);
            container.Insert(labelIndex, nameField);
            nameField.Focus();
            nameField.SelectAll();
        }

        private void ChangeVariableType(StateVariable oldVariable, string newTypeName, int index)
        {
            StateVariable newVariable = null;

            switch (newTypeName)
            {
                case "Int":
                    newVariable = new StateVariableInt(oldVariable.Name, 0);
                    break;
                case "Float":
                    newVariable = new StateVariableFloat(oldVariable.Name, 0f);
                    break;
                case "Bool":
                    newVariable = new StateVariableBool(oldVariable.Name, false);
                    break;
                case "String":
                    newVariable = new StateVariableString(oldVariable.Name, "");
                    break;
                case "Vector3":
                    newVariable = new StateVariableVector3(oldVariable.Name, Vector3.zero);
                    break;
            }

            if (newVariable != null)
            {
                _variables[index] = newVariable;
                OnVariableChanged?.Invoke();
                RefreshVariableList();
            }
        }

        private VisualElement CreateInlineValueEditor(StateVariable variable)
        {
            switch (variable)
            {
                case StateVariableInt intVar:
                    return CreateInlineIntEditor(intVar);
                case StateVariableFloat floatVar:
                    return CreateInlineFloatEditor(floatVar);
                case StateVariableBool boolVar:
                    return CreateInlineBoolEditor(boolVar);
                case StateVariableString stringVar:
                    return CreateInlineStringEditor(stringVar);
                case StateVariableVector3 vectorVar:
                    return CreateInlineVector3Editor(vectorVar);
                default:
                    return new Label("Unsupported");
            }
        }

        private VisualElement CreateValueEditor(StateVariable variable)
        {
            switch (variable)
            {
                case StateVariableInt intVar:
                    return CreateIntEditor(intVar);
                case StateVariableFloat floatVar:
                    return CreateFloatEditor(floatVar);
                case StateVariableBool boolVar:
                    return CreateBoolEditor(boolVar);
                case StateVariableString stringVar:
                    return CreateStringEditor(stringVar);
                case StateVariableVector3 vectorVar:
                    return CreateVector3Editor(vectorVar);
                default:
                    return new Label("Unsupported type");
            }
        }

        private VisualElement CreateInlineIntEditor(StateVariableInt intVar)
        {
            var field = new IntegerField();
            field.value = intVar.Value;
            field.style.fontSize = 10;
            field.style.height = 18;
            field.RegisterValueChangedCallback(evt =>
            {
                intVar.Value = evt.newValue;
                OnVariableChanged?.Invoke();
            });
            return field;
        }

        private VisualElement CreateInlineFloatEditor(StateVariableFloat floatVar)
        {
            var field = new FloatField();
            field.value = floatVar.Value;
            field.style.fontSize = 10;
            field.style.height = 18;
            field.RegisterValueChangedCallback(evt =>
            {
                floatVar.Value = evt.newValue;
                OnVariableChanged?.Invoke();
            });
            return field;
        }

        private VisualElement CreateInlineBoolEditor(StateVariableBool boolVar)
        {
            var field = new Toggle();
            field.value = boolVar.Value;
            field.style.height = 18;
            field.RegisterValueChangedCallback(evt =>
            {
                boolVar.Value = evt.newValue;
                OnVariableChanged?.Invoke();
            });
            return field;
        }

        private VisualElement CreateInlineStringEditor(StateVariableString stringVar)
        {
            var field = new TextField();
            field.value = stringVar.Value;
            field.style.fontSize = 10;
            field.style.height = 18;
            field.RegisterValueChangedCallback(evt =>
            {
                stringVar.Value = evt.newValue;
                OnVariableChanged?.Invoke();
            });
            return field;
        }

        private VisualElement CreateInlineVector3Editor(StateVariableVector3 vectorVar)
        {
            var container = new VisualElement();
            container.style.flexDirection = FlexDirection.Row;

            var xField = new FloatField() { value = vectorVar.Value.x };
            var yField = new FloatField() { value = vectorVar.Value.y };
            var zField = new FloatField() { value = vectorVar.Value.z };

            xField.style.fontSize = 9;
            yField.style.fontSize = 9;
            zField.style.fontSize = 9;
            xField.style.height = 18;
            yField.style.height = 18;
            zField.style.height = 18;
            xField.style.width = Length.Percent(33);
            yField.style.width = Length.Percent(33);
            zField.style.width = Length.Percent(33);

            System.Action updateVector = () =>
            {
                vectorVar.Value = new Vector3(xField.value, yField.value, zField.value);
                OnVariableChanged?.Invoke();
            };

            xField.RegisterValueChangedCallback(evt => updateVector());
            yField.RegisterValueChangedCallback(evt => updateVector());
            zField.RegisterValueChangedCallback(evt => updateVector());

            container.Add(xField);
            container.Add(yField);
            container.Add(zField);

            return container;
        }

        private VisualElement CreateIntEditor(StateVariableInt intVar)
        {
            var field = new IntegerField();
            field.value = intVar.Value;
            field.RegisterValueChangedCallback(evt =>
            {
                intVar.Value = evt.newValue;
                OnVariableChanged?.Invoke();
            });
            return field;
        }
        
        private VisualElement CreateFloatEditor(StateVariableFloat floatVar)
        {
            var field = new FloatField();
            field.value = floatVar.Value;
            field.RegisterValueChangedCallback(evt =>
            {
                floatVar.Value = evt.newValue;
                OnVariableChanged?.Invoke();
            });
            return field;
        }
        
        private VisualElement CreateBoolEditor(StateVariableBool boolVar)
        {
            var field = new Toggle();
            field.value = boolVar.Value;
            field.RegisterValueChangedCallback(evt =>
            {
                boolVar.Value = evt.newValue;
                OnVariableChanged?.Invoke();
            });
            return field;
        }
        
        private VisualElement CreateStringEditor(StateVariableString stringVar)
        {
            var field = new TextField();
            field.value = stringVar.Value;
            field.RegisterValueChangedCallback(evt =>
            {
                stringVar.Value = evt.newValue;
                OnVariableChanged?.Invoke();
            });
            return field;
        }
        
        private VisualElement CreateVector3Editor(StateVariableVector3 vectorVar)
        {
            var field = new Vector3Field();
            field.value = vectorVar.Value;
            field.RegisterValueChangedCallback(evt =>
            {
                vectorVar.Value = evt.newValue;
                OnVariableChanged?.Invoke();
            });
            return field;
        }
        
        private void ShowAddVariableMenu()
        {
            var menu = new GenericMenu();
            
            menu.AddItem(new GUIContent("Int"), false, () => AddVariable(new StateVariableInt()));
            menu.AddItem(new GUIContent("Float"), false, () => AddVariable(new StateVariableFloat()));
            menu.AddItem(new GUIContent("Bool"), false, () => AddVariable(new StateVariableBool()));
            menu.AddItem(new GUIContent("String"), false, () => AddVariable(new StateVariableString()));
            menu.AddItem(new GUIContent("Vector3"), false, () => AddVariable(new StateVariableVector3()));
            
            menu.ShowAsContext();
        }
        
        private void AddVariable(StateVariable variable)
        {
            if (_variables == null)
                _variables = new List<StateVariable>();
            
            // Generate unique name
            var baseName = $"New{variable.TypeName}";
            var uniqueName = baseName;
            int counter = 1;
            
            while (_variables.Any(v => v.Name.ToString() == uniqueName))
            {
                uniqueName = $"{baseName}{counter}";
                counter++;
            }
            
            variable.Name = new Fixed32String(uniqueName);
            _variables.Add(variable);
            
            RefreshVariableList();
            OnVariableChanged?.Invoke();
        }
        
        private void DeleteVariable(int index)
        {
            if (_variables != null && index >= 0 && index < _variables.Count)
            {
                _variables.RemoveAt(index);
                RefreshVariableList();
                OnVariableChanged?.Invoke();
            }
        }
    }
}

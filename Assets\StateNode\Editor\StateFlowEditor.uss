/* StateFlow Editor Styles */

/* Node styling */
.node {
    border-radius: 6px;
    border-width: 2px;
    min-width: 150px;
    min-height: 50px;
}

.node #title {
    border-top-left-radius: 6px;
    border-top-right-radius: 6px;
    border-bottom-left-radius: 0px;
    border-bottom-right-radius: 0px;
    padding: 4px 8px;
    font-size: 12px;
    -unity-font-style: bold;
    color: white;
}

.node #contents {
    border-bottom-left-radius: 6px;
    border-bottom-right-radius: 6px;
    border-top-left-radius: 0px;
    border-top-right-radius: 0px;
    background-color: rgba(32, 32, 32, 0.9);
    border-top-width: 0px;
    padding: 4px;
}

/* Port styling */
.port {
    min-width: 30px;
    min-height: 24px;
    margin: 2px;
}

.port-highlight {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

/* Port connector styling */
.port > .connector {
    border-radius: 8px;
    border-width: 2px;
    width: 16px;
    height: 16px;
}

.port.input > .connector {
    border-color: cyan;
    background-color: rgba(0, 255, 255, 0.3);
}

.port.output > .connector {
    border-color: magenta;
    background-color: rgba(255, 0, 255, 0.3);
}

/* Selected node styling */
.node:selected {
    border-color: #44C0FF;
    border-width: 3px;
}

/* Node type specific colors */
.node.action-node #title {
    background-color: rgba(51, 153, 51, 1);
}

.node.condition-node #title {
    background-color: rgba(153, 153, 51, 1);
}

.node.listener-node #title {
    background-color: rgba(51, 51, 153, 1);
}

.node.custom-node #title {
    background-color: rgba(102, 102, 102, 1);
}

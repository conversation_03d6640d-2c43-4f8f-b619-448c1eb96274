using UnityEngine;
using System.IO;
using System.Linq;

namespace StateScript
{
    /// <summary>
    /// Custom asset for StateFlow that can be serialized to JSON
    /// Not a ScriptableObject - uses custom serialization
    /// </summary>
    [System.Serializable]
    public class StateFlowAsset
    {
        [SerializeField] private string _jsonData = "";
        [SerializeField] private string _assetName = "New StateFlow";
        
        private StateFlow _stateFlow;
        private bool _isDirty = false;
        
        public string AssetName 
        { 
            get => _assetName; 
            set 
            { 
                _assetName = value; 
                _isDirty = true; 
            } 
        }
        
        public StateFlow StateFlow
        {
            get
            {
                if (_stateFlow == null)
                {
                    DeserializeStateFlow();
                }
                return _stateFlow;
            }
            set
            {
                _stateFlow = value;
                _isDirty = true;
            }
        }
        
        public bool IsDirty => _isDirty;
        
        public StateFlowAsset()
        {
            _stateFlow = new StateFlow();
            _assetName = "New StateFlow";
        }
        
        public StateFlowAsset(string name)
        {
            _stateFlow = new StateFlow(name);
            _assetName = name;
        }
        
        /// <summary>
        /// Serialize the StateFlow to JSON
        /// </summary>
        public void SerializeStateFlow()
        {
            if (_stateFlow != null)
            {
                var serializer = new JsonSerializer();
                _stateFlow.Serialize(serializer);
                _jsonData = serializer.ToJsonString(true);
                _isDirty = false;
            }
        }
        
        /// <summary>
        /// Deserialize the StateFlow from JSON
        /// </summary>
        public void DeserializeStateFlow()
        {
            if (!string.IsNullOrEmpty(_jsonData))
            {
                try
                {
                    var serializer = new JsonSerializer(_jsonData);
                    _stateFlow = new StateFlow();
                    _stateFlow.Deserialize(serializer);
                }
                catch (System.Exception e)
                {
                    Debug.LogError($"Failed to deserialize StateFlow: {e.Message}");
                    _stateFlow = new StateFlow(_assetName);
                }
            }
            else
            {
                _stateFlow = new StateFlow(_assetName);
            }
            _isDirty = false;
        }
        
        /// <summary>
        /// Get the raw JSON data
        /// </summary>
        public string GetJsonData()
        {
            if (_isDirty)
            {
                SerializeStateFlow();
            }
            return _jsonData;
        }
        
        /// <summary>
        /// Set the raw JSON data
        /// </summary>
        public void SetJsonData(string jsonData)
        {
            _jsonData = jsonData;
            _stateFlow = null; // Force deserialization on next access
            _isDirty = false;
        }
        
        /// <summary>
        /// Save to file
        /// </summary>
        public void SaveToFile(string filePath)
        {
            try
            {
                var jsonData = GetJsonData();
                File.WriteAllText(filePath, jsonData);
                Debug.Log($"StateFlow saved to: {filePath}");
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Failed to save StateFlow to {filePath}: {e.Message}");
            }
        }
        
        /// <summary>
        /// Load from file
        /// </summary>
        public static StateFlowAsset LoadFromFile(string filePath)
        {
            try
            {
                if (File.Exists(filePath))
                {
                    var jsonData = File.ReadAllText(filePath);
                    var asset = new StateFlowAsset();
                    asset.SetJsonData(jsonData);
                    asset._assetName = Path.GetFileNameWithoutExtension(filePath);
                    return asset;
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Failed to load StateFlow from {filePath}: {e.Message}");
            }
            
            return new StateFlowAsset();
        }
        
        /// <summary>
        /// Create a runtime instance of the StateFlow
        /// </summary>
        public StateFlow CreateRuntimeInstance()
        {
            var serializer = new JsonSerializer(GetJsonData());
            var runtimeFlow = new StateFlow();
            runtimeFlow.Deserialize(serializer);
            return runtimeFlow;
        }
        
        /// <summary>
        /// Validate the StateFlow for common issues
        /// </summary>
        public bool Validate(out string[] errors)
        {
            var errorList = new System.Collections.Generic.List<string>();
            var flow = StateFlow;
            
            // Check for entry node
            if (flow.EntryNodeId.Value == 0 && !flow.Nodes.Any(n => n is StateListenerNode listener && listener.EventType.Equals(EventType.OnStart)))
            {
                errorList.Add("No entry node or OnStart listener found");
            }
            
            // Check for disconnected nodes
            foreach (var node in flow.Nodes)
            {
                if (!(node is StateListenerNode))
                {
                    var inputPorts = node.Property.GetInputPorts();
                    bool hasInput = inputPorts.Any(port => flow.Connections.Any(c => c.InputPortId == port.Id));
                    if (!hasInput)
                    {
                        errorList.Add($"Node {node.Property.Id} has no input connections");
                    }
                }
            }
            
            // Check for invalid connections
            foreach (var connection in flow.Connections)
            {
                bool outputExists = flow.Nodes.Any(n => n.Property.Ports.Any(p => p.Id == connection.OutputPortId && p.Type == PortType.Output));
                bool inputExists = flow.Nodes.Any(n => n.Property.Ports.Any(p => p.Id == connection.InputPortId && p.Type == PortType.Input));
                
                if (!outputExists)
                {
                    errorList.Add($"Connection references non-existent output port {connection.OutputPortId}");
                }
                if (!inputExists)
                {
                    errorList.Add($"Connection references non-existent input port {connection.InputPortId}");
                }
            }
            
            errors = errorList.ToArray();
            return errors.Length == 0;
        }
    }
}

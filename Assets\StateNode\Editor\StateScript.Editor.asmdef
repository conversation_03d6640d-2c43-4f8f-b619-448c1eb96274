{"name": "StateScript.Editor", "rootNamespace": "StateScript.Editor", "references": ["StateScript.Runtime", "Unity.Collections", "Newtonsoft.Json", "UnityEngine.UIElementsModule", "UnityEditor.UIElementsModule"], "includePlatforms": ["Editor"], "excludePlatforms": [], "allowUnsafeCode": true, "overrideReferences": false, "precompiledReferences": [], "autoReferenced": true, "defineConstraints": [], "versionDefines": [], "noEngineReferences": false}
-------------------------------------------------------------------------------------------------------------------------------------------------------
Makinom 2.19.1
-------------------------------------------------------------------------------------------------------------------------------------------------------

New:
- Save Game Settings: File Settings: File Info: 'Save Time Format' setting and new text code available to add the save date and time. Use '<savetime>' to add the date and time the save file was created to the file info texts.
- Save Game Settings: File Settings: File Info, Empty File Info: 'Additional Content' and other new input settings available. Save file information can now use additional content, custon input prefabs, tooltips and input highlights.

Changes:
- 

Fixes:
- 


-------------------------------------------------------------------------------------------------------------------------------------------------------
Makinom 2.19.0
-------------------------------------------------------------------------------------------------------------------------------------------------------

New:
- Input Options: Schematic: 'Schematic' option type available. Displays a button to start a schematic.
- Input Options: Save Confirmation: 'Save Confirmation' option type available. Defines if the save question dialogue when saving a save game will be displayed.
- Input Keys: Key Code, Unity Input Manager, Mouse, Touch: 'Trigger Count' settings available. Optionally recognize input after a defined number of uses, e.g. double press/click. Previous mouse/touch count settings are replaced with these settings.
- Save Game Settings: PlayerPrefs Game Options: 'Save Confirmation' setting available. The save confirmation option state will be saved in PlayerPrefs.
- Save Game Settings: Save Game Menu: Save Question Dialogue: 'Save Confirmation Option' setting available. Define the default value of the 'Save Confirmation' option. The 'Save Confirmation' option defines if the save question dialogue is displayed or not and can be toggled on/off by the player.
- Save Game Settings: Save Game Menu: Save Question Dialogue: 'Only For Override' setting available. Optionally only display the save game question when overriding an existing save game.
- Unity UI: HUD UI Box Selection (Content Provider): 'HUD UI Box Selection' content provider component available. Uses a parent UI box's currently selected input's context (schematic context or tooltip content) as content.

Changes:
- Input Keys: Mouse, Touch: 'Trigger Count' settings replace 'Click Count' and 'Tab Count' settings. Previous setup will be updated automatically.
- UI Boxes: Controls: 'Vertical Axis' and 'Horizontal Axis' settings have been replaced by new input settings. The new settings allow either using a single input key as axis or 2 input keys for positive and negative axis input separately.
- Global Machines: Conditions: The 'Game State Conditions' and 'Variable Conditions' have been replaced by general condition settings. Previous settings will be updated automatically.

Fixes:
- Schematics: Store Nav Raycast: Fixed an issue where 'Store Normal' used the variable key of the position.


-------------------------------------------------------------------------------------------------------------------------------------------------------
Makinom 2.18.0
-------------------------------------------------------------------------------------------------------------------------------------------------------

New:
- General Conditions: 'Check Value' condition type available. Compares a value with a different value.
- UI Boxes, General Settings: Controls: 'Hold Accept Key' settings available. Accepting an input in UI boxes can optionally be limited to holding the accept key/click for a defined time. Define an input key, hold time and if click-accepting also supports hold accepting. The hold accept time can e.g. be dispalyed in HUDs. Individual UI boxes or content layouts for inputs have to enable using hold accept.
- UI Boxes: 'Use Hold Accept' setting available. Enables using hold accepting for the UI box.
- Content Layouts: 'Use Hold Accept' setting available. Either uses the default setup (i.e. setting of the UI box) or overrides the UI box's setup, enabling/disabling hold accepting.
- Formulas, Schematics: Selected Data Contains: 'Selected Data Contains' node available in 'Selected Data' nodes. Checks if data stored in a selected data list contains something stored in another selected data list.
- Unity UI: HUD Value Bar: Accept Hold Time: 'Accept Hold Time' type available. Displays the time accept has been held down (both input key and click accept).
- Unity UI: Is Selected UI Input Component: 'Is Selected UI Input' component available. Enables or disables a game object depending if a defined UI input is currently selected in it's UI box.

Changes:
- UI Boxes: Using 'Cursor Over Selection' and 'Unfocused Cursor Over' no longer works when focus is blocked and the input the cursor is over isn't part of the focused UI box.


-------------------------------------------------------------------------------------------------------------------------------------------------------
Makinom 2.17.0
-------------------------------------------------------------------------------------------------------------------------------------------------------

New:
- UI Input Filters: Filter input content displayed in a UI box by defined conditions. Input filters are added to the UI box.
- UI Boxes: Controls: 'Input Filter Settings' available. Define input keys to navigate and use UI input filters.
- UI System: Unity UI: Default UI Prefabs: 'Dropdown Input Prefab' setting available. Defines the input prefab used for dropdown inputs.
- Content Layouts: 'Highlight Input' settings available. Optionally add a highlight color to all inputs that use a content layout.
- Save Game Settings: Load Game Menu: 'Separate Auto Save Slots' settings available. Optionally separate normal and auto save slots by using tabs.
- Input Options: Custom Dropdown: 'Custom Dropdown' option type available. Displays a dropdown menu with defined list of options. Uses int variables/PlayerPrefs to store the selected option's index.
- Input Options: 'Keep Value' setting available when using 'Live Update'. Optionally keep the changed value for an input option even when canceling or closing (without accepting changes) the options dialogue.
- Schematics: Text File Exists: 'Text File Exists' node available in 'Value > Text File' nodes. Checks if a text file exists.
- Schematics: Delete Text File: 'Delete Text File' node available in 'Value > Text File' nodes. Deletes a defined text file.
- Schematics: Store Variables: 'Encrypt Data' setting available. Optionally encrypt the XML-formatted text using the save game encryption.
- Schematics: Load Variables: 'Decrypt Data' setting available. Optionally decrypt the XML-formatted text using the save game encryption.
- Schematics: Save Game Exists: 'Any' save game type available. Checks if any save game or auto save game file exists.
- Text Files: File Type: 'Variable' file type available for text file handling. Saves the data into a global string variable using the file name as variable key. This is e.g. used by 'Store Variables' and 'Load Variables' nodes to save/load variable data into a text file.
- General Conditions: Has Tagged Machine: 'Has Tagged Machine' condition type available. Checks if a game object has a tagged machine with defined tags.
- Component Conditions: Has Tagged Machine: 'Has Tagged Machine' condition type available. Checks if the component's game object has a tagged machine with defined tags.
- HUD Conditions: Has Tagged Machine: 'Has Tagged Machine' condition type available. Checks if the HUD's user has a tagged machine with defined tags.
- Unity UI: UI Tooltip (TextMesh Pro) Component: 'UI Tooltip (TextMesh Pro)' component available. Automatically displays tooltips when hovering over words that where added using global text codes (name, short name, icon or the tooltip text codes). Please note that this only works for content that is directly referenced by the project.
- Unity UI: UI Input Filter Component: 'UI Input Filter' component available. Add this component to an input component of a type matching the filter you're using (usually a 'UI Toggle Input' component to enable/disable a filter, except for 'Text Search' filter that uses a text input field).
- Unity UI: UI Box Input Filters Component: 'UI Box Input Filters' component available. This component manages multiple 'UI Input Filter' components added to it and it's child objects. Add the component to a UI box prefab to allow filtering inputs displayed by the UI box.
- Unity UI: UI Box Multi Input Filters Component: 'UI Box Multi Input Filters' component avaialble. This component manages multiple 'UI Box Input Filters' components. Use it to create more complex filter combinations. Add the component to a UI box prefab.
- Unity UI: UI Box Component: Input Settings: 'Dropdown Input Prefab' setting available. Defines the input prefab used for dropdown inputs, overriding the default input prefabs.
- Unity UI: UI Cursor Over Schematics Component: 'UI Cursor Over Schematics' component available. Optionally start schemtics when the cursor enters or leaves a UI element. E.g. can be used for cursor-over scale changes.
- Unity UI: HUD Selected Data Content Provider Component: 'HUD Selected Data (Content Provider)' component available. Uses the first content stored in a defined selected data as HUD user/content.
- Unity UI: HUD Forward Content Provider Component: 'HUD Forward Content Provider' component available. Uses another content provider's content as content. E.g. useful if you set up HUD elements as a prefab using a forward content provider at their root, adding the prefab to another HUD just requires to set the content provider of the forward content provider.
- Unity UI: Input Components: 'Audio Settings' available. Optionally override the UI box's or default audio clip settings for individual inputs.
- Unity UI: Context Menu: 'UI Box > Input Filter' context menu entries available. Use the new context menu entries to create toggle filter inputs (for prefab or added to a UI box) and add 'UI Box Input Filters' and 'UI Box Multi Input Filters' to UI boxes.
- Unity UI: Context Menu: 'Dropdown Input' menu entry available in 'UI Box > Input (Value)' context menu. Creates a dropdown input used for a prefab or as a placed input on a UI box.
- Text Editor: 'Add Tooltip' button available in 'More > Data' for data types that are directly referenced by the project. Adds tooltip information to the text that can be displayed by 'UI Tooltip (TextMesh Pro)' components. The tooltip text code is not displayed in texts.

Changes:
- Schematics: Store Variables: The 'Store Variables' node has been renamed to 'Save Variables'.

Fixes:
- Input Keys: Input Key: Fixed an issue where 'Input Key' input origins could lead to axis values not being used.
- UI Boxes: Fixed an issue where opening and closing a UI box in the same frame could lead to it still registering as open, not opening it again later.
- Auto Machines: Notify Start: Object Variable Change: Fixed an issue where using 'Object ID' or 'Game Object' didn't show the correct settings.
- Editor: Schematics: Fixed an issue where 'New Schematic' didn't create a new schematic when a schematic was already opened.
- Editor: Fixed a potential issue where initially saving a new project could lead to some data assets having no data in some Unity versions. This only appeared when not creating a backup during saving.
- Node Editor: Fixed an issue where copying a node from a node group and pasting it in a schematic without that node group caused an error. Pasting nodes will no longer use the node group of the copied node.
- Node Editor: Fixed an issue where removing a gate didn't remove connects to it.
- Unity UI: HUD Click Component, UI Button Input: Fixed an issue where using 'HUD Click' components on button inputs ('UI Button Input' component) where not recognizing clicks.


-------------------------------------------------------------------------------------------------------------------------------------------------------
Makinom 2.16.1
-------------------------------------------------------------------------------------------------------------------------------------------------------

Fixes:
- Scene Object Components: Cursor: Fixed an issue where mouse cursor changes where not undone if the game object is destroyed while the cursor is over it.
- Editor: Variable Conditions: Fixed an issue that could cause data changes being recognized when using 'Template' variable conditions although no data was changed.
- Schematics: Save Game Exists: Fixed an issue where checking 'Retry' didn't work.


-------------------------------------------------------------------------------------------------------------------------------------------------------
Makinom 2.16.0
-------------------------------------------------------------------------------------------------------------------------------------------------------

New:
- Unity 6: Makinom 2 now supports Unity 6.
- Movement Components: Nav Mesh Agent: 'Use Root Motion' settings available. Optionally use an 'Animator' component's root motion for position changes instead of the NavMesh agent.
- Save Games: Auto Save Slots: 'Use Delete Key' setting available in auto save slot selections. Optionally allow deleting existing auto save slots in the auto save slot selection.
- Schematics: Unity Console: 'Add Schematic Name' setting available. Optionally add the name of the schematic at the start of the console output. By default enabled, adding the name.

Changes:
- Schematics, Machine Components: Resource Overrides: Overriding prefabs will now use the pooling settings defined in the schematic. Previously pooling was ignored when the prefab was replaced by an override.

Fixes:
- Formulas: Selected Data: Fixed an issue where formula didn't create their own local selected data unless they where forwarded local data from their call (e.g. a schematic).
- Text Codes: Fixed an issue where text codes for sub-data where not used.
- Save Games: Auto Save Slots: Fixed an issue where auto save slot selection could use the wrong slot.
- Spawn Points: Fixed an issue that could result in wrong spawn positions when using a 2D collider to define a spawn area.


-------------------------------------------------------------------------------------------------------------------------------------------------------
Makinom 2.15.4
-------------------------------------------------------------------------------------------------------------------------------------------------------

New:
- HUDs: Tooltip: Tooltip Checks: Custom: 'Custom' tooltip type check available. Checks if a tooltip is a custom tooltip, either allowing all custom tooltips or from defined custom tooltip keys.
- Unity UI: UI Custom Tooltip Components: 'UI Custom Tooltip' components available. Adds a custom tooltip to a part of your UI with a defined content (e.g. name, description and icon). Custom tooltips can use a 'Custom Tooltip Key' to use separate tooltip HUDs to display them.

Changes:
- Unity UI: UI Box Components: Having both 'Horizontal Input Change' and 'Vertical Input Change' set to 0 to use Unity's direction based input selection will now force using vertical input change 1 when the UI box is disabled (e.g. when using a hidden target menu in ORK Framework 3). Directional input based on UI positions was otherwise not working correctly due to the disabled state of UI game objects.

Fixes:
- UI Boxes: Typewriter: Fixed an issue where the 'Typewriter Finished Object' wasn't enabled when the typewriter effect finished on it's own.


-------------------------------------------------------------------------------------------------------------------------------------------------------
Makinom 2.15.3
-------------------------------------------------------------------------------------------------------------------------------------------------------

New:
- Schematics: Store NavMesh Path: 'Store NavMesh Path' node available in 'Movement > NavMesh' nodes. Stores the current path (corners) of a NavMesh agent into a Vector 3 variable list.
- Schematics: Change Line Renderer Positions: 'Change Line Renderer Positions' node available in 'Game Object > Renderer' nodes. Sets the positions of a 'Line Renderer' component using a Vector 3 variable list.
- Schematics: Show Dialogue: Notification: 'Max Display Count' setting available when using 'Queue' notification mode. Defines how many notification can be displayed at the same time before being queued.

Changes:
- Tagged Machine Components: A tagged machine will now be registered when it's component is enabled (and unregistered when disabled), greatly improving performance for the 'Search Tagged Machines' node and other tagged machine nodes accessing all in the scene. You can access all registered tagged machines via scripting using 'TaggedMachineComponent.Available'.

Fixes:
- Schematics: Change Rotation: Fixed an issue where using intpolation could result in different rotation directions based on the current rotation of the game object.
- Schematics: Value Option Dialogue: Custom: Fixed an issue where using custom 'Int' values used float variables/PlayerPrefs instead of int.
- Editor: Filters: Fixed an issue where using filters didn't change the list on first use.


-------------------------------------------------------------------------------------------------------------------------------------------------------
Makinom 2.15.2
-------------------------------------------------------------------------------------------------------------------------------------------------------

New:
- UI Box Selections: 'UI Layout' settings available for most 'UI Box' selection settings. Optionally use a UI layout for the displayed UI box.
- Variable Lists: 'Lowest' and 'Highest' list origins available. Getting a variable from a variable list now allows getting the lowest/highest value (int/float), the value with shortest/longest distance to 0-vector (Vector3) or the shortest/longest text (string).
- Scripting: Reflection: Parameters and fields are now extensible settings. Extend from 'BaseParameterType' and 'BaseSchematicParameterType' to add new value types.

Fixes:
- Mouse/Touch Controls: Fixed an issue where using 'Start' mode recognized the mouse as pressed without releasing it.


-------------------------------------------------------------------------------------------------------------------------------------------------------
Makinom 2.15.1
-------------------------------------------------------------------------------------------------------------------------------------------------------

New:
- Schematics: Change Position: 'Use Stop Time' settings available when using 'Move' and 'Move By Speed'. Optionally define a maximum time the movement can take before automatically stopping.
- Schematics: Load Scene: 'Destroy After Run' setting available when not using 'Spawn Machine Object'. Destroying the machine object after the schematic finished (after a scene load) is now optional.
- Unity UI: UI Int/Float Button Input Component: 'Inverse Horizontal Input' and 'Inverse Vertical Input' settings available. Optionally inverse the value changes coming from horizontal or vertical input, e.g. having left increase and right decrease the value.

Changes:
- Plugins: 'Use GUI Tick' setting has been removed. GUI tick functionality is no longer supported by plugins. Implement it via a custom component if you need it.

Fixes:
- Content Information: Fixed an issue where referencing a content itself via text codes (e.g. adding the name of an input key to the description of the same input key) would cause issues in editor popups and in-game.
- Schematics: Show Dialogue: Choice: Fixed an issue where using 'Store Selection' to remember the selected input index didn't work.
- Schematics: Change Rotation: Fixed an issue where rotating over time with interpolation and locked axes could result in wrong rotations.
- Unity UI: UI Boxes, Ok/Cancel Buttons: Fixed an issue where the state of ok/cancel buttons wasn't updated on changes.


-------------------------------------------------------------------------------------------------------------------------------------------------------
Makinom 2.15.0
-------------------------------------------------------------------------------------------------------------------------------------------------------

New:
- Game States: 'Active State Schematics' and 'Inactive State Schematics' settings available. Optionally execute schematics when a game state is set active or inactive.
- UI Boxes: 'Block Focus Cursor Over' setting available. Optionally block the 'Focus Cursor Over' setting (defined in general settings for UI boxes) for the UI box. The UI box will not be focused automatically when the cursor is over it.
- Schematics: Read Text File, Read Text File Lines: 'Decrypt Data' setting available. Optionally decrypt the text using the save game encryption.
- Schematics: Write Text File, Write Text File Lines: 'Encrypt Data' setting available. Optionally encrypt the text using the save game encryption.
- Auto Machines: Notify Start: 'Game State Change' type available. Start the machine when defined game state changed to a defined state.
- Unity UI: Schematics: Spawn UI Prefab: 'Spawn UI Prefab' node available in 'UI > UI Components' nodes. Spawns a prefab on a selected UI layer's canvas.

Changes:
- HUDs: Navigation Bar: Navigation Points: HUD elements on navigation points can now access object variables and similar things in case the point represents a game object (e.g. 'Interactin' or 'Marker' nav points).

Fixes:
- Editor, Play Mode, UI: Fixed an issue where having the editor open while entering play mode could cause issues with the UI not being displayed. This only occured when using 'Domain Reload' in the Unity editor settings.
- Editor, Play Mode: Fixed an issue where using 'Accept Timeout' wasn't reset when stopping play mode and not using domain reload.
- Save Games: Fixed an issue where checking if continue is available could report false at the start of a game, even when a save game to continue was available.
- HUDs: Navigation Bar: Fixed an issue where position-based navigation markers could lead to an additional marker in their scene due to not cleaning out the previous scene's marker.
- Animations: Mecanim: Fixed an issue where animation durations where reported wrong when using a different animation speed.
- Selected Data: Fixed an issue where getting a single component from selected data was only returned when it was attached to a game object stored in selected data, not if the component itself was stored.
- Schematics: Fixed an issue where overriding a schematic's prefabs or audio clips on play could cause an error if the schematic doesn't have any defined prefabs/audio clips.


-------------------------------------------------------------------------------------------------------------------------------------------------------
Makinom 2.14.0
-------------------------------------------------------------------------------------------------------------------------------------------------------

New:
- Data Loading: Performance improvement when loading data (editor and in-game). Especially noticeable when opening large projects with hundreds or thousands of data assets in the Makinom editor.
- Editor: Loading/reloading data, checking for changes and saving now displays information screens instead of the settings. The editor is locked in any case, the new screens will inform you that an operation is in progress. Previously, larger projects had locked editors for some time without letting you know what's going on.
- Editor: Variable Fields: Improved performance of the variable popup fields.
- Game Controls: Interactions: Move To Interaction: 'Update Position' settings available. Optionally update the position the player moves to during movement. E.g. use this if your interactions are moving to let the player run after them.
- Game Settings: Performance Settings: 'Auto Data Init' setting available. Initializing the settings of data assets directly referenced by the project (e.g. input keys, music or HUDs) is now optional. Not loading the settings on project initialization will boost the initial project load time and loads the settings as needed (i.e. when they're first accessed). By default disabled, i.e. not loading settings on project init (change to previous behaviour).
- Asset Sources: Asset Bundle: Asset bundles now support using sub assets. E.g. using sprites from a sprite sheet is now possible.
- Math Functions: 'Root Of Value' and 'Root By Value' functions available. Finds the nth root of the value (e.g. x^1/3) or uses the value as root of a defined  (e.g. 3^1/x).
- Float Operators: 'Root' float operator available. The nth (value) root of the current value (i.e. current value ^ 1/value).
- HUDs: Conditional Schematics: 'Auto Stop' settings available for 'Valid Schematic' and 'Invalid Schematic' settings. Optionally stop a running valid/invalid schematic when the condition changed (i.e. stopping valid schematic when becoming invalid or stopping invalid schematic when becomming valid).
- Schematics: Stop Machine: 'Stop Machine' node available in 'Machine' nodes. Stops running machines/schematics that use a defined schematic asset and machine object.
- Untiy UI: HUD Click Component: Schematic: The HUD's user/content is now available as local selected data via the data key 'action'.
- Inspector: Makinom Object: Listed running machins/schematics can now be stopped via a button.
- Scripting: HUDs: The 'HUDInstance' class now has functions to access the HUDs displaying content. They're returned as 'IUIBase' interface instances, which are in most cases 'IHUD' interfaces (e.g. the 'HUD' component when using 'Unity UI' module), but can also be 'IUIBox' interfaces (e.g. 'Console' HUD in ORK Framework).

Changes:
- Project Initialization: The directly referenced data assets no longer automatically load their settings on project initialization (in-game). This improves initial load time and loads the settings as needed. To restore the previous behaviour (i.e. loading the settings on project init), use the new 'Auto Data Init' setting in 'Game > Game Settings' in the 'Performance Settings'.
- Unity UI Components: HUD Click, HUD Button Control Content, HUD Axis Control Content, HUD Joystick Control Content: Using clickable HUD content as part of a UI box's inputs will now forward click-selection to the parent input.

Fixes:
- Schematics: Start Machine: Fixed an issue where the 'Resource Overrides' settings where not used.
- Schematics: Show Dialogue: Fixed an issue where descriptions of 'Choice' dialogues didn't use the correct variable origin or replace actor text codes.
- Unity UI: UI Selection Cursor Component: Fixed an issue where enabling 'Show Only Focused' didn't hide the cursor of an unfocused UI box when first showing it or updating the displayed content.


-------------------------------------------------------------------------------------------------------------------------------------------------------
Makinom 2.13.0
-------------------------------------------------------------------------------------------------------------------------------------------------------

New:
- Child Objects: New child object settings available. Beside defining the 'Path' to the child object, you can now also find child objects by 'Name' or 'Tag'. Available everywhere child objects can be defined. Previous settings are automatically updated to 'None' or 'Path' (depending if a path was defined or not).
- Input Keys: Key Code, Mouse, Touch, Unity Input Manager: 'Max Hold Time' setting available (all input handlings except 'Down'). Optionally use a maximum time the input can be held to recognize the input.
- Schematics: Select Game Object: New 'Use Child Objects' settings available. You can now use the 'Direct Children', 'All Children' or 'Defined' children (e.g. all child objects with a defined tag) of a game object.
- Unity UI: HUD Value Bar Content Components, HUD Icon Bar Content Components: 'Value Type' setting available. The value/icon bar content now supports values from different origins. Previous settings will be updated to 'Value' type.
- Unity UI: HUD Value Bar Content Components, HUD Icon Bar Content Components: 'Value' value type available. Displays a defined value (i.e. the previous settings of the value bar components).
- Unity UI: HUD Value Bar Content Components, HUD Icon Bar Content Components: 'Input Hold Time' value type available. Displays the time an input key has been held down (only for input keys using a hold time).

Changes:
- Reflection: Reflection calls (e.g. from function nodes in schematics) will now output a warning to the console if a component isn't found on a game object.

Fixes:
- Component Conditions: Fixed an issue where setting an 'Auto Fail Action' and afterwards setting 'Auto Check' to 'None' still used the auto fail action when the conditions where invalid.
- Auto Save Slot Dialogue: Fixed an issue where canceling out of the dialogue still used the slot.
- Game Object Pools: Fixed an issue where disabling a pooled game object after time could lead to an error if the game object was disabled.


-------------------------------------------------------------------------------------------------------------------------------------------------------
Makinom 2.12.0
-------------------------------------------------------------------------------------------------------------------------------------------------------

New:
- Editor: Popup Fields: Favorites: Popup selection fields and node editor context menus allow you to mark selects as favorites. Favorite selections will be listed under 'Favorites' at the top of the popup/context menu. Please note that this isn't available for asset selection popups (e.g. input keys) and certain editor popups (e.g. layer selection in node editor).
- Editor: Foldouts: Foldouts can be tinted in a defined color. The color selection is located in the upper right corner of the foldout. This can be disabled in the editor settings.
- Editor Settings: Foldout Settings: 'Use Foldout Colors' settings available. Optionally allow setting tint colors for foldouts. The color can optionally also be used as text color in the foldout jumplist. The color can be set in the upper right corner of a foldout.
- Playing Music: 'Play From' setting available. Play the music from the 'Start', the 'Current Channel Time' or the 'Stored Time Position'.
- Music: 'Store Time Position' setting available. Automatically store the current time position of the music clip when stopping or changing to a different music clip. The music can restart from the stored time position by using the 'Play From' setting in the play music settings.
- Save Game Settings: PlayerPrefs Game Options: 'Text Speed' setting available. The 'Text Speed' option will also be stored in the PlayerPrefs (outside of save games).
- Schematics: Store Music: 'Only Store Time Position' setting available. Only store the time position of the channel's currently playing music clip instead of the clip and time position. The music can restart from the stored time position by using the 'Play From' setting in the play music settings.
- Schematics: Add Interaction Controller: 'Add Interaction Controller' node available in 'Game Object > Component' nodes. Adds the interaction controller prefab to a game object (if no 'Interaction Controller' component is found on it) and registers the game object's interaction controllers with the system.
- Schematics: Remove Interaction Controller: 'Remove Interaction Controller' node available in 'Game Object > Component' nodes. Disables all 'Interaction Controller' components on a game object an unregisters them from the system. 
- Schematics: Register Other Player: 'Register Other Player' node available in 'Game > Player' nodes. Registers or unregisters a game object as an other player. Other players are able to start trigger/collision interactions that can otherwise only be started by the main player.
- Schematics: Add Player Components: 'Add Player Components' node available in 'Game Object > Component' nodes. Adds the player components, control machines and control behaviours that are set up in 'Base/Control > Game Controls' to a game object.
- Schematics: Remove Player Components: 'Remove Player Components' node available in 'Game Object > Component' nodes. Removes the player components, control machines and control behaviours that are set up in 'Base/Control > Game Controls' from a game object.
- Schematics: Start Machine, Add Machine To Stack: 'Resource Overrides' settings available. Optionally override prefabs and audio clips of the used schematic.
- Unity UI: UI Content (TextMesh Pro) Component: 'Typewriter Adjust Size' setting available. Using typewriter will set the actual text instead of the visible characters. This adjusts the size of the displayed text, otherwise the size of the full text is used from the start. Please note that this can impact performance and should only be used if your content's size adjusts.
- Unity UI: HUD Tooltip (Content Provider) Component: 'HUD Tooltip (Content Provider)' component available. Uses the current tooltip content as content.
- Editor Data Asset: 'Clear Foldout States' button available. Removes all stored foldout state data (expanded state, colors).
- Editor Data Asset: 'Clear Popup Favorites' button available. Removes all favorited popup selections.

Changes:
- Playing Music: 'From Current Time' setting has been replaced by the 'Play From' setting. Previous data is updated automatically.
- Schematics: Store Music: Storing music will also store the time position for the music clip independent of the music channel. The music can restart from the stored time position by using the 'Play From' setting in the play music settings.
- Unity UI: Screen Fader: Changed how the screen fader's 'RectTransform' is set up. Should now cover all screen sizes and aspect ratios.
- Unity UI: The 'HUD Content Provider (GameObject)' has been renamed to 'HUD Game Object (Content Provider)'.

Fixes:
- Editor: Saving: Fixed an issue where new lines in names could cause issues when saving asset files.
- Editor: Asset Sources: Fixed an issue where changing the source didn't update the settings (e.g. automatically finding the asset bundle).


-------------------------------------------------------------------------------------------------------------------------------------------------------
Makinom 2.11.0
-------------------------------------------------------------------------------------------------------------------------------------------------------

New:
- Editor: Schematics: Dialogue Importer: New tool to import dialogues into multiple 'Show Dialogue' nodes available. Open the tool using the context menu option 'Open Dialogue Importer'. Comes with 2 built-in import models ('Separator' and 'Markup'), can be extended by custom formats.
- String Values, Variable Fields: The variable keys with local context are now automatically listed at the top of the variables/texts. The button popup beside the string/variable field will list matching scenes at the top under the 'Local' sub-menu. Local context refers to variables/texts used within the displayed settings (e.g. a schematic, formula or the game settings).
- String Values, Variable Fields: The names of scenes added to Unity's build settings are now automatically listed at the top of the variables/texts. The button popup beside the string/variable field will list matching scenes at the top under the 'Scenes' sub-menu.
- Editor Settings: Foldout Settings: 'Extended Information' setting available. Optionally display more information in the title of some foldouts, e.g. foldouts of array elements showing details on their setup. By default enabled.
- Content Layouts: 'Use Content Variables' setting avaialble. Optionally replace variable text codes with variables from the displayed content instead of global variables. If the content doesn't have variables, the text codes will be replaced with default values (e.g. int/float '0', bool 'false', etc.).
- Camera Positions: 'Orthographic Size' settings available. Optionally set the camera's orthographic size. Only used by cameras using 'Orthographic' projection.
- UI Boxes: Controls: 'Limit Accept Click Time' settings available. Optionally limit the time a click can be held to accept inputs.
- Editor: Foldouts: Many foldout titles of array elements display additional information about their settings when using 'Extended Information' in the editor settings.
- Schematics: Change Camera Position: 'Set Orthographic Size' settings available when using a target position instead of a camera position. Optionally set the camera's orthographic size. Only used by cameras using 'Orthographic' projection.
- Camera Events: 'Set Orthographic Size' settings available. Optionally set the camera's orthographic size. Only used by cameras using 'Orthographic' projection.
- Unity UI: HUD Value Bar (Slider): 'HUD Value Bar (Slider)' component available. Displays a value bar's content using a 'Slider' component.
- Unity UI: Context Menu: New variants available for 'Slider' value bars.

Changes:
- Interactions: Object Turn Settings: Using a 2D setup (i.e. 'Default Horizontal Plane' set to 'XY' in 'Game > Game Settings') will now rotate the game objects on their Z-axis to face the other game object's position.

Fixes:
- Mount Settings: The 'Scale' setting (available when enabling 'Set Scale') was wrongly named 'Rotation Offset'.
- HUDs: Fixed an issue that could cause errors when opening a HUD again before closing (animated via schematics) finished. Error appeared after closing the HUD again.
- Unity UI: UI Box Component: Input Settings, Tab Buttons: Fixed an issue where not having a 'Scroll Rect' selected was hiding some settings.
- UI Layouts: Fixed an issue where some layout configurations could lead to content that should be placed in the upper left corner of the screen not updating their positions correctly.
- Item Collectors: Fixed an issue where not interactable interactions could still fire their pre-interaction functionality (e.g. moving to interaction).


-------------------------------------------------------------------------------------------------------------------------------------------------------
Makinom 2.10.1
-------------------------------------------------------------------------------------------------------------------------------------------------------

New:
- Inspector: Component Conditions: 'Add In Control Condition' button available in 'Condition Settings' of components. Adds a game state condition checking for player control being active. Requires a game state condition that reacts to player control changes.

Changes:
- Interaction Controllers: Disabling the interaction controller (component or game object) will now remove all interactions that where recognized by it.

Fixes:
- Editor: Nodes: Fixed an issue where using the 'Add Node' button in the toolbar could cause an error.
- Editor: Opening a schematic via double click on it's asset or the 'Edit Schematic' button in it's inspector will now prompt a save dialogue in case the editor is already opened and has data changes (same as changing to schematic section).
- Unity UI: Fixed an issue with canvas sorting order in Unity 2022.2.


-------------------------------------------------------------------------------------------------------------------------------------------------------
Makinom 2.10.0
-------------------------------------------------------------------------------------------------------------------------------------------------------

New:
- Vector3 Values: Game Object Velocity: 'Game Object Velocity' value type available. Uses the velocity of a game object, coming from an 'Object Changes' component (will be added automatically if not on the game object).
- Float Values: Initial Value (Formula): 'Initial Value (Formula)' value type available. Uses the initial value of the formula (or sub-calculation) as value. Only used in formulas, 0 if used outside of formulas.
- Game Controls: Interactions: Auto Stop Interaction: 'Auto Stop Interaction' settings available. Automatically stop interactions when the player is a defined distance away from it. Has to be enabled in individual interaction components to use it.
- Schematics: Show Dialogue: 'Keep Open' setting available in 'Message' dialogue types with 'Wait' enabled. The schematic continues after accepting the dialogue, but the dialogue will remain open. Use a 'Close All Dialogues' node (or setting in the 'Settings' node) to close the schematic's still open dialogues.
- Schematics: Select HUD Content Provider: 'Select HUD Content Provider' node available in 'UI > UI Components' nodes. Use the content/user of a HUD content provider component attached to a game object to change selected data.
- Interaction Machine Component: Start Settings: 'Use Auto Stop Distance' setting available. Optionally use the auto stop distance defined in 'Base/Control > Game Controls' to automatically stop the interaction when the player is a defined distance away. Please note that this isn't used for 'Multi' execution types.
- Camera Events: 'Set Field of View' setting available. Changing the camera's field of view when using a game object for placement is now optional. By default enabled (previous behaviour).
- Unity UI: UI Line Renderer Component: 'UI Line Renderer' component available. Simple UI line renderer component for displaying straight or curved lines.
- Unity UI: UI Box Component: Input/Tab Settings: 'Scroll To Center' setting available. Scrolling to a selected input will try to center the view on it.
- Unity UI: UI Box Component: Input/Tab Settings: 'Scroll Padding' setting available (when not using 'Scroll To Center'). Scrolling to a selected input can use a padding to the edge of the scroll rect.

Changes:
- Unity UI: HUD Input Display (Sprite): Not using 'Cursor Over Image' changes will now fall back to the 'Non-Pressed Image' changes.

Fixes:
- Schematics: Is Music Playing: Fixed an issue where not checking for a specific music clip always resulted in 'Failed'.
- Camera Events: Fixed an issue where the camera event didn't work when the player controls wheren't blocked.
- Camera Events: Fixed an issue where the 'Update Position' setting wasn't displayed when using a camera position for the camera placement.
- Unity UI: HUD Icon Bar Content Components: Fixed an issue that caused an error when displaying icon bars.
- Unity UI: HUD Joystick Control Content, HUD Axis Control Content, HUD Button Control Content: Fixed an issue where closing the HUD during input doesn't reset the input.


-------------------------------------------------------------------------------------------------------------------------------------------------------
Makinom 2.9.1
-------------------------------------------------------------------------------------------------------------------------------------------------------

New: 
- Schematics: Select UI Base Component: 'Select UI Base Component' node available in 'UI > UI Components' nodes. Use the 'UI Box' or 'HUD' component (or anything implementing 'IUIBase') attached to a game object to change selected data. Allows using machine components on UI game objects to animate them.
- Schematics: Is UI Box Focused: 'Is UI Box Focused' node available in 'UI' nodes. Checks if a UI box stored in selected data is focused or not.
- Schematics: Rotate Component Settings: 'Is 2D Look At' setting available in all nodes using 'Rotate Component' settings to define how a game object is rotated. Ensures that 'look at' rotations are correct for 2D.
- Unity UI: UI Selection Cursor Component: 'Show Only Focused' setting available. Optionally only show the selection cursor while the UI box is focused. By default disabled (always showing).

Fixes:
- Editor: Fixed issues related to 'GetLastRect' errors in some Unity versions.


-------------------------------------------------------------------------------------------------------------------------------------------------------
Makinom 2.9.0
-------------------------------------------------------------------------------------------------------------------------------------------------------

New:
- Editor: Copy-Paste Context Menu: You can now use a context menu (via context-click) to copy settings to the clipboard and paste them on other, compatible settings.
- Editor: Sub-Sections: Search field available to search for sub-sections. Searches the sub-sections of all sections, listing the found matches. Clicking on one will open the section and sub-section.
- Float Values: Selected Data Count: 'Selected Data Count' value type available. Uses the number of content stored in a selected data key as value.
- Value Checks: Range Inclusive Lower: 'Range Inclusive Lower' check type available. Checks if the value is between the two defined check values, including value 1, excluding value 2 (>= v1, < v2).
- Value Checks: Range Inclusive Upper: 'Range Inclusive Upper' check type available. Checks if the value is between the two defined check values, excluding value 1, including value 2 (> v1, <= v2).
- Schematics: Shake Camera, Shake Object: 'Shake Rotation' setting available. Optionally shake the rotation of the camera/object instead of the position.
- Schematics: Value Option Dialogue: New text codes available. Use '<startvalue>' to add the original/start value and '<change>' to add the change (to the start value) in value inputs.
- Unity UI: HUD Selected Data List Component: 'HUD Selected Data List' component available. Lists content stored in selected data by spawning a prefab for each content. Using other content components on the prefab allows to display content information, e.g. name or icon of the content (if available).

Changes:
- Flying Texts: Schematics: Using a schematic to animate a flying texts will now set the initial color of the flying text to be fully transparent (A=0). I.e. you need to use a 'Change UI Color' node set or fade the color of the flying text. This is needed to prevent the flying text from initially display with full alpha for a frame before the schematic starts.
- Editor: Data Listing: Search, Filter & Sort: The search field is now always visible, filter and sorting options only when the 'Search, Filter & Sort' foldout is expanded.
- Unity UI: UI Boxes, HUDs: Switching between regular canvas and world space canvas now resets some rect transform values to those of the original prefab.
- Screen Fades: Screen fades are now performed using unscaled delta time.

Fixes:
- Schematics: Destroy Prefab: Fixed an issue where destroying all spawned prefabs (via prefab ID '-1') didn't destroy all instances.
- Screen Fades: Fixed an issue where screen fades starting while the game was paused didn't progress until the game is unpaused. Now, if a screen fade is started in pause, it'll also execute in pause.
- Unity UI Components: Added an automatic workaround for Unity UI components going missing when a project's library is rebuild.
- Unity UI: Inputs: Fixed an issue where drag-scrolling was interpreted as clicking on an input.


-------------------------------------------------------------------------------------------------------------------------------------------------------
Makinom 2.8.0
-------------------------------------------------------------------------------------------------------------------------------------------------------

New:
- Float Values: Move Speed: 'Move Speed' value type available. Uses the move speed (walk, run, sprint) of a game object (e.g. from 'Move Speed' component) as value.
- Input Keys: Mouse: 'Axis Without Input' setting available. The mouse axis input will be set without mouse button input, i.e. the axis is available at all times.
- Flying Texts: Object Creation: 'Follow World Position' setting available. Following the world position (on-screen) where the flying text is positioned is now optional. If disabled, the flying text will be initially displayed on-screen at the world position, but stay on it's screen position. The text can still be animated and moved using schematics. By default enabled (following world position).
- HUDs: Tooltip: 'Needed' setting available for tooltip checks. Defines if only one or all of the defined tooltip checks must be valid. Defaults to 'One' (previous behaviour).
- HUDs: Tooltip: 'Is Valid' setting available in tooltip checks. You can now define if the tooltip/content must or mustn't be of the defined type. Setting available for each tooltip check individually. By default enabled (must be defined type).
- Schematics: Shake UI Offset: 'Shake UI Offset' node available in 'UI' nodes. Shakes a UI box, HUD, flying text or anything implementing the 'IUIAnimation' interface stored in selected data. Shaking uses the animation offset of the UI (same as 'Change UI Offset' node).
- Follow Waypoint Path Component: 'Follow Waypoint Path' component available. Starts following a defined path when the scene is loaded (or after instantiating the object). Does the same as the 'Follow Path' node in schematics.
- Unity UI: HUD Drop Receiver Component: 'HUD Drop Receiver' component available. Uses the HUD's user (via the used content provider) as target of a drop action (used by 'IDragOrigin' implementations, calling the 'DroppedOnObject' function). HUD types with drop options will handle dropping automatically, but need this component when used as HUD templates for content in a UI box.
- Unity UI: HUD Condition Component: 'HUD User' condition type available. Checks the user of the HUD/content provider for being of a defined type. Uses 'Tooltip' type HUD content checks, but doesn't require being part of a 'Tooltip' type HUD.

Changes:
- Waypoint Paths: Inspector: 'Update Length' button available to update the displayed path length (doesn't update automatically).
- Unity UI: UI Boxes: Dropping something on a UI box will now also check HUDs that are added to the UI box (e.g. via HUD templates).
- UI Boxes: Inputs: Highlighted inputs now take the input state colors into account.

Fixes:
- Waypoint Paths: Fixed an issue where using rotation caused an error due to uninitialiized settings.
- Save Game Settings: Save/Load Menu: Fixed an issue where the cancel input key wasn't working in the save file selection.


-------------------------------------------------------------------------------------------------------------------------------------------------------
Makinom 2.7.0
-------------------------------------------------------------------------------------------------------------------------------------------------------

New:
- Game Settings: Data Settings: 'Data Format' setting available. Select which format the settings data will be stored in, either 'Byte Array' (current) or 'XML String'. Using 'XML String' allows merging data via version control products (e.g. GIT), but has slower save/load times. It's recommended to use 'Byte Array' for your built games to make use of the faster load times.
- Game Settings: 'Default Rounding' setting available. Defines the rounding used when other rounding settings use the 'Default' rounding.
- HUDs: Schematics: 'Conditional Schematic' settings available. Define conditions checking the HUD's content/user and schematics that'll be used when the conditions are valid or invalid. Like other HUD schematics, the HUD and content/user are available as selected data in the schematic, e.g. allowing to fade/scale the HUD based on the conditions.
- UI Settings, UI Boxes: Dragging Notification: 'Use Content Icon' setting available. Optionally uses the icon of the dragged content instead of the defined icon of the notification.
- Math Functions: Math functions have been replaced by an extensible class. Extend from the 'BaseMathFunction' class to implement custom Math function. Previous settings will be udpated automatically.
- Math Functions: 'Log' and 'Log Base' functions available. Uses the natural (base e) or defined base logarithm of the value.
- Math Functions: 'Raised To Power' and 'Raised To Value' functions available. Raises the value to a defined power (e.g. x^3) or raises a defined value to the power of the value (e.g. 3^x).
- Math Functions: 'Multiply', 'Divide' and 'Divide By Value' functions available. Multiply or divide the value by a defined value, or divide a defined value by the value.
- Math Functions: 'Add To Value', 'Subtract From Value' and 'Subtract Value' functions available. Add or subtract a defined value to/from the value, or subtract the value from a defined value.
- Math Functions: 'Modulo (%)' function available. Uses a modulo operation (the rest of a division), the value % a defind value.
- Schematics: Select Game Objects: 'Use Child Objects' setting available. Optionally use all direct child objects of the defined game object. I.e. this uses child objects that are parented to the defined game object, but not child objects of it's child objects.
- Schematics: Select UI Input Context: 'Select UI Input Context' node available in 'Value > Selected Data' nodes (only available when using 'Unity UI' module). Use the schematic context of a UI input component attached to a game object, e.g. get the context added to a choice button. The schematic context of an input is e.g. used by Makinom's extension 'ORK Framework' in various menus, e.g. 'Inventory' menus to add the item of a button.
- Unity UI: UI Button (Ok, Cancel) Component: 'Hidden By Inactive' setting available. Optionally hide ok/cancel buttons if their state is inactive (e.g. ok/cancel being blocked). By default enabled (hiding inactive buttons).
- Unity UI: UI Input Components: 'Add as Placed Input' button available for inputs added to a UI box but not yet added as placed input. Adds the input as a placed input to the 'UI Box' component's settings.
- Unity UI: UI Tab Button Components: 'Add as Placed Tab' button available for tab buttons added to a UI box but not yet added as placed tab. Adds the tab button as a placed tab to the 'UI Box' component's settings.
- Unity UI: HUD Value Bar (Sprite) Component: 'Fade Change' setting available. Optionally change the displayed value over time from current value to new value.
- Unity UI: HUD Text Content Component: 'Use Time Update' settings available. Optionally update the content in regular intervals.
- Unity UI: HUD List Components: 'Force Prefab' setting available. Force using the prefab defined in the component instead of a prefab provided by the displayed content.
- Editor: Saving: Performance improvement when saving changes.

Changes:
- UI: Dialogues: Most dialogues will now allow showing ok/cancel buttons in any case. The ok/cancel buttons of the UI box handle their display according to their own setup (e.g. hidden by inputs or inactive state). For the 'Unity UI' module, this is handled by the 'UI Botton (Ok, Cancel)' component of the UI box's ok/cancel buttons.
- Math Functions: 'Plus1', 'Plus90' and 'Plus180' functions have been replaced by 'Add To Value' function. Previous settings will be updated automatically.
- Math Functions: 'Minus1', 'Minus90' and 'Minus180' functions have been replaced by 'Subtract From Value' function. Previous settings will be updated automatically.
- Editor: History: The 'Schematics' section will no longer be remembered in history navigation.
- Unity UI: HUD Text Content Component: 'Update Every' is no longer displayed when a time-based text code is used in the text. Instead it's now displayed (and used when 'Use Time Update' is enabled).

Fixes:
- Editor: Schematics: Fixed an issue where exiting play mode while having the schematic editor open could cause errors.
- Inspectors: Prefabs: Fixed an issue where changing settings on a prefab's component outside of prefab edit mode could lead to changes not being saved when closing Unity.
- Editor: Playing: Fixed an issue that could cause errors with starting coroutines when stopping play in the Unity editor.
- Node Editor: Fixed an issue where selecting a node group caused errors.
- UI: Custom Inputs: Fixed an issue where custom input settings without a custom input could override already assigned custom inputs.
- Unity UI: HUDs: Fixed an issue where some HUD components could be updated too late when spawned.
- Unity UI: HUD Tooltip: Fixed an issue where closing the HUD while displaying a tooltip (cursor over) didn't remove the tooltip.


-------------------------------------------------------------------------------------------------------------------------------------------------------
Makinom 2.6.1
-------------------------------------------------------------------------------------------------------------------------------------------------------

Fixes:
- Data Assets: Fixed an issue where changing the name in the editor could lead to changes not being saved.


-------------------------------------------------------------------------------------------------------------------------------------------------------
Makinom 2.6.0
-------------------------------------------------------------------------------------------------------------------------------------------------------

New:
- Languages: Export: 'File Per Sub-Section' setting available for exporting settings. You can now export language data from settings into separate files for each editor sub-section, each language file will be named after the data it contains.
- Languages: Export: 'File Per Schematic' setting available for exporting schematics. You can now export language data from schematics into separate files, each language file will be named after the schematic it belongs to.
- Languages: Export: Optionally export a defined single schematic instead of all schematics in a folder.
- Languages: Import: Optionally import to a defined single schematic instead of all schematics in a folder.
- Formulas: Select Selected Data: 'Select Selected Data' node available in 'Selected Data' nodes. Uses the first, last, a random or all content from another selected data as selected data.
- Global Machines: Auto: 'Unity Time Scale' and 'Makinom Time Scale' settings available for 'Auto' global machines. Defines if the timeout between checks is influenced by the Unity and Makinom time scales, e.g. disable both to check in unscaled time (also during freeze pause). By default enabled.
- Editor: GUID: The GUID settings at the top of data with a GUID now has a button to automatically set the GUID using the content's name.
- Editor Settings: Node Editor: 'Show Node Folder Info' setting available. Optionally show display the folders/sub-folders in which a node can be found at the top of the node's settings.
- Schematics: Set Screen Color: 'Set Screen Color' node available in 'Animation > Fade' nodes. Set's the screen fade color, canceling ongoing screen fades.
- Schematics: Value Option Dialogue: Accept, Cancel: 'Accept Only Here' and 'Cancel Only Here' settings available in the 'Accept' and 'Cancel' inputs. Optionally only allow accepting/canceling the dialogue via the added input buttons.
- Schematics: Activate Constraint: 'Activate Constraint' node available in 'Game Object > Constraint' nodes. Activates or deactivates defined constraint components.
- Schematics: Is Constraint Active: 'Is Constraint Active' node available in 'Game Object > Constraint' nodes. Checks if defined constraint components are active.
- Schematics: Lock Constraint: 'Lock Constraint' node available in 'Game Object > Constraint' nodes. Locks or unlocks defined constraint components.
- Schematics: Is Constraint Locked: 'Is Constraint Locked' node available in 'Game Object > Constraint' nodes. Checks if defined constraint components are locked.
- Schematics: Set Constraint Weight: 'Set Constraint Weight' node available in 'Game Object > Constraint' nodes. Sets the weight of defined constraint components.
- Schematics: Check Constraint Weight: 'Check Constraint Weight' node available in 'Game Object > Constraint' nodes. Checks the weight of defined constraint components.
- Schematics: Add Constraint Source: 'Add Constraint Source' node available in 'Game Object > Constraint' nodes. Adds a transform source to defined constraint components.
- Schematics: Remove Constraint Source: 'Remove Constraint Source' node available in 'Game Object > Constraint' nodes. Removes a transform source from defined constraint components.
- Schematics: Is Scene Stored: 'Is Scene Stored' node available in 'Game > Scene' nodes. Checks if a scene is currently stored.
- Schematics: Is Scene Position Stored: 'Is Scene Position Stored' node available in 'Game > Scene' nodes. Checks if a scene position for a scene is currently stored.
- Scripting: Extensions: 'MakinomEditorExtension' and 'MakinomDataExtension' classes to extend Makinom with custom editor and data functionality now has a 'Sorting' setting to manage the order of multiple extensions.

Changes:
- UI Layers: The default layer (used as fallback) is no longer created. Instead, the first UI layer will be used as default layer. The default layer is used when some UI content didn't select a UI layer to be placed on and didn't have any settings.
- UI Boxes: Unity UI: Small performance improvement when listing inputs in a UI box (e.g. buttons).
- Editor: Foldouts: Foldout headers now show tooltips for their help texts.

Fixes:
- UI Boxes: Unity UI: Fixed an issue where using HUD templates on buttons could lead to a short flicker when updating the content of the UI box (e.g. in menu screens).
- Schematics: Call HUD: Fixed an issue where using 'Change Users' to 'Clear' the users caused an error.
- Object Changes: A time scale of 0 caused 'NaN' values for horizontal and vertical speed.
- Editor: Fixed an issue where moving a data asset in the list could lead to data loss when saving.


-------------------------------------------------------------------------------------------------------------------------------------------------------
Makinom 2.5.0
-------------------------------------------------------------------------------------------------------------------------------------------------------

New:
- Prefab Savers: 'Prefab Savers' sub-section available in the 'Base/Control' section. Save position, rotation, scale or local object variables of prefab instances and their child objects when changing scenes and saving the game. Spawning prefabs via 'Spawn Prefab' nodes have to enable adding the prefab instance to prefab savers, and the spawned prefab must match one of the defined prefab saver prefabs.
- Vector3 Values: Random Value: 'Random Value' value type available. Generates a random value between a defined minimum and maximum Vector3 value.
- Vector3 Values: Scene Position: 'Scene Position' value type available. Uses the stored scene position of the current scene or a defined scene name as value.
- Vector3 Values: Random Inside Sphere, Random On Sphere, Random Rotation, Random Rotation Uniform: 'Multiply By' setting available. Multiply the random value by a defined value (float value selection). Defaults to 1 (i.e. using the random value without any change).
- Game Object Saver Components: 'Game Object Saver' component available. Save position, rotation, scale or local object variables of game objects and their child objects in your scene. Also saves data from attached components implementing the 'IComponentSaveData' interface. Only works for game objects already placed in your scenes, not for game objects spawned in-game.
- Formulas: Begin Sub Calculation: 'Begin Sub Calculation' node available in 'Base' nodes. Starts a new calculation as part of the formula (i.e. used as 'left parenthesis'). Everything within a sub calculation will be calculated until it's corresponding 'End Sub Calculation' node and added to the previous formula value using the defined operator.
- Formulas: End Sub Calculation: 'End Sub Calculation' node available in 'Base' nodes. Closes the last opened sub calculation (i.e. used as 'right parenthesis'). The sub calculation's current value (result) will be used to change the previous formula value using it's corresponding 'Begin Sub Calculation' node's operator.
- Save Game Settings: 'Game Object Savers' settings available in the save settings. Defines if data from 'Game Object Savers' is saved with save games.
- Save Game Settings: 'Prefab Savers' settings available in the save settings. Defines if data from prefab savers is saved with save games.
- UI Boxes: Controls: 'Select Left Click', 'Select Middle Click' and 'Select Right Click' settings available. Define if clicks will select inputs. The settings are only available if their equivalent 'Accept Click' setting is disabled, e.g. disable 'Accept Middle Click' to have 'Select Middle Click' available.
- UI Boxes: Dragging Notification: 'Dragging Notification' settings available in the override settings. Optionally use a custom dragging notification to display dragged content coming from the UI box. The default dagging notification is set up in 'UI > UI Settings'.
- Game Controls: Object Selection: Mouse/Touch Control: 'Use Cursor Over' setting available. Optionally select game objects when the cursor is over them instead of using click/touch interaction.
- Schematics: Prefabs: 'Remove Prefab Saver' setting available when destroying prefabs at the end of the schematic or using 'Auto Destroy After Time'. Removes the destroyed prefab instance from prefab savers it might be added to. By default enabled.
- Schematics: Spawn Prefab, Fill Grid Cells: 'Add Prefab Saver' setting available. Adds the spawned prefab to it's matching prefab saver, saving it's data (e.g. position) on scene changes, respawning it when returning to the scene. By default disabled.
- Schematics: Destroy Prefab, Destroy Object: 'Remove Prefab Saver' setting available. Removes a game object from being saved by prefab savers (if added). Otherwise destroying the game object would trigger saving it's data if it's added to a prefab saver. By default enabled.
- Schematics: Remove From Prefab Saver: 'Remove From Prefab Saver' node available in 'Game Object > Prefab' nodes. Remove a game object from being saved by prefab savers. Either remove all in the current scene, only from a defined prefab saver or from a defined game object.
- Schematics: Clear Prefab Savers: 'Clear Prefab Savers' node available in 'Game Object > Prefab' nodes. Removes stored data from prefab savers in a defined scene or all scenes.
- Schematics: Clear Game Object Savers: 'Clear Game Object Savers' node available in 'Game > Scene' nodes. Removes stored data from 'Game Object Saver' components in a defined scene or all scenes.
- Schematics: Stop Global Machine: 'Stop All' setting available. Optionally stop all global machines instead of a defined global machine.
- Schematics: Apply Builtin Root Motion: 'Apply Builtin Root Motion' node available in 'Animation > Mecanim' nodes. Apply an 'Animator' component's built-in root motion. Use this in schematics started by the 'Animator Move' start type of 'Animation Machine' components or the 'State Move' start type of 'Animator State Machine' components to use the default root motion.
- Schematics: Animation Nodes: 'Scope' setting available in most legacy and Mecanim animation nodes. Define the scope of 'Animation' and 'Animator' components that will be used in the game objects (e.g. in child objects).
- Schematics: Emit Particles: Now supports 'Visual Effect' components.
- Scripting: 'IComponentSaveData' interface available. Add data from components to save games, e.g. when saving game objects using a 'Game Object Saver' component.

Changes:
- Editor: Saving: Settings assets (e.g. the game settings) are no longer saved each time the project is saved, only when their settings where changed.
- Editor: Selected Data: The selected data origin now defaults to 'Local' for new settings (e.g. in schematic nodes).
- Editor: Selected Data: Selected data changes now default to 'Set' instead of 'Add' for new settings (e.g. in schematic nodes).
- Editor: Schematics: Live Debugging: The 'Local Variables' debug information is now also shown with or without a node being selected. Previously it was only shown when no node was selected.
- Game Settings: The 'Visibility Check' now defaults to 'Camera Viewport' instead of 'Renderer Is Visible'.
- Global Machines: Stopping the game or loading a save game will now stop all running global machines.
- Unity UI: HUDs: If no 'UI Receive Cursor Events' component is added to the HUD when registering drop targets, one is added automatically.
- Schematics: When changing scenes the machine object will now be unparented before marking it as 'DontDestroyOnLoad'.

Fixes:
- Schematics: Spawn Prefab: Fixed an issue where spawning on a 'Spawn Point' always used a scale of 1 instead of the prefab's scale.
- Unity UI: Cursor Over: Fixed an issue where the cursor being over child objects no longer registered as being cursor over in UI boxes and HUDs in Unity 2021.2 and newer.
- Unity UI: UI Boxes: Fixed an issue where icons in texts (e.g. via text codes) where not color faded when first initialized.
- Data Assets: Fixed an issue where a (re)import of a data asset didn't clear the cached data. E.g. copying over the file with a backup or using sharing solution (e.g. GIT) could keep the last saved data cached (done to improve loading times in the editor).


-------------------------------------------------------------------------------------------------------------------------------------------------------
Makinom 2.4.1
-------------------------------------------------------------------------------------------------------------------------------------------------------

Fixes:
- Game States: Fixed an issue where the 'Game Running' state (or auto activating/deactivating a state via 'Start Game') wasn't set when loading a saved game.
- Editor: Schematics: Debugging: Fixed an issue where not using 'Domain Reload' in Unity didn't clear debug information from previous runs. Debug information is now removed when playing in the editor.
- Editor: Search: Fixed an issue where search highlights where no longer displayed after opening a popup selection (e.g. asset selection or dropdown menu).
- UI Boxes: Fixed an issue where focus changes could cause errors in some cases.
- Schematics: Fixing an issue where an eternal loop in a stopped schematic leads to errors.


-------------------------------------------------------------------------------------------------------------------------------------------------------
Makinom 2.4.0
-------------------------------------------------------------------------------------------------------------------------------------------------------

New:
- Float Values: 'Angle (Position)' value type available. Uses the angle from a game object to a position as value.
- Float Values: Float Variable, Int Variable: 'Multi Value Use' setting available when using 'Object', 'Object List', 'Selected Data' or 'Selected Data List' variable origin. Defines how values from multiple variable sources will be used, e.g. using the first found or adding them all together. Defaults to using the 'First' value (previous behavious).
- Vector3 Values: Cursor Position (World): 'Use Raycast' setting available in 'Cursor Position (World)' value type. Using a raycast to find the world position of the cursor is now optional. By default enabled (previous behaviour).
- Flying Texts: Flash: 'Set Property' settings available. Optionally set a property instead of the color when flashing a flying text's game object.
- Editor: 'Editor Name' setting available in all data with language content definition. Optionally define a different name to be used in the editor (e.g. in popup lists). Can be used to use a different internal name without impacting the content the player will see.
- Editor Settings: Node Editor: 'Initial Node Anchor' and 'Focus Node Anchor' settings available. Select where the selected nodes will be displayed when first displaying and focusing on selected nodes (via 'F' key). E.g. display them in 'Middle Center' (default) or 'Upper Left' of the grid.
- UI Boxes: Controls: Input Settings: 'Inactive Input Handling' setting available. Define how inactive/disabled inputs are handled. Either use them like regular inputs ('None', displays them and allows selecting them), 'Skip Selection' (displays them but skips over them when selecting inputs) or 'Hide' the inactive inputs.
- UI Boxes: Controls: Tab Settings: 'Inactive Tab Handling' setting available. Define how inactive/disabled tabs are handled. Either use them like regular tabs ('None', displays them and allows selecting them), 'Skip Selection' (displays them but skips over them when selecting tabs) or 'Hide' the inactive tabs.
- Schematics: Change Animator Controller: 'Change Animator Controller' node available in 'Animation > Mecanim' nodes. Changes the animator controller used by a game object's 'Animator' component. Supports animator controllers and animator override controllers.
- Schematics: Store Position Angle: 'Store Position Angle' node available in 'Movement > Rotation' nodes. Stores the angle from a game object to a position into a float variable.
- Schematics: Rotate to: 'Is 2D' setting available. Optionally rotate in 2D space, only rotating on the Z-axis.
- Schematics: Dialogue Nodes: 'In Pause' setting available. Defines if a UI box can be controlled when the game is paused. Defaults to 'Auto', allowing control if the game is paused when displaying the UI box.
- Is Makinom Camera Component: 'Is Makinom Camera' component available. Sets the 'Camera' defined in the component as Makinom's camera. E.g. use this when spawning the camera with the player, while having another camera in the scene.
- No Tooltip Component: 'No Tooltip' component available. Prevents game objects in a scene to cause tooltips when the cursor is over them. Not used for UI game objects, only for scene game objects.
- Interaction Components, Machine Components: Object Turn Settings: 'Reset Rotation' setting available when using 'Turn Machine Object'. Optionally restore the rotation of the machine object after the interaction/machine finished executing.
- Unity UI: No Fade Component: 'No Fade' component available. Prevents the game object from being color faded. E.g. use it for the checkmark of a toggle to prevent it from being faded when fading child objects of the input.
- Unity UI: Limit Content Size Fitter: 'Limit Content Size Fitter' component available. Works like a 'Content Size Fitter' component and adds min/max values for width and height (when using horizontal/vertical constraints).
- Unity UI: Circle Layout Group: 'Circle Layout Group' component available. Arranges content in circles around the UI's pivot.

Changes:
- Editor: Search Highlights: Highlighted settings will now highlight the background when using Unity's 'Light' theme and the foreground when using Unity's 'Dark' theme.
- Node Editor: Displaying the node/slot detail text will now catch any errors occuring due to broken node setups. A warning in the Unity console will hint at which node/slot is causing issues.
- Unity UI: UI Box: Input Settings: Setting 'Horizontal Input Change' and 'Vertical Input Change' to 0 will now use a direction-based input selection.
- Unity UI: Context Menu: Creating a 'Toggle' input adds 'No Fade' and 'No Flash' components to the toggle's checkmark to prevent issues with color fading.
- Data Assets: Individual data assets no longer store the version and save time. The project asset and schematic assets still store them.
- Movement Components: Default: 'Face Direction' now rotates on the Z-axis when using 2D movement (i.e. using a 'Rigidbody 2D' component for movement).
- Schematics: A disabled node being skipped a 2nd time now only stops the schematic if it's a node with multiple next slots. This is a safeguard to prevent schematics from executing forever. Previously this was done with any disabled node.

Fixes:
- UI Boxes, HUDs: Fixed an issue where UI displaying at a game object's position (not using world space canvas) where displayed when they're positioned behind the camera.
- Schematics: Call HUD: Fixed an issue where setting a HUD's user didn't work correctly.
- Editor: Formulas: Fixed an issue where removing a formula didn't update the node editor to display the next selected formula.
- Schematics: Change Sound Volume: Fixed an issue where fading a defined audio channel didn't change the volume.
- Schematics: Change UI Color: Fixed an issue where using 'Stop' fade type caused UI issues in the editor.
- Formulas: Loop: Fixed an issue where using the loop node caused freezing when using local variables for the counter.
- Animation Machines: Fixed issues when using 'Animation Event (int)', 'Animation Event (float)' and 'Animation Event (string)' start types without checking the parameter.
- Animation State Machines: Fixed issues with all start types when not using hash/layer index checks.
- Asset Sources: Asset Bundles: Fixed an issue where assets from asset bundles wheren't loaded correctly when playing in the Unity editor without domain reload.


-------------------------------------------------------------------------------------------------------------------------------------------------------
Makinom 2.3.0
-------------------------------------------------------------------------------------------------------------------------------------------------------

New:
- Unity: Domain Reload: Entering play mode without reloading the domain is now supported. You can find Unity's play mode settings in the Unity menu 'Edit > Project Settings... > Editor'.
- Editor: Search: Improved searching in a tab's settings. Search now highlights matching settings. The search bar will show how many matching settings where found and allows navigating between them via buttons. Use 'F3' and 'Shift + F3' to navigate highlighted settings via keyboard shortcuts. Jump list will highlight which foldouts contain found settings.
- Editor Settings: 'Search Settings' available. Define highlight colors and if entering search should automatically jump to the first found setting.
- Float Values: 'Angle' value type available. Uses the angle between two game objects as value.
- Flying Texts: Count To Value: 'No Zero' setting available. Counting doesn't start at 0, unless the final value is 0. The minimum start count is 1 (or -1 for negative values).
- Game Controls: Interactions: 'Line Of Sight' settings available. Optionally check the line of sight between the player and the interaction to determine if the interaction is available.
- UI System: Unity UI: 'Add Canvas Scaler' setting available. Adding a canvas scaler to automatically created canvases is now optional. However, it's recommended to use it and use prefabs in your UI layers for other canvas setups instead.
- Unity UI: UI Box Component, HUD Component: Position Settings: 'At Game Object Canvas' setting available. Optionally transfer the UI to a 'Canvas' of a game object when displaying it at that game object's position (e.g. dialogue speaker or HUD user). Allows using world space UI. By default enabled.
- Unity UI Setup: Context Menu: 'Text+Icon Content' and 'Icon Content' entries available in 'Makinom > HUD > Content'. Creates 'HUD Text Content' component game objects with text and icon or icon only configuration.
- HUD Click: 'Click Count' setting available. Define the number of clicks that are needed to use a HUD click action. Available in the 'HUD Click' component for the 'Unity UI' module.

Changes:
- Editor: The section buttons switch to only showing icons when the editor window's width is too small.
- Editor Settings: Hotkey Settings: 'Section' hotkeys removed due to clashing with some Unity hotkeys.
- Data Assets: Settings that where previously part of the project asset have been moved to individual settings assets. The project asset now only holds references to other data assets. This allows easier working in teams via collaboration tools (e.g. GIT). Your previous settings will still load and automatically be updated when saving the project again.

Fixes:
- Editor: Fixed an issue where keeping the editor docked when entering play mode could lead to inconsistencies between play and editor data lists.
- Animation Machine, Animation State Machine: Fixed errors that where caused by unused start types.


-------------------------------------------------------------------------------------------------------------------------------------------------------
Makinom 2.2.0
-------------------------------------------------------------------------------------------------------------------------------------------------------

New:
- Editor Settings: 'Copy Name Type' setting available. Select how the name of copied data will be changed. Either keep the original name ('None') or add 'COPY' to the front or back of the name.
- Interaction Controllers: 'Priority' setting available. Multiple interaction controllers on the player will be sorted by priority - the highest priority value will be used/checked first.
- Interaction Controllers: 'Limit Machine Types' settings available. Optionally limit the machine types an interaction controller can interact with.
- Float Values: Math Function: 'Minus 90', 'Plus 90', 'Minus 180' and 'Plus 180' math functions available. Adds/subtracts 90 or 180 from the value.
- Flying Texts: 'Create Object' settings available in 'UI > UI Settings > Flying Text Settings' and individual flying texts (overriding default). Define if a game object is created to position the flying text on screen based on a world position. 'None' doesn't create one and only allows changing the UI via the 'Change UI' nodes, 'Empty' creates an empty game object and 'Prefab' creates an instance of a prefab (e.g. to add physics or world space canvas).
- UI Boxes: 'Use Accept Timeout' settings available. Optionally use a (global) timeout for allowing accepting of UI content. This is used for all types of accepting, i.e. 'Accept Key' and 'Accept Click' on inputs.
- Movement Components: Nav Mesh Agent: 'Stop Immediately' setting available. Optionally stop the agent immediately by setting the velocity to 0 when stop is called. E.g. used by the 'Move To Interaction' settings.
- Movement Components: Default: 'Add Component' now lets you define the settings for the component. Will only be used when adding the component, otherwise keeps the settings of an already attached component.
- Movement Components: Default: Now supports 2D movement when a 'Rigidbody 2D' component is attached.
- Schematics: Change Last Move Direction: 'Change Last Move Direction' node available in 'Movement > Movement' nodes. Changes the last move direction value of 'Object Changes' components. This value can e.g. be used to determine the direction in 2D environments. Please note that the last move direction will automatically be updated by the component the next time the game object moves.
- Schematics: Store Angle: 'Store Angle' node available in 'Movement > Rotation' nodes. Stores the angle between two objects into a float variable.
- Face Camera Component: 'Face Camera' component available. Lets the game object face the Makinom camera or main camera in the scene. E.g. use it to let a world space canvas or sprite always face the camera.
- Scripting: Asset Sources, Asset Selections: 'SetAsset' function available in 'AssetSource' and 'AssetSelection' classes. Sets the source to 'Reference' and uses the provided asset.

Changes:
- Node Editor: 'Place on Grid' entry in the context menu has been renamed to 'Snap nodes to grid'.
- Editor: Remembering sections now also remembers the view limit state.

Fixes:
- Priority Machines: Fixed an issue where priority sorting was wrong.


-------------------------------------------------------------------------------------------------------------------------------------------------------
Makinom 2.1.0
-------------------------------------------------------------------------------------------------------------------------------------------------------

New:
- Unity 2019.4: Makinom now requires at least Unity 2019.4.
- Editor Settings: 'Sub-Section Separators' setting available. Adds separators between sub-section groups instead of showing all in a list of buttons. By default enabled.
- Editor Settings: 'Settings Buttons' and 'Search Bar Buttons' settings available when using 'Allow View Limiting'. 'Search Bar Buttons' shows the new buttons in the search bar to limit the view to the root settings foldouts. 'Settings Buttons' shows the view limit buttons above and below the settings area.
- Editor Settings: 'Scroll Index Change' setting available. Keep the scroll position consistent when changing data list entries. By default enabled.
- Editor Settings: Hotkey Settings: 'Settings Scroll' setting available. Use 'Home' to scroll to the top and 'End' to scroll to the bottom of the settings area. By default enabled.
- Editor Settings: Node Editor Settings: 'Scroll Trackpad Mode' settings available. Optionally use the scroll wheel input (e.g. when using a trackpad) to scroll the node area instead of zooming.
- Editor Settings: Node Editor Settings: 'Hold Shift Zoom' setting available. Optionally only zoom using the scroll wheel while holding the 'Shift' key.
- Editor Settings: Node Editor Settings: 'Auto Start Debugging' setting available. Automatically start debugging the first running instance of the currently opened schematic.
- Editor Settings: 'Max Depth' setting available when showing the jump list. Limits the depth of displayed foldouts in the jumplist. Defaults to 3.
- Editor: Settings Search Bar: 'Show Foldout Buttons' toggle available. Shows or hides the view limit buttons for the root settings foldouts in the search bar.
- Editor: Text Areas: Double clicking on a text area will now start editing it. Same as clicking on the 'Edit Text' button.
- Editor: Music: Loops: 'Test Loop' button available. Test the defined loop in the editor. Please note that due to playing in edit mode the result might not be 100% accurate.
- Game Starter: 'Load Async' option available when using 'Use Asset Bundle' option. Optionally load the asset bundle and Makinom project asset using async operations. You can get the loading progress via the 'AsyncProgress' float property on the 'Game Starter' component, e.g. for custom a loading bar UI.
- Bool/Int/Float/String Values: 'CSV' value types available. Read values from CSV files by row index/key and column index/key. Using row key refers to the value of a 1st column of a row, column key to the value of the 1st row of a column.
- Float Values: 'Game Object Count' value type available. Uses the number of game objects of a selected object as value.
- String Values: 'Scene Name' value type available. Uses the active scene's name as value.
- String Values: 'Float Value' value type available. Uses a float value formatted as a text as value. The float value is defined with a float value selection, supporting variables, formulas, etc.
- Vector 3 Values: 'Shot Velocity' value type available. Calculates the velocity from a position to a target for a given speed. Supports different arcs.
- Float Operators: 'Bitwise Complement' (~), 'Left Shift' (<<), 'Right Shift' (>>), 'Logical AND' (&), 'Logical Exclusive OR' (^) and 'Logical OR' (|) float operators available. Use these operators for bit operations. Will convert the used values to integers before the operation.
- Float Operators: Float operators have been replaced by an extensible class. Extend from the 'BaseFloatOperator' class to implement custom float operations. Previous settings will be udpated automatically.
- Vector3 Operators: Vector3 operators have been replaced by an extensible class. Extend from the 'BaseVector3Operator' class to implement custom Vector3 operations. Previous settings will be udpated automatically.
- String Operators: String operators have been replaced by an extensible class. Extend from the 'BaseStringOperator' class to implement custom string operations. Previous settings will be udpated automatically.
- Input Keys: Unity Input Manager: 'Up/Down Full Axis' setting available when using 'Is Joypad Axis'. Optionally use the axis input of 'Up' and 'Down' input handling as full input (1/-1). Can be useful if the input is e.g. used in UI, as otherwise the received input axis at 'Up' or 'Down' could be too low.
- Game Controls: Pause Key: 'Input Audio Clip' settings available. Optionally play an audio clip when using the pause key.
- Game Controls: Pause Key: 'Conditions' settings available. Optionally only allow using the pause key when defined conditions are valid (e.g. game states).
- Game Controls: Control Behaviours: 'Control Behaviours' settings available. Register custom control behaviour components with Makinom's control block system.
- Game Controls: Move To Interaction: 'Speed Type' setting available for the speed settings. Optionally try to use a move speed defined on the player's game object using a 'Move Speed' component (or something else implementing the 'IMoveSpeed' interface) instead of a fixed, defined speed. If no move speed is found on the game object, the speed defined in the settings will be used.
- Game Controls: Object Selection: Selection Input Settings: 'Input Audio Clip' settings available for the individual input keys. Optionally play an audio clip when using the keys.
- Game Settings: 'Default Horizontal Plane' setting available. Defines the global default horizontal plane that'll be used. All other horizontal plane settings now offer a 'Default' option in addition to the 'XZ' and 'XY' options, using the default horizontal plane. All other horizontal plane settings have been changed to 'Default'.
- Game States: 'Reset New Game' setting available. Optionally reset a game state to it's initial state when starting a new game. By default enabled.
- Formulas: Field Value: 'Field Value' node available. Uses the value of an int or float field/parameter either from a 'Static' class, a game object's component or a component stored in selected data.
- Formulas: Function Value: 'Function Value' node available. Uses the return value of called function either from a 'Static' class, a game object's component or a component stored in selected data. The function must use 1 parameter to pass on the 'FormulaCall' used to calculate the formula, which gives you access to user/target, current value and local variables/selected data, e.g. public void YourFunction(FormulaCall call).
- Formulas: Math Function: 'Math Function' node available. Uses a Mathf function on the current value of the formula. E.g. allows rounding, absolute value or sum up of the value.
- Formulas: Unity Console: 'Unity Console' node available. Prints a text to the Untiy console.
- Formulas: Selected Data Count: 'Selected Data Count' node available. Either checks the number of data stored in a selected data list or uses the number as a value to change the formula's value.
- Formulas: Check Height Differences: 'Check Height Differences' node available. Checks if the target is above or below the user.
- Scene Objects: 'No Tooltip' setting available. Prevents the scene object from causing tooltips.
- Scene Connections: 'Scene Connections' are available in the 'Game' section. Scan your project's scenes for connections (via 'Scene Changer' components) and use that information to find paths to navigation markers across multiple scenes. Navigation markers can be used to mark positions or game objects to be displayed by navigation HUDs.
- Animations: Mecanim: 'Use Animator Speed' setting available when using 'Fixed' animation duration. Optionally multiply the fixed duration by the animator's speed. By default disabled.
- Sound Assignments: Add multiple audio clips to a sound type assignment. One of the added clips will be played randomly. Previous settings will be updated automatically.
- Text Codes: New variable text code for 'Int with Format' available. Display int variables with a defined formatting.
- UI System: 'Show Confirmation Dialog' setting available. Optionally disable the UI system change confirmation dialogue. Use this option in case you're running into a Unity dialog related error.
- UI System: Unity UI: 'Event System' settings available. Select which event system will be created, either none, Makinom's default event system or from a defined prefab with your event system setup.
- UI Settings: Drag/Drop Settings: 'Drag/Drop Settings' available. Define how dragging and dropping UI content is handled and displayed.
- UI Settings: Exit Game Question: 'Exit Game Question' settings available. Defines how an (optional) exit game question will look when stopping the game.
- UI Settings: Flying Texts: 'Default UI' setting available. Define a default UI setup for flying texts that will be used if individual flying texts don't define a UI setup.
- UI Settings: Flying Texts: 'Check Visibility' settings available. Optionally use a camera viewport check on the flying text's world position to determine if it's visible or not. Not visible flying texts will be hidden in the UI. By default enabled.
- UI Boxes: 'Block Cursor Over Selectin' setting available. Optionally block selecting inputs on cursor over for the UI box (when 'Cursor Over Selection' is enabled in the general settings of UI boxes).
- UI Boxes: Focus Settings: 'Focus Cursor Over' setting available. Optionally focus the UI box the cursor is currently over.
- UI Boxes: Controls: 'Scroll Top Key' and 'Scroll Bottom Key' settings available. Optionally use input keys to scroll to the top or bottom of the scroll area.
- HUDs: Navigation Bar: 'Navigation Bar' HUD type available. Navigation bars can show cardinal directions and scene information (e.g. interactions and navigation markers).
- HUDs: Unity UI: 'HUD Navigation Bar' component available. Set up the navigation bar's settings and displayed navigation points (e.g. cardinal directions, separators, interactions, etc.).
- HUDs: HUD Click: 'Schematic' click type available. Starts a schematic when clicking on a HUD or part of a HUD.
- HUDs: HUD Click: 'Toggle Game Object' click type available. Toggles a game object active/inactive. Only for the 'Unity UI' module.
- Save Games: Save Data Settings: 'Scene Positions' setting available. Defines if scene positions will be saved with save games. Scene positions can be set using the 'Set Scene Position' schematic node.
- Save Games: Pause Settings: 'Pause Settings' available. Optionally pause the game or change the timescale while displaying save menus.
- Save Games: Save Game Menu: 'Exit After Save' settings available. Optionally stop the game after saving - can also optionally display the exit game question to the player.
- Save Games: Save Game Menu: Saving Info Dialogue: 'Saving Info Dialogue' settings available. Optionally display an information dialogue while saving the game. Will be closed when the save has been completed (before showing the 'Saved Info Dialogue').
- Save Games: Save Game Menu: Saved Info Dialogue: 'Auto Close' settings available. Optionally auto close the saved info dialogue after a defined amount of time.
- Save Games: Load Game Menu: Loading Info Dialogue: 'Loading Info Dialogue' settings available. Optionally display an information dialogue while loading the game. Will be closed when the load has been completed (before showing the 'Loaded Info Dialogue').
- Save Games: Load Game Menu: Loaded Info Dialogue: 'Auto Close' settings available. Optionally auto close the loaded info dialogue after a defined amount of time.
- Save Games: Save Point: Save Button: 'Close After Save' setting available. Optionally close the save menus after saving the game instead of returning to the save point menu.
- General Condition Templates: 'General Condition Templates' sub-section available in 'Templates' section. Define general conditions, e.g. checking for game states or variables. General conditions can be extended by deriving from the 'BaseGeneralCondition' class.
- Schematics: Change UI Color: 'Change UI Color' node available in 'UI' nodes. Changes or fades the color of a UI box, HUD, flying text or anything implementing the 'IColorFadeable' interface stored in selected data.
- Schematics: Change UI Offset: 'Change UI Offset' node available in 'UI' nodes. Changes or fades the animation offset of a UI box, HUD, flying text or anything implementing the 'IUIAnimation' interface stored in selected data.
- Schematics: Change UI Scale: 'Change UI Scale' node available in 'UI' nodes. Changes or fades the scale factor of a UI box, HUD, flying text or anything implementing the 'IUIAnimation' interface stored in selected data.
- Schematics: Navigation Marker: 'Navigation Marker' node available in 'Game > Scene' nodes. Adds/removes a navigation marker or clears all markers.
- Schematics: Start Machine: 'Start Without Objects' setting available. Optionally start the machine withouth any machine/starting objects. If disabled, the machine will start for each machine/starting object pair (or not at all if no game objects are available).
- Schematics: Show Dialogue: Choices: 'Enable Drag' and 'Enable Click Drag' settings available. Optionally allow dragging or click dragging a choice button, e.g. to start a 'Drop' interaction.
- Schematics: Wait For Input: 'Store Input Time' settings available. Optionally store the time it took the player to press the input (from start of the node) into a float variable.
- Schematics: Unity Console: 'Debug Type' setting available. You can now print to the regular 'Log', 'Warning' log or 'Error' log of the Untiy console.
- Schematics: Change Number Object Variables: 'Change Number Object Variables' node available. Changes an int of float variable on all currently registered object variables.
- Schematics: Raycast, Shapecast, Check Shape, Select Game Objects: Filter Game Objects: 'Variable Conditions' settings available. Optionally filter game objects by object variables on them.
- Schematics: Select Components, Remove Component, Enable Component, Is Component Enabled: 'Filter Game Objects' settings available. Optionally filter the used game objects.
- Schematics: Store Scene: 'Load Type' setting available. Set the scene load type that will be used when loading the stored scene.
- Schematics: Hide Autoclose Dialogues: 'Hide Autoclose Dialogues' node available in 'UI > Dialogue' nodes. Hides or shows (new) 'Autoclose' type dialogues.
- Schematics: Are Autoclose Dialogues Hidden: 'Are Autoclose Dialouges Hidden' node available in 'UI > Dialogue' nodes. Checks if 'Autoclose' type dialogues are hidden.
- Schematics: Block Flying Texts: 'Block Flying Texts' node available in 'UI > Dialogue' nodes. Blocks or unblocks new flying texts from being displayed.
- Schematics: Are Flying Texts Blocked: 'Are Flying Texts Blocked' node available in 'UI > Dialogue' nodes. Checks if flying texts are blocked.
- Schematics: Show Dialogue: Auto Close: 'Ignore Hidden' setting available for 'Auto Close' dialogues. Optionally ignore the hidden auto close dialogue state and show the dialogue in any case.
- Schematics: Stop Game: 'Show Exit Game Question' setting available. Optionally show the exit game question to the player before stopping the game. By default disabled.
- Schematics: Collision Camera: 'Collision Camera' node available in 'Game Object > Camera' nodes. Enable or diable the collision camera.
- Schematics: Check Height Differences: 'Check Height Differences' node available in 'Movement > Movement' nodes. Checks if a game object is above or below another game object.
- Schematics: Select Selected Data: 'Select Selected Data' node available in 'Value > Selected Data' nodes. Uses the firt, last, a random or all content from another selected data as selected data.
- Schematics: Move, Change Position, Move Into Direction: 'Speed Type' setting available for the speed settings. Optionally try to use a move speed defined on a game object using a 'Move Speed' component (or something else implementing the 'IMoveSpeed' interface) instead of a fixed, defined speed. If no move speed is found on the game object, the speed defined in the node will be used.
- Schematics: Change Position: 'Ignore Radius' setting available when using 'Move' to position/object. Ignores the radius of the mover and (optional) target object. By default enabled.
- Schematics: Set Scene Position: 'Set Scene Position' node available. Set a default position/rotation in a scene - can be used by load scene nodes and scene changers to spawn the player. You can store one scene position per scene.
- Schematics: Change Color: The 'Change Color' node can now also change the colors of 'UI Box' and 'HUD' components.
- Schematics: Load CSV File: 'Load CSV File' node available in 'Value > Text File' nodes. Loads a CSV file's data for later use as bool, float and string values (in value selections).
- Schematics: Clear CSV File: 'Clear CSV File' node available in 'Value > Text File' nodes. Removes a CSV file's loaded data or all CSV data.
- Schematics: Stop Camera Position Fade: 'Stop Camera Position Fade' node available in 'Game Object > Camera' nodes. Stops a currently fading camera position change.
- Spawn Point Component: Adding a collider (2D or 3D) will spawn at a random position within the collider's bounds. Using the 'Place On Ground' settings will find the ground for the individual random positions instead of placing the spawn point on the ground.
- Navigation Marker Component: 'Navigation Marker' component available. Adds the game object as a (temporary) navigatoin marker. The marker is added when the component is enabled and removed when the component is disabled (e.g. when changing scenes or destroying the game object).
- Game Object Manager Component: 'Game Object Manager' component available. Enables/disables game objects based on defined conditions (e.g. variables or game states).
- Component Manager Component: 'Component Manager' component available. Enables/disables components, colliders, renderers or LOD groups based on defined conditions (e.g. variables or game states).
- Variable Changer Component: 'Variable Changer' component available. Uses defined variable changes, supports auto, interaction, trigger and collision start types.
- Move Speed Component: 'Move Speed' component available. Defines walk, run and sprint movement speeds that can be used by schematic nodes (movement nodes) to use a game object's speeds instead of a fixed, defined speed.
- Scene Changers: 'Start Settings' available. You can now start scene changers like auto, interaction, trigger and collision machines. Defaults to 'Trigger Enter' start type (previous behaviour). Please note that already existing scene changers in your scenes need to manually set the start type.
- Music Players: 'Start Settings' and 'Condition Settings' available. You can now start music players like auto, interaction, trigger and collision machines. New music players default to 'Start' start type (previous behaviour). Please note that already existing music players in your scenes need to manually set the start type.
- Interaction Machines: 'Drop' start type available. Start the interaction by dropping something (from UI) on it. Allows checking the content information (e.g. name) of the dropped content.
- HUD Condition Component: 'General Condition Template' condition available. Checks a selected general condition template.
- Unity UI: UI Boxes, HUDs: 'Animation Offset' property available to be used by animations. Use this in animation clips to move the UI box or HUD, e.g. for open/close animations.
- Unity UI: UI Boxes, HUDs: Open/Close State 'General Wait Time' setting available. Optionally use the 'Max Wait Time' as a general wait time, waiting for the defined time in any case.
- Unity UI: UI Boxes: Inputs: 'Int Button' and 'Float Button' value inputs available. Change int/float value inputs using horizontal and/or vertical +/- buttons instead of sliders. Quickly create them using the context menu in the scene hierarchy.
- Unity UI: UI Boxes: Inputs: 'Unfocused Highlight' setting available. Doesn't show the 'Highlighted' state for color tint inputs on onfocused UI boxes. Requires an 'UI Input Color Changer' component on the input's game object.
- Unity UI: Inputs: Input components (e.g. 'UI Button Input' or 'UI Int Slider Input') can optionally override the input schematics of the UI box (select/accept schematics).
- Machine Components: Conditions: 'General Condition Template' condition available. Checks a selected general condition template.
- Radius Component: 'Use Scale' settings available. Optionally use the game object's scale to impact the radius. By default enabled, using X-axis scale.
- Node Editor: You can now click drag the grid with the left mouse button while holding the 'ALT' key (Windows) or 'Option' key (Mac).
- Data Assets: 'Sort At End' setting available (in the inspector when having data asset selected). Enable this to sort the asset at the end of their data list the next time the editor is opened (will be disabled when saving in the editor). E.g. use this before exporting data to make sure it's added in another project at the end of the list instead of interefring with the project's sorting.
- Unity UI Setup: Context Menu: 'UI Box Fit Size' entries available. Set up UI boxes with content size fitters for vertical size adjustment.
- Makinom Handler Component: Inspector: Manage audio volumes in the inspector of the 'Makinom Handler' component (found on the '_Makinom' game object that's created when starting Makinom in a game).

Changes:
- UI Boxes: Some input/tab settings have been moved from the 'UI Box' component to the UI box setup in 'UI > UI Boxes' in the Makinom editor. 'Auto Select First Input', 'Loop', 'Select First', 'Unfocused Selection' and 'Unfocused Highlight' are now part of the 'Controls' settings in 'UI > UI Boxes' (both default in the general settings and the individual UI box overrides). You have to redo your setup if you use any of these.
- Variables: Object ID: The 'Object ID' definition when accessing variables (e.g. 'Change Variables' node or variable conditions) is now a string value selection field. E.g. allows using the current scene name as object ID.
- Variable Conditions: 'Exists' setting is now by default disabled in newly created conditions. Your old settings are not changed by this.
- Vector 3 Values: 'Gravity 2D' and 'Gravity 3D' have been moved from 'Value' to 'Physics' sub-menu.
- Unity UI Setup: Context Menu: Creating UI boxes, inputs and HUDs using the scene hierarchy context menu will now add 'UI Color Changer' and 'UI Input Color Changer' (for inputs) components.
- Editor Settings: Text Area: 'Preview Height' has been changed to 'Max Preview Height'. The setting now defines the maximum height the text preview (i.e. showing text of a currently not editing text area) will have instead of a fixed height.
- Editor: Highly reduced the time it takes to open the Makinom editor. This comes at the cost of only searching for data assets in Makinom's data path ('Assets/Gaming Is Love/_Data/'), i.e. any individual Makinom data assets outside the data path will not be displayed in the editor. This limitation can only be changed in the source code.
- Editor: Highly reduced the time it takes to save the Makinom project in the editor. Saving will now only save the Makinom assets that where actually changed. The more different settings/assets you change, the longer saving will take. This has no effect on the time it takes to create an (optional) backup.
- Editor: Language Content Definition: The 'Language Content' foldout when defining texts and icons for other languages is now by default closed.
- Editor: Custom Content: The 'Custom Content' you can define for the content information of data (e.g. 'Scene Objects') is now by default closed (foldout).
- Editor: Foldouts: Improved foldout behaviour when opening/closing foldouts with child foldouts. The foldouts should now keep their opened/closed state intact without shifting to other foldouts.
- Editor: Saving: The 'Create Backup' toggle's state will now be remembered between saves.
- Scene Wizard: 'Create Game Object', 'Add Component' and 'Add Machine' now use searchable popup selections to select which game object/component/machine to use. You can extend the available options by deriving from 'SceneWizard_BaseCreateObject', 'SceneWizard_BaseAddComponent' and 'SceneWizard_BaseAddMachine', implementing a static, parameterless function named 'Use' for what happens when it's selected.
- Animations: Mecanim: The 'Animation Clip' duration type will now multiply the clip's duration with the animator's speed to get the actual animation duration.
- Game Settings: Raycast Type: The 'Raycast Type' setting is now used as the global default raycast type. All other raycast type settings now offer a 'Default' option (replacing the separate 'Own Raycast Type' setting), using the default raycast type. All other raycast type settings have been changed to 'Default'.
- HUDs: Tooltip: 'Tooltip Check' settings replace the previous 'Inputs' and 'Scene Object' tooltip settings. Tooltip checks are an extensible system for checking which tooltips to display. When no checks are added, all tooltips can be displayed by the HUD. Please note that previous settings will not be udpated to the new system.
- Schematics: Rigidbody Constraints: You can now add multiple constraints to e.g. freeze X and Y rotation.
- Schematics: Show Dialogue: Choices: 'Variable Conditions' have been replaced by general 'Conditions' in individual 'Choice' settings. Currently supports variable and game state conditions. Previous settings will be updated automatically.
- Schematics: Show Dialogue: Notification: The 'Notification Queue' setting is now a popup selection, allowing to add, replace or queue notifications (instead of only replace and queue as with the previous toggle setting). Previous settings will be updated automatically.
- Schematics: Auto Save Slot Dialogue, Language Dialogue, Value Option Dialogue: Speaker, portrait and audio options have been removed and remodeled to regular title/message content display to make these dialogues useable outside schematics (e.g. for future extensions).
- Schematics: Shake Camera, Shake Object: Intensity values are no longer limited to be between 0 and 1 (but it's still recommended for most use cases). E.g. using orthographic cameras with a large size require higher intensity values for the shaking to be noticable.

Fixes:
- Editor: Popups: Fixed an issue where closing popups by clicking on the popup field again blocked scrolling.
- Editor: Popups: Fixed an issue where popups didn't open to the top side when being too close to the bottom. Issue was related to using different screen size scaling (Unity Editor Preferences).
- Editor: Fixed an issue where copying listed data would use blank content information with only the copy name.
- Variable/Reflection Fields: Fixed issues with variable and reflection fields that automatically selected and overwrote the initially typed letters when opening the selection popup.
- Game Settings: Asset Settings: Asset Source Changes: Fixed an issue where the project-wide asset source changes didn't work correctly.
- Unity UI: Portraits: Fixed an issue where using 'Disable If Empty' didn't disable the game object when showing no portrait.
- Unity UI: Fixed placement issues when using 'Screen Space Camera' canvas.
- Save Games: Fixed an issue where float values in save games could be saved with wrong values.
- Save Games: Saving object variables now allows spaces in variable keys, this previously caused errors. Please note that previous save games will not load their object variables.
- Save Games: Fixed an issue where save file descriptions where not displayed. Will be displayed for new save games created with this version (or newer), as the description with the save game content is part of the save file.
- Input Keys: Unity Input Manager: Fixed an issue where using 'Is Joypad Axis' could lead to continuous input.
- Backups: Backups exceeding the defined number of backups where not deleted.
- Schematics: Load Scene: Using 'Stored' origin didn't load the stored scene.
- Schematics: Actors: Find Object: Fixed an issue where the search wasn't processed if no machine object was available.
- Schematic Nodes: Change Position: Fixed an issue where the radius from 'Radius' components wasn't used in stop distances.
- UI Boxes: Fixed an issue where using typewriter with a text speed of 0 didn't display the full text and stopped at 1 letter.
- UI Boxes: Controls: Fixed an issue where the (optional) scroll axis input wasn't used when inputs where added to the UI box (e.g. dialogue choices).
- Music: Fixed an issue where looping didn't work correctly when playing a stored music.


-------------------------------------------------------------------------------------------------------------------------------------------------------
Makinom 2.0.0
-------------------------------------------------------------------------------------------------------------------------------------------------------

New:
- Everything. Initial release.

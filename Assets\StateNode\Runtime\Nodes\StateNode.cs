using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace StateScript
{
    /// <summary>
    /// Runtime properties of a state node
    /// </summary>
    public class StateNodeProperty : ISerializable
    {
        public NodeId Id { get; set; }
        public List<StatePort> Ports { get; set; } = new List<StatePort>();
        
        public StateNodeProperty()
        {
            Id = GenerateUniqueId();
        }
        
        public void Serialize(ISerializer serializer)
        {
            serializer.WriteInt("id", Id.Value);
            serializer.WriteInt("portCount", Ports.Count);
            
            for (int i = 0; i < Ports.Count; i++)
            {
                serializer.WriteObject($"port_{i}", Ports[i]);
            }
        }
        
        public void Deserialize(ISerializer serializer)
        {
            Id = serializer.ReadInt("id");
            var portCount = serializer.ReadInt("portCount");
            
            Ports.Clear();
            for (int i = 0; i < portCount; i++)
            {
                var port = serializer.ReadObject<StatePort>($"port_{i}");
                Ports.Add(port);
            }
        }
        
        public StatePort GetInputPort(string name = "Input")
        {
            return Ports.FirstOrDefault(p => p.Type == PortType.Input && p.Name.ToString() == name);
        }
        
        public StatePort GetOutputPort(string name = "Output")
        {
            return Ports.FirstOrDefault(p => p.Type == PortType.Output && p.Name.ToString() == name);
        }
        
        public List<StatePort> GetInputPorts()
        {
            return Ports.Where(p => p.Type == PortType.Input).ToList();
        }
        
        public List<StatePort> GetOutputPorts()
        {
            return Ports.Where(p => p.Type == PortType.Output).ToList();
        }
        
        private static NodeId GenerateUniqueId()
        {
            return UnityEngine.Random.Range(1, int.MaxValue);
        }
    }
    
    /// <summary>
    /// Editor-only properties of a state node
    /// </summary>
    public class StateNodeEditorProperty : ISerializable
    {
        public Vector2 Position { get; set; }
        public Fixed32String Name { get; set; }
        public Fixed32String Description { get; set; }
        public Color NodeColor { get; set; } = Color.white;
        
        public void Serialize(ISerializer serializer)
        {
            serializer.WriteVector2("position", Position);
            serializer.WriteString("name", Name.ToString());
            serializer.WriteString("description", Description.ToString());
            serializer.WriteColor("nodeColor", NodeColor);
        }
        
        public void Deserialize(ISerializer serializer)
        {
            Position = serializer.ReadVector2("position");
            Name = new Fixed32String(serializer.ReadString("name"));
            Description = new Fixed32String(serializer.ReadString("description"));
            NodeColor = serializer.ReadColor("nodeColor", Color.white);
        }
    }
    
    /// <summary>
    /// Base class for all state nodes
    /// </summary>
    public abstract class StateNode : ISerializable
    {
        public StateNodeProperty Property { get; set; }
        public StateNodeEditorProperty EditorProperty { get; set; }
        public StateNodeStatus Status { get; protected set; }
        
        protected StateNode()
        {
            Property = new StateNodeProperty();
            EditorProperty = new StateNodeEditorProperty();
            Status = StateNodeStatus.None;
            InitializePorts();
        }
        
        /// <summary>
        /// Called when the node becomes active
        /// </summary>
        public abstract void OnEnter(StateFlow stateFlow, IStateContext context);
        
        /// <summary>
        /// Called every frame while the node is active
        /// </summary>
        public abstract void OnUpdate(StateFlow stateFlow, IStateContext context, float deltaTime);
        
        /// <summary>
        /// Called when the node becomes inactive
        /// </summary>
        public abstract void OnExit(StateFlow stateFlow, IStateContext context);
        
        /// <summary>
        /// Initialize the ports for this node type
        /// </summary>
        protected abstract void InitializePorts();
        
        /// <summary>
        /// Reset the node to its initial state
        /// </summary>
        public virtual void Reset()
        {
            Status = StateNodeStatus.None;
        }
        
        public virtual void Serialize(ISerializer serializer)
        {
            serializer.WriteString("nodeType", GetType().Name);
            serializer.WriteObject("property", Property);
            serializer.WriteObject("editorProperty", EditorProperty);
            serializer.WriteInt("status", (int)Status);
        }
        
        public virtual void Deserialize(ISerializer serializer)
        {
            // NodeType is handled by factory, not here
            Property = serializer.ReadObject<StateNodeProperty>("property");
            EditorProperty = serializer.ReadObject<StateNodeEditorProperty>("editorProperty");
            Status = (StateNodeStatus)serializer.ReadInt("status");
        }
        
        protected PortId GenerateUniquePortId()
        {
            return UnityEngine.Random.Range(1, int.MaxValue);
        }
        
        public override string ToString()
        {
            return $"{GetType().Name} ({Property.Id})";
        }
    }
}

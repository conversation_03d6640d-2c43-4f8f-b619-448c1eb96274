using System;

namespace StateScript
{
    /// <summary>
    /// Unique identifier for nodes
    /// </summary>
    [System.Serializable]
    public struct NodeId : IEquatable<NodeId>
    {
        public int Value;
        
        public static implicit operator int(NodeId id) => id.Value;
        public static implicit operator NodeId(int value) => new NodeId { Value = value };
        
        public bool Equals(NodeId other) => Value == other.Value;
        public override bool Equals(object obj) => obj is NodeId other && Equals(other);
        public override int GetHashCode() => Value;
        
        public static bool operator ==(NodeId left, NodeId right) => left.Equals(right);
        public static bool operator !=(NodeId left, NodeId right) => !left.Equals(right);
        
        public override string ToString() => Value.ToString();
    }
    
    /// <summary>
    /// Unique identifier for ports
    /// </summary>
    [System.Serializable]
    public struct PortId : IEquatable<PortId>
    {
        public int Value;
        
        public static implicit operator int(PortId id) => id.Value;
        public static implicit operator PortId(int value) => new PortId { Value = value };
        
        public bool Equals(PortId other) => Value == other.Value;
        public override bool Equals(object obj) => obj is PortId other && Equals(other);
        public override int GetHashCode() => Value;
        
        public static bool operator ==(PortId left, PortId right) => left.Equals(right);
        public static bool operator !=(PortId left, PortId right) => !left.Equals(right);
        
        public override string ToString() => Value.ToString();
    }
    
    /// <summary>
    /// Event type for state flow events
    /// </summary>
    [System.Serializable]
    public struct EventType : IEquatable<EventType>
    {
        public Fixed32String Name;
        
        public EventType(string name) => Name = new Fixed32String(name);
        
        // Predefined events
        public static EventType OnStart = new EventType("OnStart");
        public static EventType OnComplete = new EventType("OnComplete");
        public static EventType OnFailed = new EventType("OnFailed");
        public static EventType OnPaused = new EventType("OnPaused");
        public static EventType OnResumed = new EventType("OnResumed");
        
        public bool Equals(EventType other) => Name.Equals(other.Name);
        public override bool Equals(object obj) => obj is EventType other && Equals(other);
        public override int GetHashCode() => Name.GetHashCode();
        
        public static bool operator ==(EventType left, EventType right) => left.Equals(right);
        public static bool operator !=(EventType left, EventType right) => !left.Equals(right);
        
        public override string ToString() => Name.ToString();
    }
    
    /// <summary>
    /// Port types for node connections
    /// </summary>
    public enum PortType
    {
        Input,
        Output
    }
    
    /// <summary>
    /// Status of individual nodes
    /// </summary>
    public enum StateNodeStatus
    {
        None,
        Running,
        Completed,
        Failed
    }
    
    /// <summary>
    /// Status of the entire state flow
    /// </summary>
    public enum StateFlowStatus
    {
        None,
        Running,
        Paused,
        Completed,
        Failed
    }
    
    /// <summary>
    /// Status of individual state actions
    /// </summary>
    public enum StateActionStatus
    {
        None,
        Running,
        Completed,
        Failed
    }
}

namespace StateScript
{
    /// <summary>
    /// Node that listens for specific events and activates when triggered
    /// Used for event-driven state transitions
    /// </summary>
    public class StateListenerNode : StateNode
    {
        public EventType EventType { get; set; }
        
        public StateListenerNode()
        {
            EventType = EventType.OnStart; // Default event
        }
        
        public StateListenerNode(EventType eventType)
        {
            EventType = eventType;
        }
        
        public StateListenerNode(string eventName)
        {
            EventType = new EventType(eventName);
        }
        
        public override void OnEnter(StateFlow stateFlow, IStateContext context)
        {
            Status = StateNodeStatus.Completed; // Immediately complete to trigger next node
        }
        
        public override void OnUpdate(StateFlow stateFlow, IStateContext context, float deltaTime)
        {
            // Event nodes don't need continuous updates
            // They are activated by the StateFlow's event system
        }
        
        public override void OnExit(StateFlow stateFlow, IStateContext context)
        {
            // Nothing to clean up
        }
        
        protected override void InitializePorts()
        {
            // Listener nodes only have output ports (they are entry points)
            Property.Ports.Add(new StatePort("Output", PortType.Output, Property.Id));
        }
        
        /// <summary>
        /// Check if this listener should respond to the given event
        /// </summary>
        public bool ShouldTrigger(EventType eventType)
        {
            return EventType.Equals(eventType);
        }
        
        public override void Serialize(ISerializer serializer)
        {
            base.Serialize(serializer);
            serializer.WriteString("eventName", EventType.Name.ToString());
        }
        
        public override void Deserialize(ISerializer serializer)
        {
            base.Deserialize(serializer);
            var eventName = serializer.ReadString("eventName");
            EventType = new EventType(eventName);
        }
        
        public override string ToString()
        {
            return $"StateListenerNode ({EventType})";
        }
    }
}

using UnityEngine;
using StateScript;

namespace StateScript.Examples
{
    /// <summary>
    /// Example component that runs a StateFlow
    /// Demonstrates how to use the StateScript system in a game
    /// </summary>
    public class StateFlowRunner : MonoBehaviour, IStateContext
    {
        [Header("StateFlow Configuration")]
        [SerializeField] private StateFlowAsset _stateFlowAsset;
        [SerializeField] private bool _autoStart = true;
        [SerializeField] private bool _debugMode = false;
        
        [Head<PERSON>("Runtime Info")]
        [SerializeField] private StateFlowStatus _currentStatus;
        [SerializeField] private string _currentNodeName;
        
        private StateFlow _runtimeStateFlow;
        
        private void Start()
        {
            if (_stateFlowAsset != null)
            {
                // Create a runtime instance of the StateFlow
                _runtimeStateFlow = _stateFlowAsset.CreateRuntimeInstance();

                // Set this component as the context
                _runtimeStateFlow.SetContext(this);

                if (_autoStart)
                {
                    StartStateFlow();
                }
            }
            else
            {
                Debug.LogWarning("No StateFlowAsset assigned to StateFlowRunner", this);
            }
        }

        private void Update()
        {
            if (_runtimeStateFlow != null && _runtimeStateFlow.Status == StateFlowStatus.Running)
            {
                _runtimeStateFlow.Update(Time.deltaTime);

                // Update debug info
                _currentStatus = _runtimeStateFlow.Status;
                _currentNodeName = _runtimeStateFlow.CurrentNode?.GetType().Name ?? "None";

                if (_debugMode)
                {
                    DebugStateFlow();
                }
            }
        }
        
        /// <summary>
        /// Start the state flow execution
        /// </summary>
        public void StartStateFlow()
        {
            if (_runtimeStateFlow != null)
            {
                _runtimeStateFlow.Start();
                Debug.Log($"Started StateFlow: {_runtimeStateFlow.Name}", this);
            }
        }
        
        /// <summary>
        /// Stop the state flow execution
        /// </summary>
        public void StopStateFlow()
        {
            if (_runtimeStateFlow != null)
            {
                _runtimeStateFlow.Stop();
                Debug.Log($"Stopped StateFlow: {_runtimeStateFlow.Name}", this);
            }
        }
        
        /// <summary>
        /// Pause the state flow execution
        /// </summary>
        public void PauseStateFlow()
        {
            if (_runtimeStateFlow != null)
            {
                _runtimeStateFlow.Pause();
                Debug.Log($"Paused StateFlow: {_runtimeStateFlow.Name}", this);
            }
        }
        
        /// <summary>
        /// Resume the state flow execution
        /// </summary>
        public void ResumeStateFlow()
        {
            if (_runtimeStateFlow != null)
            {
                _runtimeStateFlow.Resume();
                Debug.Log($"Resumed StateFlow: {_runtimeStateFlow.Name}", this);
            }
        }
        
        /// <summary>
        /// Trigger a custom event in the state flow
        /// </summary>
        public void TriggerEvent(string eventName)
        {
            if (_runtimeStateFlow != null)
            {
                var eventType = new EventType(eventName);
                _runtimeStateFlow.OnEvent(eventType);
                Debug.Log($"Triggered event: {eventName}", this);
            }
        }
        
        /// <summary>
        /// Get a variable value from the state flow
        /// </summary>
        public T GetVariable<T>(string variableName) where T : StateVariable
        {
            return _runtimeStateFlow?.GetVariable<T>(variableName);
        }
        
        /// <summary>
        /// Get an integer variable value
        /// </summary>
        public int GetInt(string variableName)
        {
            return _runtimeStateFlow?.GetInt(variableName) ?? 0;
        }
        
        /// <summary>
        /// Set an integer variable value
        /// </summary>
        public void SetInt(string variableName, int value)
        {
            _runtimeStateFlow?.SetInt(variableName, value);
        }
        
        /// <summary>
        /// Get a float variable value
        /// </summary>
        public float GetFloat(string variableName)
        {
            return _runtimeStateFlow?.GetFloat(variableName) ?? 0f;
        }
        
        /// <summary>
        /// Set a float variable value
        /// </summary>
        public void SetFloat(string variableName, float value)
        {
            _runtimeStateFlow?.SetFloat(variableName, value);
        }
        
        /// <summary>
        /// Get a boolean variable value
        /// </summary>
        public bool GetBool(string variableName)
        {
            return _runtimeStateFlow?.GetBool(variableName) ?? false;
        }
        
        /// <summary>
        /// Set a boolean variable value
        /// </summary>
        public void SetBool(string variableName, bool value)
        {
            _runtimeStateFlow?.SetBool(variableName, value);
        }
        
        /// <summary>
        /// Get a string variable value
        /// </summary>
        public string GetString(string variableName)
        {
            return _runtimeStateFlow?.GetString(variableName) ?? "";
        }
        
        /// <summary>
        /// Set a string variable value
        /// </summary>
        public void SetString(string variableName, string value)
        {
            _runtimeStateFlow?.SetString(variableName, value);
        }
        
        private void DebugStateFlow()
        {
            if (_runtimeStateFlow == null) return;
            
            // Draw debug info in scene view
            var position = transform.position + Vector3.up * 2f;
            
            // Status
            var statusColor = _runtimeStateFlow.Status switch
            {
                StateFlowStatus.Running => Color.green,
                StateFlowStatus.Paused => Color.yellow,
                StateFlowStatus.Completed => Color.blue,
                StateFlowStatus.Failed => Color.red,
                _ => Color.gray
            };
            
            Debug.DrawRay(position, Vector3.up * 0.5f, statusColor);
            
            // Current node info
            if (_runtimeStateFlow.CurrentNode != null)
            {
                var nodePosition = position + Vector3.right * 1f;
                Debug.DrawRay(nodePosition, Vector3.up * 0.3f, Color.cyan);
            }
        }
        
        private void OnDrawGizmosSelected()
        {
            if (_runtimeStateFlow != null && _debugMode)
            {
                // Draw state flow info
                Gizmos.color = Color.white;
                var position = transform.position + Vector3.up * 2.5f;
                
                // You could draw more detailed debug info here
                // For now, just show that the component is active
                Gizmos.DrawWireSphere(position, 0.2f);
            }
        }
        
        private void OnValidate()
        {
            // Update runtime info in inspector
            if (_runtimeStateFlow != null)
            {
                _currentStatus = _runtimeStateFlow.Status;
                _currentNodeName = _runtimeStateFlow.CurrentNode?.GetType().Name ?? "None";
            }
        }
    }
}

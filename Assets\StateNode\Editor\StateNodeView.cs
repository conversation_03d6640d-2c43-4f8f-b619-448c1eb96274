using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEditor;
using UnityEngine.UIElements;
using UnityEditor.UIElements;
using UnityEditor.Experimental.GraphView;
using StateScript;

namespace StateScript.Editor
{
    /// <summary>
    /// Visual representation of a StateNode in the GraphView
    /// </summary>
    public class StateNodeView : Node
    {
        public event Action<StateNode> OnNodeSelected;
        
        public StateNode StateNode { get; private set; }
        
        private Dictionary<PortId, StatePortView> _portViews = new Dictionary<PortId, StatePortView>();
        
        public StateNodeView(StateNode stateNode)
        {
            StateNode = stateNode;

            title = stateNode.EditorProperty.Name.ToString();

            // Add CSS classes for styling
            AddToClassList("node");
            AddNodeTypeClass();

            // Set node styling
            SetNodeStyling();

            // Create ports
            CreatePorts();

            // Handle selection
            RegisterCallback<MouseDownEvent>(OnMouseDown);

            // Make node resizable and movable
            capabilities |= Capabilities.Resizable | Capabilities.Movable | Capabilities.Selectable;
        }
        
        private void SetNodeStyling()
        {
            var color = StateNode.EditorProperty.NodeColor;

            // Set default colors based on node type if not set
            if (color == Color.white)
            {
                color = StateNode switch
                {
                    StateActionNode => new Color(0.2f, 0.6f, 0.2f, 1f), // Green
                    StateConditionNode => new Color(0.6f, 0.6f, 0.2f, 1f), // Yellow
                    StateListenerNode => new Color(0.2f, 0.2f, 0.6f, 1f), // Blue
                    _ => new Color(0.4f, 0.4f, 0.4f, 1f) // Gray
                };
                StateNode.EditorProperty.NodeColor = color;
            }

            // Apply proper styling with rounded corners
            var titleContainer = this.Q("title");
            if (titleContainer != null)
            {
                titleContainer.style.backgroundColor = color;
                titleContainer.style.borderTopLeftRadius = 6;
                titleContainer.style.borderTopRightRadius = 6;
                titleContainer.style.color = Color.white;
                titleContainer.style.unityFontStyleAndWeight = FontStyle.Bold;
                titleContainer.style.fontSize = 12;
                titleContainer.style.paddingTop = 4;
                titleContainer.style.paddingBottom = 4;
                titleContainer.style.paddingLeft = 8;
                titleContainer.style.paddingRight = 8;
            }

            // Style the main container
            var mainContainer = this.Q("contents");
            if (mainContainer != null)
            {
                mainContainer.style.backgroundColor = new Color(0.2f, 0.2f, 0.2f, 0.9f);
                mainContainer.style.borderBottomLeftRadius = 6;
                mainContainer.style.borderBottomRightRadius = 6;
                mainContainer.style.borderTopWidth = 0;
                mainContainer.style.borderBottomWidth = 1;
                mainContainer.style.borderLeftWidth = 1;
                mainContainer.style.borderRightWidth = 1;
                mainContainer.style.borderBottomColor = color;
                mainContainer.style.borderLeftColor = color;
                mainContainer.style.borderRightColor = color;
            }

            // Ensure the node title is visible and properly styled
            var titleLabel = this.Q<Label>("title-label");
            if (titleLabel != null)
            {
                titleLabel.text = StateNode.EditorProperty.Name.ToString();
                titleLabel.style.color = Color.white;
                titleLabel.style.unityFontStyleAndWeight = FontStyle.Bold;
            }
        }

        private void AddNodeTypeClass()
        {
            // Add CSS class based on node type for styling
            switch (StateNode)
            {
                case StateActionNode:
                    AddToClassList("action-node");
                    break;
                case StateConditionNode:
                    AddToClassList("condition-node");
                    break;
                case StateListenerNode:
                    AddToClassList("listener-node");
                    break;
                default:
                    AddToClassList("custom-node");
                    break;
            }
        }

        private void CreatePorts()
        {
            foreach (var statePort in StateNode.Property.Ports)
            {
                var portView = CreatePortView(statePort);
                _portViews[statePort.Id] = portView;
                
                if (statePort.Type == PortType.Input)
                {
                    inputContainer.Add(portView);
                }
                else
                {
                    outputContainer.Add(portView);
                }
            }
        }
        
        private StatePortView CreatePortView(StatePort statePort)
        {
            var direction = statePort.Type == PortType.Input ? Direction.Input : Direction.Output;
            var capacity = Port.Capacity.Multi; // Allow multiple connections
            
            var portView = new StatePortView(statePort, direction, capacity);
            portView.portName = statePort.Name.ToString();
            
            return portView;
        }
        
        public StatePortView GetPortView(PortId portId)
        {
            return _portViews.TryGetValue(portId, out var portView) ? portView : null;
        }
        
        private void OnMouseDown(MouseDownEvent evt)
        {
            if (evt.clickCount == 1)
            {
                OnNodeSelected?.Invoke(StateNode);
            }
            else if (evt.clickCount == 2)
            {
                // Double-click to edit node properties
                OpenNodeEditor();
            }
        }
        
        private void OpenNodeEditor()
        {
            // Create a popup window for editing node properties
            var popup = new NodeEditorPopup(StateNode);
            var rect = new Rect(worldBound.position, new Vector2(300, 400));
            UnityEditor.PopupWindow.Show(rect, popup);
        }
        
        public override void SetPosition(Rect newPos)
        {
            base.SetPosition(newPos);
            StateNode.EditorProperty.Position = newPos.position;
        }
        
        public void UpdateTitle()
        {
            title = StateNode.EditorProperty.Name.ToString();

            // Also update the title label if it exists
            var titleLabel = this.Q<Label>("title-label");
            if (titleLabel != null)
            {
                titleLabel.text = StateNode.EditorProperty.Name.ToString();
            }
        }
        
        public void RefreshPorts()
        {
            // Clear existing ports
            inputContainer.Clear();
            outputContainer.Clear();
            _portViews.Clear();
            
            // Recreate ports
            CreatePorts();
        }
    }
    
    /// <summary>
    /// Custom port view for StateScript ports
    /// </summary>
    public class StatePortView : Port
    {
        public StatePort StatePort { get; private set; }

        // Use a consistent type for all ports to ensure connections work
        private static Type connectionType = typeof(StateConnection);

        public StatePortView(StatePort statePort, Direction direction, Capacity capacity)
            : base(Orientation.Horizontal, direction, capacity, connectionType)
        {
            StatePort = statePort;
            portName = statePort.Name.ToString();

            // Set port color based on type
            var color = direction == Direction.Input ? Color.cyan : Color.magenta;
            portColor = color;

            // Style the port
            this.style.minWidth = 30;
            this.style.minHeight = 24;

            // Make sure the port is visible and clickable
            EnableInClassList("port", true);

            // Add a highlight effect on hover
            this.RegisterCallback<MouseEnterEvent>(evt => {
                this.AddToClassList("port-highlight");
            });

            this.RegisterCallback<MouseLeaveEvent>(evt => {
                this.RemoveFromClassList("port-highlight");
            });
        }

        public override bool ContainsPoint(Vector2 localPoint)
        {
            // Expand the clickable area slightly
            var bounds = this.layout;
            var expandedRect = new Rect(
                bounds.x - 10,
                bounds.y - 10,
                bounds.width + 20,
                bounds.height + 20
            );
            return expandedRect.Contains(localPoint);
        }
    }
    
    /// <summary>
    /// Popup window for editing node properties
    /// </summary>
    public class NodeEditorPopup : PopupWindowContent
    {
        private StateNode _node;
        private Vector2 _scrollPosition;
        
        public NodeEditorPopup(StateNode node)
        {
            _node = node;
        }
        
        public override Vector2 GetWindowSize()
        {
            return new Vector2(300, 400);
        }
        
        public override void OnGUI(Rect rect)
        {
            GUILayout.Label("Node Properties", EditorStyles.boldLabel);
            
            _scrollPosition = EditorGUILayout.BeginScrollView(_scrollPosition);
            
            // Basic properties
            EditorGUI.BeginChangeCheck();
            
            var newName = EditorGUILayout.TextField("Name", _node.EditorProperty.Name.ToString());
            if (EditorGUI.EndChangeCheck())
            {
                _node.EditorProperty.Name = new Fixed32String(newName);
            }
            
            EditorGUI.BeginChangeCheck();
            var newDescription = EditorGUILayout.TextField("Description", _node.EditorProperty.Description.ToString());
            if (EditorGUI.EndChangeCheck())
            {
                _node.EditorProperty.Description = new Fixed32String(newDescription);
            }
            
            EditorGUI.BeginChangeCheck();
            var newColor = EditorGUILayout.ColorField("Color", _node.EditorProperty.NodeColor);
            if (EditorGUI.EndChangeCheck())
            {
                _node.EditorProperty.NodeColor = newColor;
            }
            
            EditorGUILayout.Space();
            
            // Node-specific properties
            DrawNodeSpecificProperties();
            
            EditorGUILayout.EndScrollView();
            
            EditorGUILayout.Space();
            
            if (GUILayout.Button("Close"))
            {
                editorWindow.Close();
            }
        }
        
        private void DrawNodeSpecificProperties()
        {
            switch (_node)
            {
                case StateActionNode actionNode:
                    DrawActionNodeProperties(actionNode);
                    break;
                case StateConditionNode conditionNode:
                    DrawConditionNodeProperties(conditionNode);
                    break;
                case StateListenerNode listenerNode:
                    DrawListenerNodeProperties(listenerNode);
                    break;
            }
        }
        
        private void DrawActionNodeProperties(StateActionNode actionNode)
        {
            GUILayout.Label("Actions", EditorStyles.boldLabel);
            
            for (int i = 0; i < actionNode.Actions.Count; i++)
            {
                var action = actionNode.Actions[i];
                EditorGUILayout.LabelField($"Action {i}: {action.GetType().Name}");
                
                EditorGUI.indentLevel++;
                action.Duration = EditorGUILayout.FloatField("Duration", action.Duration);
                action.Delay = EditorGUILayout.FloatField("Delay", action.Delay);
                EditorGUI.indentLevel--;
                
                if (GUILayout.Button("Remove"))
                {
                    actionNode.Actions.RemoveAt(i);
                    break;
                }
                
                EditorGUILayout.Space();
            }
            
            if (GUILayout.Button("Add Wait Action"))
            {
                actionNode.Actions.Add(new WaitAction(1.0f));
            }
            
            if (GUILayout.Button("Add Log Action"))
            {
                actionNode.Actions.Add(new LogAction("Hello World"));
            }
        }
        
        private void DrawConditionNodeProperties(StateConditionNode conditionNode)
        {
            GUILayout.Label("Condition", EditorStyles.boldLabel);
            
            if (conditionNode.Condition != null)
            {
                EditorGUILayout.LabelField($"Type: {conditionNode.Condition.GetType().Name}");
            }
            else
            {
                EditorGUILayout.LabelField("No condition set");
            }
            
            if (GUILayout.Button("Set Always True"))
            {
                conditionNode.SetSimpleCondition(new AlwaysTrueCondition());
            }
            
            if (GUILayout.Button("Set Always False"))
            {
                conditionNode.SetSimpleCondition(new AlwaysFalseCondition());
            }
        }
        
        private void DrawListenerNodeProperties(StateListenerNode listenerNode)
        {
            GUILayout.Label("Event Listener", EditorStyles.boldLabel);
            
            var eventName = EditorGUILayout.TextField("Event Name", listenerNode.EventType.Name.ToString());
            if (eventName != listenerNode.EventType.Name.ToString())
            {
                listenerNode.EventType = new EventType(eventName);
            }
        }
    }
}

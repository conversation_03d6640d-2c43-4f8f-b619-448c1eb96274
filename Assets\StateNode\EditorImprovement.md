
1. improve the visuals for the toolbar(or menu bar), right now the new load save button's are layouted in the same row, can we make a menu with dropdown for those buttons? so that we might add other menus in the future.
2. add a file name display in the toolbar, so that we know which file we are editing. when the file is changed, the file name should be bold and add a * to the title.
3. right now the variables and the inspector are both in the left side, when having too many variables, the inspector will not be easy to use. so let's put the inspector to the right, and when there is no selected node, let's hide the inspector. as for the variables, it should be scrollable, and expandable.
4. right now the graphview has not grids in the background, let's add a toggle in the top right of the menu bar to show/hide the grid. by default it should be shown.
5. add some shotcuts for common operations, like save, re-center the view, etc.

using UnityEngine;

namespace StateScript
{
    /// <summary>
    /// Interface for serialization operations
    /// </summary>
    public interface ISerializer
    {
        // Write operations
        void WriteInt(string key, int value);
        void WriteFloat(string key, float value);
        void WriteString(string key, string value);
        void WriteBool(string key, bool value);
        void WriteVector2(string key, Vector2 value);
        void WriteVector3(string key, Vector3 value);
        void WriteColor(string key, Color value);
        void WriteObject(string key, ISerializable obj);
        void WriteArray<T>(string key, T[] array) where T : ISerializable;
        
        // Read operations
        int ReadInt(string key, int defaultValue = 0);
        float ReadFloat(string key, float defaultValue = 0f);
        string ReadString(string key, string defaultValue = "");
        bool ReadBool(string key, bool defaultValue = false);
        Vector2 ReadVector2(string key, Vector2 defaultValue = default);
        Vector3 ReadVector3(string key, Vector3 defaultValue = default);
        Color ReadColor(string key, Color defaultValue = default);
        T ReadObject<T>(string key) where T : ISerializable, new();
        T[] ReadArray<T>(string key) where T : ISerializable, new();
        
        // Special operations for polymorphic deserialization
        ISerializer GetSubSerializer(string key);
        bool HasKey(string key);
    }
}

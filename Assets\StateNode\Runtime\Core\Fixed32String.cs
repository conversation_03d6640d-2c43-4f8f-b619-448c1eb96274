using System;
using Unity.Collections;
using Unity.Collections.LowLevel.Unsafe;

namespace StateScript
{
    /// <summary>
    /// Stack-allocated fixed-size string for performance-critical scenarios
    /// </summary>
    [System.Serializable]
    public unsafe struct Fixed32String : IEquatable<Fixed32String>
    {
        private fixed byte bytes[32];
        
        public Fixed32String(string value)
        {
            // Clear the buffer first
            for (int i = 0; i < 32; i++)
            {
                bytes[i] = 0;
            }

            if (!string.IsNullOrEmpty(value))
            {
                var encoded = System.Text.Encoding.UTF8.GetBytes(value);
                var length = Math.Min(encoded.Length, 31); // Reserve 1 byte for null terminator
                for (int i = 0; i < length; i++)
                {
                    bytes[i] = encoded[i];
                }
            }
        }

        public override string ToString()
        {
            // Find the null terminator
            int length = 0;
            for (int i = 0; i < 32; i++)
            {
                if (bytes[i] == 0)
                {
                    length = i;
                    break;
                }
                if (i == 31) length = 32; // No null terminator found, use full length
            }

            if (length == 0) return string.Empty;

            // Create byte array for decoding
            var buffer = new byte[length];
            for (int i = 0; i < length; i++)
            {
                buffer[i] = bytes[i];
            }

            return System.Text.Encoding.UTF8.GetString(buffer);
        }

        public bool Equals(Fixed32String other)
        {
            for (int i = 0; i < 32; i++)
            {
                if (bytes[i] != other.bytes[i])
                    return false;
            }
            return true;
        }

        public override bool Equals(object obj) => obj is Fixed32String other && Equals(other);

        public override int GetHashCode()
        {
            // Find the actual string length
            int length = 0;
            for (int i = 0; i < 32; i++)
            {
                if (bytes[i] == 0)
                {
                    length = i;
                    break;
                }
                if (i == 31) length = 32;
            }

            // Simple hash for performance
            int hash = 17;
            for (int i = 0; i < length; i++)
            {
                hash = hash * 31 + bytes[i];
            }
            return hash;
        }
        
        public static implicit operator string(Fixed32String fixedString) => fixedString.ToString();
        public static implicit operator Fixed32String(string str) => new Fixed32String(str);
        
        public static bool operator ==(Fixed32String left, Fixed32String right) => left.Equals(right);
        public static bool operator !=(Fixed32String left, Fixed32String right) => !left.Equals(right);
    }
}

using UnityEngine;
using UnityEngine.UIElements;
using UnityEditor;
using UnityEditor.Callbacks;
using UnityEditor.Experimental.GraphView;
using StateScript;

namespace StateScript.Editor
{
    /// <summary>
    /// Main editor window for editing StateFlow graphs using Unity's GraphView
    /// </summary>
    public class StateFlowEditorWindow : EditorWindow
    {
        private StateFlowAsset _currentAsset;
        private StateFlowGraphView _graphView;
        private StateFlowInspectorView _inspectorView;
        private StateFlowVariableView _variableView;

        [MenuItem("Window/StateScript/StateFlow Editor")]
        public static void OpenWindow()
        {
            var window = GetWindow<StateFlowEditorWindow>();
            window.titleContent = new GUIContent("StateFlow Editor");
            window.Show();
        }

        public static void OpenWindow(StateFlowAsset asset)
        {
            var window = GetWindow<StateFlowEditorWindow>();
            window.titleContent = new GUIContent($"StateFlow Editor - {asset.AssetName}");
            window.LoadAsset(asset);
            window.Show();
        }

        private void CreateGUI()
        {
            // Create the main container
            var root = rootVisualElement;
            CreateGUIManually(root);
            InitializeViews();
        }

        private void CreateGUIManually(VisualElement root)
        {
            // Create main layout
            var mainContainer = new VisualElement();
            mainContainer.style.flexDirection = FlexDirection.Row;
            mainContainer.style.flexGrow = 1;

            // Left panel for variables and inspector
            var leftPanel = new VisualElement();
            leftPanel.style.width = 300;
            leftPanel.style.backgroundColor = new Color(0.2f, 0.2f, 0.2f, 1f);
            leftPanel.name = "left-panel";

            // Graph view container
            var graphContainer = new VisualElement();
            graphContainer.style.flexGrow = 1;
            graphContainer.name = "graph-container";

            // Add toolbar
            var toolbar = new VisualElement();
            toolbar.style.flexDirection = FlexDirection.Row;
            toolbar.style.height = 22;
            toolbar.style.backgroundColor = new Color(0.22f, 0.22f, 0.22f, 1f);
            toolbar.style.borderBottomColor = new Color(0.13f, 0.13f, 0.13f, 1f);
            toolbar.style.borderBottomWidth = 1;
            toolbar.style.paddingLeft = 4;
            toolbar.style.paddingRight = 4;
            toolbar.style.paddingTop = 1;
            toolbar.style.paddingBottom = 1;

            var saveButton = new Button(() => SaveAsset()) { text = "Save" };
            var loadButton = new Button(() => LoadAssetDialog()) { text = "Load" };
            var newButton = new Button(() => CreateNewAsset()) { text = "New" };

            // Style buttons to look like proper toolbar buttons
            StyleAsToolbarButton(saveButton);
            StyleAsToolbarButton(loadButton);
            StyleAsToolbarButton(newButton);

            toolbar.Add(newButton);
            toolbar.Add(loadButton);
            toolbar.Add(saveButton);

            root.Add(toolbar);
            root.Add(mainContainer);
            mainContainer.Add(leftPanel);
            mainContainer.Add(graphContainer);
        }

        private void StyleAsToolbarButton(Button button)
        {
            button.style.height = 20;
            button.style.marginLeft = 0;
            button.style.marginRight = 1;
            button.style.marginTop = 0;
            button.style.marginBottom = 0;
            button.style.paddingLeft = 8;
            button.style.paddingRight = 8;
            button.style.paddingTop = 2;
            button.style.paddingBottom = 2;
            button.style.borderTopLeftRadius = 2;
            button.style.borderTopRightRadius = 2;
            button.style.borderBottomLeftRadius = 2;
            button.style.borderBottomRightRadius = 2;
            button.style.backgroundColor = new Color(0.35f, 0.35f, 0.35f, 1f);
            button.style.borderTopColor = new Color(0.5f, 0.5f, 0.5f, 1f);
            button.style.borderBottomColor = new Color(0.2f, 0.2f, 0.2f, 1f);
            button.style.borderLeftColor = new Color(0.4f, 0.4f, 0.4f, 1f);
            button.style.borderRightColor = new Color(0.4f, 0.4f, 0.4f, 1f);
            button.style.borderTopWidth = 1;
            button.style.borderBottomWidth = 1;
            button.style.borderLeftWidth = 1;
            button.style.borderRightWidth = 1;
            button.style.fontSize = 11;
            button.style.unityTextAlign = TextAnchor.MiddleCenter;

            // Add hover effects
            button.RegisterCallback<MouseEnterEvent>(evt =>
            {
                button.style.backgroundColor = new Color(0.45f, 0.45f, 0.45f, 1f);
            });

            button.RegisterCallback<MouseLeaveEvent>(evt =>
            {
                button.style.backgroundColor = new Color(0.35f, 0.35f, 0.35f, 1f);
            });
        }

        private void InitializeViews()
        {
            var root = rootVisualElement;

            // Find containers
            var leftPanel = root.Q("left-panel");
            var graphContainer = root.Q("graph-container");

            if (leftPanel == null || graphContainer == null)
            {
                Debug.LogError("Could not find required UI containers");
                return;
            }

            // Create graph view
            _graphView = new StateFlowGraphView(this);
            _graphView.StretchToParentSize();
            graphContainer.Add(_graphView);

            // Create variable view
            _variableView = new StateFlowVariableView();
            leftPanel.Add(_variableView);

            // Create inspector view
            _inspectorView = new StateFlowInspectorView();
            leftPanel.Add(_inspectorView);

            // Connect events
            _graphView.OnNodeSelected += _inspectorView.InspectNode;
            _variableView.OnVariableChanged += OnVariableChanged;
        }

        public void LoadAsset(StateFlowAsset asset)
        {
            _currentAsset = asset;

            if (_graphView != null)
            {
                _graphView.LoadStateFlow(asset.StateFlow);
            }

            if (_variableView != null)
            {
                _variableView.LoadVariables(asset.StateFlow.Variables);
            }

            titleContent = new GUIContent($"StateFlow Editor - {asset.AssetName}");
        }

        private void SaveAsset()
        {
            if (_currentAsset == null)
                return;

            // If this is a new asset without a file path, prompt for save location
            // Default to .stateflow extension
            var path = EditorUtility.SaveFilePanel("Save StateFlow", "Assets", _currentAsset.AssetName + ".stateflow", "stateflow");
            if (!string.IsNullOrEmpty(path))
            {
                _currentAsset.SaveToFile(path);
                AssetDatabase.Refresh();
                Debug.Log($"Saved StateFlow: {_currentAsset.AssetName} to {path}");

                // Update title to show saved status
                titleContent = new GUIContent($"StateFlow Editor - {_currentAsset.AssetName}");
            }
        }

        private void LoadAssetDialog()
        {
            // Show a menu to choose file type
            var menu = new GenericMenu();
            menu.AddItem(new GUIContent("Load .stateflow file"), false, () => LoadFileWithExtension("stateflow"));
            menu.AddItem(new GUIContent("Load .json file"), false, () => LoadFileWithExtension("json"));
            menu.ShowAsContext();
        }

        private void LoadFileWithExtension(string extension)
        {
            var path = EditorUtility.OpenFilePanel($"Load StateFlow ({extension})", "Assets", extension);
            if (!string.IsNullOrEmpty(path))
            {
                try
                {
                    var asset = StateFlowAsset.LoadFromFile(path);
                    LoadAsset(asset);
                }
                catch (System.Exception e)
                {
                    Debug.LogError($"Failed to load StateFlow: {e.Message}");
                    EditorUtility.DisplayDialog("Error",
                        $"Failed to load StateFlow:\n{e.Message}", "OK");
                }
            }
        }

        private void CreateNewAsset()
        {
            var asset = new StateFlowAsset("New StateFlow");
            LoadAsset(asset);
        }

        private void OnVariableChanged()
        {
            // StateFlowAsset is not a UnityEngine.Object, so we don't use SetDirty
            // The asset tracks its own dirty state internally
        }

        private void OnDisable()
        {
            // Save any pending changes
            if (_currentAsset != null && _currentAsset.IsDirty)
            {
                if (EditorUtility.DisplayDialog("Unsaved Changes",
                    "You have unsaved changes. Do you want to save before closing?",
                    "Save", "Don't Save"))
                {
                    SaveAsset();
                }
            }
        }
    }
}

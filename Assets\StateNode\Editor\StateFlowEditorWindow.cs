using UnityEngine;
using UnityEngine.UIElements;
using UnityEditor;
using UnityEditor.UIElements;
using UnityEditor.Callbacks;
using UnityEditor.Experimental.GraphView;
using StateScript;

namespace StateScript.Editor
{
    /// <summary>
    /// Main editor window for editing StateFlow graphs using Unity's GraphView
    /// </summary>
    public class StateFlowEditorWindow : EditorWindow
    {
        private StateFlowAsset _currentAsset;
        private StateFlowGraphView _graphView;
        private StateFlowInspectorView _inspectorView;
        private StateFlowVariableView _variableView;
        private Label _fileNameLabel;
        private bool _isDirty = false;
        private ToolbarToggle _gridToggleButton;

        [MenuItem("Window/StateScript/StateFlow Editor")]
        public static void OpenWindow()
        {
            var window = GetWindow<StateFlowEditorWindow>();
            window.titleContent = new GUIContent("StateFlow Editor");
            window.Show();
        }

        public static void OpenWindow(StateFlowAsset asset)
        {
            var window = GetWindow<StateFlowEditorWindow>();
            window.titleContent = new GUIContent($"StateFlow Editor - {asset.AssetName}");
            window.LoadAsset(asset);
            window.Show();
        }

        private void CreateGUI()
        {
            // Create the main container
            var root = rootVisualElement;
            CreateGUIManually(root);
            InitializeViews();

            // Register keyboard shortcuts
            RegisterKeyboardShortcuts();
        }

        private void CreateGUIManually(VisualElement root)
        {
            // Create toolbar
            var toolbar = new Toolbar();
            toolbar.style.justifyContent = Justify.SpaceBetween;

            // Left side of toolbar - File operations
            var leftToolbarContainer = new VisualElement();
            leftToolbarContainer.style.flexDirection = FlexDirection.Row;

            var newButton = new ToolbarButton(CreateNewAsset) { text = "New" };
            var loadButton = new ToolbarButton(LoadAssetDialog) { text = "Load" };
            var saveButton = new ToolbarButton(SaveAsset) { text = "Save" };

            leftToolbarContainer.Add(newButton);
            leftToolbarContainer.Add(loadButton);
            leftToolbarContainer.Add(saveButton);

            // Right side of toolbar - Grid toggle
            var rightToolbarContainer = new VisualElement();
            rightToolbarContainer.style.flexDirection = FlexDirection.Row;

            _gridToggleButton = new ToolbarToggle() { text = "Grid" };
            _gridToggleButton.value = true; // Default to on
            _gridToggleButton.RegisterValueChangedCallback(evt => ToggleGrid());
            rightToolbarContainer.Add(_gridToggleButton);

            toolbar.Add(leftToolbarContainer);
            toolbar.Add(rightToolbarContainer);

            // Create main layout
            var mainContainer = new VisualElement();
            mainContainer.style.flexDirection = FlexDirection.Row;
            mainContainer.style.flexGrow = 1;

            // Left panel for variables
            var leftPanel = new VisualElement();
            leftPanel.style.width = 300;
            leftPanel.style.backgroundColor = new Color(0.2f, 0.2f, 0.2f, 1f);
            leftPanel.name = "left-panel";

            // Graph view container
            var graphContainer = new VisualElement();
            graphContainer.style.flexGrow = 1;
            graphContainer.name = "graph-container";

            root.Add(toolbar);
            root.Add(mainContainer);
            mainContainer.Add(leftPanel);
            mainContainer.Add(graphContainer);
        }

        // Keep only the window menu item
        [MenuItem("Window/StateScript/StateFlow Editor", false, 1)]
        public static void OpenWindowMenuItem()
        {
            OpenWindow();
        }

        private void InitializeViews()
        {
            var root = rootVisualElement;

            // Find containers
            var leftPanel = root.Q("left-panel");
            var graphContainer = root.Q("graph-container");

            if (leftPanel == null || graphContainer == null)
            {
                Debug.LogError("Could not find required UI containers");
                return;
            }

            // Create graph view
            _graphView = new StateFlowGraphView(this);
            _graphView.StretchToParentSize();
            graphContainer.Add(_graphView);

            // Create file name label overlay
            _fileNameLabel = new Label("No file loaded");
            _fileNameLabel.style.position = Position.Absolute;
            _fileNameLabel.style.top = 10;
            _fileNameLabel.style.left = 10;
            _fileNameLabel.style.backgroundColor = new Color(0, 0, 0, 0.7f);
            _fileNameLabel.style.color = Color.white;
            _fileNameLabel.style.paddingTop = 4;
            _fileNameLabel.style.paddingBottom = 4;
            _fileNameLabel.style.paddingLeft = 8;
            _fileNameLabel.style.paddingRight = 8;
            _fileNameLabel.style.borderTopLeftRadius = 4;
            _fileNameLabel.style.borderTopRightRadius = 4;
            _fileNameLabel.style.borderBottomLeftRadius = 4;
            _fileNameLabel.style.borderBottomRightRadius = 4;
            _fileNameLabel.style.fontSize = 12;
            graphContainer.Add(_fileNameLabel);

            // Create variable view
            _variableView = new StateFlowVariableView();
            leftPanel.Add(_variableView);

            // Create inspector view as floating overlay on the right side
            _inspectorView = new StateFlowInspectorView();
            _inspectorView.style.position = Position.Absolute;
            _inspectorView.style.top = 10;
            _inspectorView.style.right = 10;
            _inspectorView.style.width = 300;
            _inspectorView.style.maxHeight = Length.Percent(80);
            _inspectorView.style.backgroundColor = new Color(0.25f, 0.25f, 0.25f, 0.95f);
            _inspectorView.style.borderTopLeftRadius = 8;
            _inspectorView.style.borderTopRightRadius = 8;
            _inspectorView.style.borderBottomLeftRadius = 8;
            _inspectorView.style.borderBottomRightRadius = 8;
            _inspectorView.style.borderTopColor = new Color(0.4f, 0.4f, 0.4f, 1f);
            _inspectorView.style.borderBottomColor = new Color(0.4f, 0.4f, 0.4f, 1f);
            _inspectorView.style.borderLeftColor = new Color(0.4f, 0.4f, 0.4f, 1f);
            _inspectorView.style.borderRightColor = new Color(0.4f, 0.4f, 0.4f, 1f);
            _inspectorView.style.borderTopWidth = 1;
            _inspectorView.style.borderBottomWidth = 1;
            _inspectorView.style.borderLeftWidth = 1;
            _inspectorView.style.borderRightWidth = 1;
            _inspectorView.style.display = DisplayStyle.None; // Initially hidden
            graphContainer.Add(_inspectorView);

            // Connect events
            _graphView.OnNodeSelected += _inspectorView.InspectNode;
            _variableView.OnVariableChanged += OnVariableChanged;

            // Initialize grid toggle button state
            UpdateGridToggleButton();
        }

        public void LoadAsset(StateFlowAsset asset)
        {
            _currentAsset = asset;

            if (_graphView != null)
            {
                _graphView.LoadStateFlow(asset.StateFlow);
            }

            if (_variableView != null)
            {
                _variableView.LoadVariables(asset.StateFlow.Variables);
            }

            SetDirty(false);
            UpdateFileNameDisplay();
        }

        private void SetDirty(bool dirty)
        {
            _isDirty = dirty;
            UpdateWindowTitle();
        }

        private void UpdateWindowTitle()
        {
            if (_currentAsset != null)
            {
                var title = $"StateFlow Editor - {_currentAsset.AssetName}";
                if (_isDirty)
                    title += "*";
                titleContent = new GUIContent(title);
            }
            else
            {
                titleContent = new GUIContent("StateFlow Editor");
            }
        }

        private void UpdateFileNameDisplay()
        {
            if (_fileNameLabel != null)
            {
                if (_currentAsset != null)
                {
                    var displayName = _currentAsset.AssetName;
                    if (_isDirty)
                        displayName += "*";
                    _fileNameLabel.text = displayName;
                }
                else
                {
                    _fileNameLabel.text = "No file loaded";
                }
            }
        }

        private void SaveAsset()
        {
            if (_currentAsset == null)
                return;

            // If this is a new asset without a file path, prompt for save location
            // Default to .stateflow extension
            var path = EditorUtility.SaveFilePanel("Save StateFlow", "Assets", _currentAsset.AssetName + ".stateflow", "stateflow");
            if (!string.IsNullOrEmpty(path))
            {
                _currentAsset.SaveToFile(path);
                AssetDatabase.Refresh();
                Debug.Log($"Saved StateFlow: {_currentAsset.AssetName} to {path}");

                // Clear dirty flag and update displays
                SetDirty(false);
            }
        }

        private void SaveAssetAs()
        {
            if (_currentAsset == null)
                return;

            // Always prompt for save location
            var path = EditorUtility.SaveFilePanel("Save StateFlow As", "Assets", _currentAsset.AssetName + ".stateflow", "stateflow");
            if (!string.IsNullOrEmpty(path))
            {
                _currentAsset.SaveToFile(path);
                AssetDatabase.Refresh();
                Debug.Log($"Saved StateFlow: {_currentAsset.AssetName} to {path}");

                // Clear dirty flag and update displays
                SetDirty(false);
            }
        }

        private void LoadAssetDialog()
        {
            // Show a menu to choose file type
            var menu = new GenericMenu();
            menu.AddItem(new GUIContent("Load .stateflow file"), false, () => LoadFileWithExtension("stateflow"));
            menu.AddItem(new GUIContent("Load .json file"), false, () => LoadFileWithExtension("json"));
            menu.ShowAsContext();
        }

        private void LoadFileWithExtension(string extension)
        {
            var path = EditorUtility.OpenFilePanel($"Load StateFlow ({extension})", "Assets", extension);
            if (!string.IsNullOrEmpty(path))
            {
                try
                {
                    var asset = StateFlowAsset.LoadFromFile(path);
                    LoadAsset(asset);
                }
                catch (System.Exception e)
                {
                    Debug.LogError($"Failed to load StateFlow: {e.Message}");
                    EditorUtility.DisplayDialog("Error",
                        $"Failed to load StateFlow:\n{e.Message}", "OK");
                }
            }
        }

        private void CreateNewAsset()
        {
            var asset = new StateFlowAsset("New StateFlow");
            LoadAsset(asset);
        }

        private void OnVariableChanged()
        {
            SetDirty(true);
        }

        public void OnGraphChanged()
        {
            SetDirty(true);
        }

        private void ToggleGrid()
        {
            if (_graphView != null && _gridToggleButton != null)
            {
                bool newVisibility = _gridToggleButton.value;
                _graphView.SetGridVisible(newVisibility);
            }
        }

        private void UpdateGridToggleButton()
        {
            if (_gridToggleButton != null && _graphView != null)
            {
                bool isVisible = _graphView.IsGridVisible();
                _gridToggleButton.SetValueWithoutNotify(isVisible);
            }
        }

        private void RegisterKeyboardShortcuts()
        {
            var root = rootVisualElement;
            root.RegisterCallback<KeyDownEvent>(OnKeyDown);
        }

        private void OnKeyDown(KeyDownEvent evt)
        {
            // Handle keyboard shortcuts
            if (evt.ctrlKey || evt.commandKey) // Support both Ctrl (Windows/Linux) and Cmd (Mac)
            {
                switch (evt.keyCode)
                {
                    case KeyCode.S:
                        evt.StopPropagation();
                        SaveAsset();
                        break;
                    case KeyCode.N:
                        evt.StopPropagation();
                        CreateNewAsset();
                        break;
                    case KeyCode.O:
                        evt.StopPropagation();
                        LoadAssetDialog();
                        break;
                }
            }
            else
            {
                switch (evt.keyCode)
                {
                    case KeyCode.F:
                        evt.StopPropagation();
                        FrameAll();
                        break;
                }
            }
        }

        private void FrameAll()
        {
            if (_graphView != null)
            {
                _graphView.FrameAll();
            }
        }

        private void OnDisable()
        {
            // Save any pending changes
            if (_currentAsset != null && _isDirty)
            {
                if (EditorUtility.DisplayDialog("Unsaved Changes",
                    "You have unsaved changes. Do you want to save before closing?",
                    "Save", "Don't Save"))
                {
                    SaveAsset();
                }
            }
        }
    }
}

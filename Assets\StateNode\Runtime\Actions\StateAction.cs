namespace StateScript
{
    /// <summary>
    /// Base class for all state actions
    /// Actions can have individual timing and run simultaneously within a StateActionNode
    /// </summary>
    public abstract class StateAction : ISerializable
    {
        public float Duration { get; set; }
        public float Delay { get; set; }
        public StateActionStatus Status { get; set; }
        
        /// <summary>
        /// Type discriminator for polymorphic deserialization
        /// </summary>
        public abstract string TypeName { get; }
        
        protected StateAction()
        {
            Duration = 0f;
            Delay = 0f;
            Status = StateActionStatus.None;
        }
        
        /// <summary>
        /// Called when the action starts (after delay)
        /// </summary>
        public abstract void OnEnter(StateFlow stateFlow, IStateContext context);
        
        /// <summary>
        /// Called every frame while the action is running
        /// </summary>
        public abstract void OnUpdate(StateFlow stateFlow, IStateContext context, float deltaTime);
        
        /// <summary>
        /// Called when the action completes or is interrupted
        /// </summary>
        public abstract void OnExit(StateFlow stateFlow, IStateContext context);
        
        /// <summary>
        /// Reset the action to its initial state
        /// </summary>
        public virtual void Reset()
        {
            Status = StateActionStatus.None;
        }
        
        public virtual void Serialize(ISerializer serializer)
        {
            serializer.WriteString("actionType", TypeName);
            serializer.WriteFloat("duration", Duration);
            serializer.WriteFloat("delay", Delay);
            serializer.WriteInt("status", (int)Status);
        }
        
        public virtual void Deserialize(ISerializer serializer)
        {
            // ActionType is handled by factory, not here
            Duration = serializer.ReadFloat("duration");
            Delay = serializer.ReadFloat("delay");
            Status = (StateActionStatus)serializer.ReadInt("status");
        }
        
        public override string ToString()
        {
            return $"{GetType().Name} (Delay: {Delay}s, Duration: {Duration}s)";
        }
    }
    
    /// <summary>
    /// Example action that waits for a specified duration
    /// </summary>
    public class WaitAction : StateAction
    {
        public override string TypeName => "Wait";
        
        public WaitAction() { }
        
        public WaitAction(float duration, float delay = 0f)
        {
            Duration = duration;
            Delay = delay;
        }
        
        public override void OnEnter(StateFlow stateFlow, IStateContext context)
        {
            Status = StateActionStatus.Running;
        }
        
        public override void OnUpdate(StateFlow stateFlow, IStateContext context, float deltaTime)
        {
            // Wait action doesn't need to do anything during update
            // Completion is handled by StateActionNode based on timing
        }
        
        public override void OnExit(StateFlow stateFlow, IStateContext context)
        {
            Status = StateActionStatus.Completed;
        }
    }
    
    /// <summary>
    /// Example action that logs a message
    /// </summary>
    public class LogAction : StateAction
    {
        public string Message { get; set; } = "";
        
        public override string TypeName => "Log";
        
        public LogAction() { }
        
        public LogAction(string message, float delay = 0f)
        {
            Message = message;
            Delay = delay;
            Duration = 0f; // Instant action
        }
        
        public override void OnEnter(StateFlow stateFlow, IStateContext context)
        {
            Status = StateActionStatus.Running;
            UnityEngine.Debug.Log($"[StateScript] {Message}");
        }
        
        public override void OnUpdate(StateFlow stateFlow, IStateContext context, float deltaTime)
        {
            // Log action completes immediately
        }
        
        public override void OnExit(StateFlow stateFlow, IStateContext context)
        {
            Status = StateActionStatus.Completed;
        }
        
        public override void Serialize(ISerializer serializer)
        {
            base.Serialize(serializer);
            serializer.WriteString("message", Message);
        }
        
        public override void Deserialize(ISerializer serializer)
        {
            base.Deserialize(serializer);
            Message = serializer.ReadString("message");
        }
    }
}

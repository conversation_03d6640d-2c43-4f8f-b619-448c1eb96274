%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!74 &7400000
AnimationClip:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: ReadyBow
  serializedVersion: 6
  m_Legacy: 0
  m_Compressed: 0
  m_UseHighQualityCurve: 1
  m_RotationCurves: []
  m_CompressedRotationCurves: []
  m_EulerCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: {x: 0, y: 0, z: 30}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    path: Body/Upper/ArmL/ForearmL[2]/HandL
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: {x: 0, y: 0, z: 0}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
      - serializedVersion: 2
        time: 0.25
        value: {x: 0, y: 0, z: -2}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
      - serializedVersion: 2
        time: 0.5
        value: {x: 0, y: 0, z: 0}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    path: Body/Upper/Head
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: {x: 0, y: 0, z: 0}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
      - serializedVersion: 2
        time: 0.25
        value: {x: 0, y: 0, z: 10}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
      - serializedVersion: 2
        time: 0.5
        value: {x: 0, y: 0, z: 0}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    path: Body/Upper/ArmR[1]/ForearmR
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: {x: 0, y: 0, z: 20}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    path: Body/Upper/ArmR[1]
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: {x: 0, y: 0, z: -10}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    path: Body/Upper/ArmR[1]/ForearmR/HandR
  m_PositionCurves: []
  m_ScaleCurves: []
  m_FloatCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: 0
        inSlope: Infinity
        outSlope: Infinity
        tangentMode: 103
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_IsActive
    path: Body/Upper/ArmL/ForearmL[1]
    classID: 1
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: 1
        inSlope: Infinity
        outSlope: Infinity
        tangentMode: 103
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_IsActive
    path: Body/Upper/ArmL/ForearmL[2]
    classID: 1
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: 0
        inSlope: Infinity
        outSlope: Infinity
        tangentMode: 103
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_IsActive
    path: Body/Upper/ArmR[1]/ForearmR/HandR/Firearm
    classID: 1
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: 0
        inSlope: Infinity
        outSlope: Infinity
        tangentMode: 103
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_IsActive
    path: Body/Upper/ArmR[1]/ForearmR/HandR/Finger
    classID: 1
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: 0
        inSlope: Infinity
        outSlope: Infinity
        tangentMode: 103
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_IsActive
    path: Body/Upper/ArmR[1]/ForearmR/HandR/ArmL
    classID: 1
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: 1
        inSlope: Infinity
        outSlope: Infinity
        tangentMode: 103
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_IsActive
    path: Body/Upper/ArmL
    classID: 1
    script: {fileID: 0}
  m_PPtrCurves: []
  m_SampleRate: 60
  m_WrapMode: 0
  m_Bounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  m_ClipBindingConstant:
    genericBindings:
    - serializedVersion: 2
      path: 2251687434
      attribute: 4
      script: {fileID: 0}
      typeID: 4
      customType: 4
      isPPtrCurve: 0
    - serializedVersion: 2
      path: 935776780
      attribute: 4
      script: {fileID: 0}
      typeID: 4
      customType: 4
      isPPtrCurve: 0
    - serializedVersion: 2
      path: 2460623317
      attribute: 2086281974
      script: {fileID: 0}
      typeID: 1
      customType: 0
      isPPtrCurve: 0
    - serializedVersion: 2
      path: 3112661526
      attribute: 2086281974
      script: {fileID: 0}
      typeID: 1
      customType: 0
      isPPtrCurve: 0
    - serializedVersion: 2
      path: 87740368
      attribute: 2086281974
      script: {fileID: 0}
      typeID: 1
      customType: 0
      isPPtrCurve: 0
    - serializedVersion: 2
      path: 1221937857
      attribute: 2086281974
      script: {fileID: 0}
      typeID: 1
      customType: 0
      isPPtrCurve: 0
    - serializedVersion: 2
      path: 3457590586
      attribute: 2086281974
      script: {fileID: 0}
      typeID: 1
      customType: 0
      isPPtrCurve: 0
    - serializedVersion: 2
      path: 2069306243
      attribute: 2086281974
      script: {fileID: 0}
      typeID: 1
      customType: 0
      isPPtrCurve: 0
    - serializedVersion: 2
      path: 2081409448
      attribute: 4
      script: {fileID: 0}
      typeID: 4
      customType: 4
      isPPtrCurve: 0
    - serializedVersion: 2
      path: 1134803067
      attribute: 4
      script: {fileID: 0}
      typeID: 4
      customType: 4
      isPPtrCurve: 0
    - serializedVersion: 2
      path: 2624144242
      attribute: 4
      script: {fileID: 0}
      typeID: 4
      customType: 4
      isPPtrCurve: 0
    pptrCurveMapping: []
  m_AnimationClipSettings:
    serializedVersion: 2
    m_AdditiveReferencePoseClip: {fileID: 0}
    m_AdditiveReferencePoseTime: 0
    m_StartTime: 0
    m_StopTime: 0.5
    m_OrientationOffsetY: 0
    m_Level: 0
    m_CycleOffset: 0
    m_HasAdditiveReferencePose: 0
    m_LoopTime: 1
    m_LoopBlend: 0
    m_LoopBlendOrientation: 0
    m_LoopBlendPositionY: 0
    m_LoopBlendPositionXZ: 0
    m_KeepOriginalOrientation: 0
    m_KeepOriginalPositionY: 1
    m_KeepOriginalPositionXZ: 0
    m_HeightFromFeet: 0
    m_Mirror: 0
  m_EditorCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: 0
        inSlope: Infinity
        outSlope: Infinity
        tangentMode: 103
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_IsActive
    path: Body/Upper/ArmL/ForearmL[1]
    classID: 1
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: 1
        inSlope: Infinity
        outSlope: Infinity
        tangentMode: 103
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_IsActive
    path: Body/Upper/ArmL/ForearmL[2]
    classID: 1
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: localEulerAnglesRaw.x
    path: Body/Upper/ArmL/ForearmL[2]/HandL
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: localEulerAnglesRaw.y
    path: Body/Upper/ArmL/ForearmL[2]/HandL
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: 30
        inSlope: 0
        outSlope: 0
        tangentMode: 136
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: localEulerAnglesRaw.z
    path: Body/Upper/ArmL/ForearmL[2]/HandL
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
      - serializedVersion: 2
        time: 0.25
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
      - serializedVersion: 2
        time: 0.5
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: localEulerAnglesRaw.x
    path: Body/Upper/Head
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
      - serializedVersion: 2
        time: 0.25
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
      - serializedVersion: 2
        time: 0.5
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: localEulerAnglesRaw.y
    path: Body/Upper/Head
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
      - serializedVersion: 2
        time: 0.25
        value: -2
        inSlope: 0
        outSlope: 0
        tangentMode: 136
      - serializedVersion: 2
        time: 0.5
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: localEulerAnglesRaw.z
    path: Body/Upper/Head
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
      - serializedVersion: 2
        time: 0.25
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
      - serializedVersion: 2
        time: 0.5
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: localEulerAnglesRaw.x
    path: Body/Upper/ArmR[1]/ForearmR
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
      - serializedVersion: 2
        time: 0.25
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
      - serializedVersion: 2
        time: 0.5
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: localEulerAnglesRaw.y
    path: Body/Upper/ArmR[1]/ForearmR
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
      - serializedVersion: 2
        time: 0.25
        value: 10
        inSlope: 0
        outSlope: 0
        tangentMode: 136
      - serializedVersion: 2
        time: 0.5
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: localEulerAnglesRaw.z
    path: Body/Upper/ArmR[1]/ForearmR
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: localEulerAnglesRaw.x
    path: Body/Upper/ArmR[1]
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: localEulerAnglesRaw.y
    path: Body/Upper/ArmR[1]
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: 20
        inSlope: 0
        outSlope: 0
        tangentMode: 136
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: localEulerAnglesRaw.z
    path: Body/Upper/ArmR[1]
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: 0
        inSlope: Infinity
        outSlope: Infinity
        tangentMode: 103
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_IsActive
    path: Body/Upper/ArmR[1]/ForearmR/HandR/Firearm
    classID: 1
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: localEulerAnglesRaw.x
    path: Body/Upper/ArmR[1]/ForearmR/HandR
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: localEulerAnglesRaw.y
    path: Body/Upper/ArmR[1]/ForearmR/HandR
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: -10
        inSlope: 0
        outSlope: 0
        tangentMode: 136
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: localEulerAnglesRaw.z
    path: Body/Upper/ArmR[1]/ForearmR/HandR
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: 0
        inSlope: Infinity
        outSlope: Infinity
        tangentMode: 103
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_IsActive
    path: Body/Upper/ArmR[1]/ForearmR/HandR/Finger
    classID: 1
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: 0
        inSlope: Infinity
        outSlope: Infinity
        tangentMode: 103
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_IsActive
    path: Body/Upper/ArmR[1]/ForearmR/HandR/ArmL
    classID: 1
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: 1
        inSlope: Infinity
        outSlope: Infinity
        tangentMode: 103
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_IsActive
    path: Body/Upper/ArmL
    classID: 1
    script: {fileID: 0}
  m_EulerEditorCurves:
  - curve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalEulerAngles.z
    path: Body/Upper/ArmR[1]/ForearmR
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalEulerAngles.y
    path: Body/Upper/ArmR[1]/ForearmR
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalEulerAngles.x
    path: Body/Upper/ArmR[1]/ForearmR
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalEulerAngles.x
    path: Body/Upper/ArmL/ForearmL[2]/HandL
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalEulerAngles.y
    path: Body/Upper/ArmL/ForearmL[2]/HandL
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalEulerAngles.z
    path: Body/Upper/ArmL/ForearmL[2]/HandL
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalEulerAngles.x
    path: Body/Upper/Head
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalEulerAngles.y
    path: Body/Upper/Head
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalEulerAngles.z
    path: Body/Upper/Head
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalEulerAngles.z
    path: Body/Upper/ArmR[1]
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalEulerAngles.y
    path: Body/Upper/ArmR[1]
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalEulerAngles.x
    path: Body/Upper/ArmR[1]
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalEulerAngles.x
    path: Body/Upper/ArmR[1]/ForearmR/HandR
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalEulerAngles.y
    path: Body/Upper/ArmR[1]/ForearmR/HandR
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalEulerAngles.z
    path: Body/Upper/ArmR[1]/ForearmR/HandR
    classID: 4
    script: {fileID: 0}
  m_HasGenericRootTransform: 0
  m_HasMotionFloatCurves: 0
  m_GenerateMotionCurves: 0
  m_Events: []

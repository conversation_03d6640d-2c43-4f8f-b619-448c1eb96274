%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!74 &7400000
AnimationClip:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: JabMeleePaired
  serializedVersion: 6
  m_Legacy: 0
  m_Compressed: 0
  m_UseHighQualityCurve: 1
  m_RotationCurves: []
  m_CompressedRotationCurves: []
  m_EulerCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: {x: 0, y: 0, z: 0}
        inSlope: {x: 0, y: 0, z: 40}
        outSlope: {x: 0, y: 0, z: 40}
        tangentMode: 0
      - serializedVersion: 2
        time: 0.25
        value: {x: 0, y: 0, z: 10}
        inSlope: {x: 0, y: 0, z: -129.99998}
        outSlope: {x: 0, y: 0, z: -129.99998}
        tangentMode: 0
      - serializedVersion: 2
        time: 0.33333334
        value: {x: 0, y: 0, z: -15}
        inSlope: {x: 0, y: 0, z: -104.999985}
        outSlope: {x: 0, y: 0, z: -104.999985}
        tangentMode: 0
      - serializedVersion: 2
        time: 0.5
        value: {x: 0, y: 0, z: 0}
        inSlope: {x: 0, y: 0, z: 90.00001}
        outSlope: {x: 0, y: 0, z: 90.00001}
        tangentMode: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    path: Body/Upper
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: {x: 0, y: 0, z: -15}
        inSlope: {x: 0, y: 0, z: -140}
        outSlope: {x: 0, y: 0, z: -140}
        tangentMode: 0
      - serializedVersion: 2
        time: 0.25
        value: {x: 0, y: 0, z: -50}
        inSlope: {x: 0, y: 0, z: 289.99997}
        outSlope: {x: 0, y: 0, z: 289.99997}
        tangentMode: 0
      - serializedVersion: 2
        time: 0.33333334
        value: {x: 0, y: 0, z: 10}
        inSlope: {x: 0, y: 0, z: 284.99997}
        outSlope: {x: 0, y: 0, z: 284.99997}
        tangentMode: 0
      - serializedVersion: 2
        time: 0.5
        value: {x: 0, y: 0, z: -15}
        inSlope: {x: 0, y: 0, z: -150.00002}
        outSlope: {x: 0, y: 0, z: -150.00002}
        tangentMode: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    path: Body/Upper/ArmL
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: {x: 0, y: 0, z: 0}
        inSlope: {x: 0, y: 0, z: 40}
        outSlope: {x: 0, y: 0, z: 40}
        tangentMode: 0
      - serializedVersion: 2
        time: 0.25
        value: {x: 0, y: 0, z: 10}
        inSlope: {x: 0, y: 0, z: 20}
        outSlope: {x: 0, y: 0, z: 20}
        tangentMode: 0
      - serializedVersion: 2
        time: 0.33333334
        value: {x: 0, y: 0, z: 10}
        inSlope: {x: 0, y: 0, z: -30.000002}
        outSlope: {x: 0, y: 0, z: -30.000002}
        tangentMode: 0
      - serializedVersion: 2
        time: 0.5
        value: {x: 0, y: 0, z: 0}
        inSlope: {x: 0, y: 0, z: -60.000004}
        outSlope: {x: 0, y: 0, z: -60.000004}
        tangentMode: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    path: Body/Upper/ArmL/ForearmL[1]
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: {x: 0, y: 0, z: 10}
        inSlope: {x: 0, y: 0, z: -120}
        outSlope: {x: 0, y: 0, z: -120}
        tangentMode: 0
      - serializedVersion: 2
        time: 0.25
        value: {x: 0, y: 0, z: -20}
        inSlope: {x: 0, y: 0, z: 659.99994}
        outSlope: {x: 0, y: 0, z: 659.99994}
        tangentMode: 0
      - serializedVersion: 2
        time: 0.33333334
        value: {x: 0, y: 0, z: 100}
        inSlope: {x: 0, y: 0, z: 449.9999}
        outSlope: {x: 0, y: 0, z: 449.9999}
        tangentMode: 0
      - serializedVersion: 2
        time: 0.5
        value: {x: 0, y: 0, z: 10}
        inSlope: {x: 0, y: 0, z: -540.00006}
        outSlope: {x: 0, y: 0, z: -540.00006}
        tangentMode: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    path: Body/Upper/ArmR[1]
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: {x: 0, y: 0, z: 20}
        inSlope: {x: 0, y: 0, z: 177.716}
        outSlope: {x: 0, y: 0, z: 177.716}
        tangentMode: 0
      - serializedVersion: 2
        time: 0.25
        value: {x: 0, y: 0, z: 64.429}
        inSlope: {x: 0, y: 0, z: -327.71594}
        outSlope: {x: 0, y: 0, z: -327.71594}
        tangentMode: 0
      - serializedVersion: 2
        time: 0.33333334
        value: {x: 0, y: 0, z: -5}
        inSlope: {x: 0, y: 0, z: -341.57394}
        outSlope: {x: 0, y: 0, z: -341.57394}
        tangentMode: 0
      - serializedVersion: 2
        time: 0.5
        value: {x: 0, y: 0, z: 20}
        inSlope: {x: 0, y: 0, z: 150.00002}
        outSlope: {x: 0, y: 0, z: 150.00002}
        tangentMode: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    path: Body/Upper/ArmR[1]/ForearmR
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: {x: 0, y: 0, z: -30}
        inSlope: {x: 0, y: 0, z: -60}
        outSlope: {x: 0, y: 0, z: -60}
        tangentMode: 0
      - serializedVersion: 2
        time: 0.25
        value: {x: 0, y: 0, z: -45}
        inSlope: {x: 0, y: 0, z: -179.99998}
        outSlope: {x: 0, y: 0, z: -179.99998}
        tangentMode: 0
      - serializedVersion: 2
        time: 0.33333334
        value: {x: 0, y: 0, z: -70}
        inSlope: {x: 0, y: 0, z: -29.999977}
        outSlope: {x: 0, y: 0, z: -29.999977}
        tangentMode: 0
      - serializedVersion: 2
        time: 0.5
        value: {x: 0, y: 0, z: -30}
        inSlope: {x: 0, y: 0, z: 240.00002}
        outSlope: {x: 0, y: 0, z: 240.00002}
        tangentMode: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    path: Body/Upper/ArmR[1]/ForearmR/HandR
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: {x: 0, y: 0, z: 0}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
      - serializedVersion: 2
        time: 0.25
        value: {x: 0, y: 0, z: -5}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
      - serializedVersion: 2
        time: 0.33333334
        value: {x: 0, y: 0, z: 5}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
      - serializedVersion: 2
        time: 0.5
        value: {x: 0, y: 0, z: 0}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    path: Body/Upper/Head
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: {x: 0, y: 0, z: 10}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
      - serializedVersion: 2
        time: 0.25
        value: {x: 0, y: 0, z: 0}
        inSlope: {x: 0, y: 0, z: -120}
        outSlope: {x: 0, y: 0, z: -120}
        tangentMode: 0
      - serializedVersion: 2
        time: 0.33333334
        value: {x: 0, y: 0, z: -40}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
      - serializedVersion: 2
        time: 0.5
        value: {x: 0, y: 0, z: 10}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    path: Body/Upper/ArmL/ForearmL[1]/HandL
  m_PositionCurves: []
  m_ScaleCurves: []
  m_FloatCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 34
      - serializedVersion: 2
        time: 0.5
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 34
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_IsActive
    path: Body/Upper/ArmL
    classID: 1
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 34
      - serializedVersion: 2
        time: 0.5
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 34
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_IsActive
    path: Body/Upper/ArmR[1]/ForearmR/HandR/ArmL
    classID: 1
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: 0
        inSlope: Infinity
        outSlope: Infinity
        tangentMode: 103
      - serializedVersion: 2
        time: 0.5
        value: 0
        inSlope: Infinity
        outSlope: Infinity
        tangentMode: 103
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_IsActive
    path: Body/Upper/ArmR[1]/ForearmR/HandR/Finger
    classID: 1
    script: {fileID: 0}
  m_PPtrCurves: []
  m_SampleRate: 60
  m_WrapMode: 0
  m_Bounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  m_ClipBindingConstant:
    genericBindings:
    - serializedVersion: 2
      path: 3928840929
      attribute: 4
      script: {fileID: 0}
      typeID: 4
      customType: 4
      isPPtrCurve: 0
    - serializedVersion: 2
      path: 2069306243
      attribute: 4
      script: {fileID: 0}
      typeID: 4
      customType: 4
      isPPtrCurve: 0
    - serializedVersion: 2
      path: 2460623317
      attribute: 4
      script: {fileID: 0}
      typeID: 4
      customType: 4
      isPPtrCurve: 0
    - serializedVersion: 2
      path: 1134803067
      attribute: 4
      script: {fileID: 0}
      typeID: 4
      customType: 4
      isPPtrCurve: 0
    - serializedVersion: 2
      path: 935776780
      attribute: 4
      script: {fileID: 0}
      typeID: 4
      customType: 4
      isPPtrCurve: 0
    - serializedVersion: 2
      path: 2624144242
      attribute: 4
      script: {fileID: 0}
      typeID: 4
      customType: 4
      isPPtrCurve: 0
    - serializedVersion: 2
      path: 2251687434
      attribute: 4
      script: {fileID: 0}
      typeID: 4
      customType: 4
      isPPtrCurve: 0
    - serializedVersion: 2
      path: 4068527691
      attribute: 4
      script: {fileID: 0}
      typeID: 4
      customType: 4
      isPPtrCurve: 0
    - serializedVersion: 2
      path: 1221937857
      attribute: 2086281974
      script: {fileID: 0}
      typeID: 1
      customType: 0
      isPPtrCurve: 0
    - serializedVersion: 2
      path: 2069306243
      attribute: 2086281974
      script: {fileID: 0}
      typeID: 1
      customType: 0
      isPPtrCurve: 0
    - serializedVersion: 2
      path: 3457590586
      attribute: 2086281974
      script: {fileID: 0}
      typeID: 1
      customType: 0
      isPPtrCurve: 0
    pptrCurveMapping: []
  m_AnimationClipSettings:
    serializedVersion: 2
    m_AdditiveReferencePoseClip: {fileID: 0}
    m_AdditiveReferencePoseTime: 0
    m_StartTime: 0
    m_StopTime: 0.5
    m_OrientationOffsetY: 0
    m_Level: 0
    m_CycleOffset: 0
    m_HasAdditiveReferencePose: 0
    m_LoopTime: 1
    m_LoopBlend: 0
    m_LoopBlendOrientation: 0
    m_LoopBlendPositionY: 0
    m_LoopBlendPositionXZ: 0
    m_KeepOriginalOrientation: 0
    m_KeepOriginalPositionY: 1
    m_KeepOriginalPositionXZ: 0
    m_HeightFromFeet: 0
    m_Mirror: 0
  m_EditorCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 34
      - serializedVersion: 2
        time: 0.25
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 34
      - serializedVersion: 2
        time: 0.33333334
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 34
      - serializedVersion: 2
        time: 0.5
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 34
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: localEulerAnglesRaw.x
    path: Body/Upper
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 34
      - serializedVersion: 2
        time: 0.25
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 34
      - serializedVersion: 2
        time: 0.33333334
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 34
      - serializedVersion: 2
        time: 0.5
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 34
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: localEulerAnglesRaw.y
    path: Body/Upper
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: 0
        inSlope: 40
        outSlope: 40
        tangentMode: 34
      - serializedVersion: 2
        time: 0.25
        value: 10
        inSlope: -129.99998
        outSlope: -129.99998
        tangentMode: 34
      - serializedVersion: 2
        time: 0.33333334
        value: -15
        inSlope: -104.999985
        outSlope: -104.999985
        tangentMode: 34
      - serializedVersion: 2
        time: 0.5
        value: 0
        inSlope: 90.00001
        outSlope: 90.00001
        tangentMode: 34
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: localEulerAnglesRaw.z
    path: Body/Upper
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 34
      - serializedVersion: 2
        time: 0.25
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 34
      - serializedVersion: 2
        time: 0.33333334
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 34
      - serializedVersion: 2
        time: 0.5
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 34
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: localEulerAnglesRaw.x
    path: Body/Upper/ArmL
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 34
      - serializedVersion: 2
        time: 0.25
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 34
      - serializedVersion: 2
        time: 0.33333334
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 34
      - serializedVersion: 2
        time: 0.5
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 34
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: localEulerAnglesRaw.y
    path: Body/Upper/ArmL
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: -15
        inSlope: -140
        outSlope: -140
        tangentMode: 34
      - serializedVersion: 2
        time: 0.25
        value: -50
        inSlope: 289.99997
        outSlope: 289.99997
        tangentMode: 34
      - serializedVersion: 2
        time: 0.33333334
        value: 10
        inSlope: 284.99997
        outSlope: 284.99997
        tangentMode: 34
      - serializedVersion: 2
        time: 0.5
        value: -15
        inSlope: -150.00002
        outSlope: -150.00002
        tangentMode: 34
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: localEulerAnglesRaw.z
    path: Body/Upper/ArmL
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 34
      - serializedVersion: 2
        time: 0.5
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 34
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_IsActive
    path: Body/Upper/ArmL
    classID: 1
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 34
      - serializedVersion: 2
        time: 0.25
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 34
      - serializedVersion: 2
        time: 0.33333334
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 34
      - serializedVersion: 2
        time: 0.5
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 34
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: localEulerAnglesRaw.x
    path: Body/Upper/ArmL/ForearmL[1]
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 34
      - serializedVersion: 2
        time: 0.25
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 34
      - serializedVersion: 2
        time: 0.33333334
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 34
      - serializedVersion: 2
        time: 0.5
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 34
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: localEulerAnglesRaw.y
    path: Body/Upper/ArmL/ForearmL[1]
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: 0
        inSlope: 40
        outSlope: 40
        tangentMode: 34
      - serializedVersion: 2
        time: 0.25
        value: 10
        inSlope: 20
        outSlope: 20
        tangentMode: 34
      - serializedVersion: 2
        time: 0.33333334
        value: 10
        inSlope: -30.000002
        outSlope: -30.000002
        tangentMode: 34
      - serializedVersion: 2
        time: 0.5
        value: 0
        inSlope: -60.000004
        outSlope: -60.000004
        tangentMode: 34
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: localEulerAnglesRaw.z
    path: Body/Upper/ArmL/ForearmL[1]
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 34
      - serializedVersion: 2
        time: 0.25
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 34
      - serializedVersion: 2
        time: 0.33333334
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 34
      - serializedVersion: 2
        time: 0.5
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 34
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: localEulerAnglesRaw.x
    path: Body/Upper/ArmR[1]
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 34
      - serializedVersion: 2
        time: 0.25
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 34
      - serializedVersion: 2
        time: 0.33333334
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 34
      - serializedVersion: 2
        time: 0.5
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 34
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: localEulerAnglesRaw.y
    path: Body/Upper/ArmR[1]
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: 10
        inSlope: -120
        outSlope: -120
        tangentMode: 34
      - serializedVersion: 2
        time: 0.25
        value: -20
        inSlope: 659.99994
        outSlope: 659.99994
        tangentMode: 34
      - serializedVersion: 2
        time: 0.33333334
        value: 100
        inSlope: 449.9999
        outSlope: 449.9999
        tangentMode: 34
      - serializedVersion: 2
        time: 0.5
        value: 10
        inSlope: -540.00006
        outSlope: -540.00006
        tangentMode: 34
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: localEulerAnglesRaw.z
    path: Body/Upper/ArmR[1]
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 34
      - serializedVersion: 2
        time: 0.25
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 34
      - serializedVersion: 2
        time: 0.33333334
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 34
      - serializedVersion: 2
        time: 0.5
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 34
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: localEulerAnglesRaw.x
    path: Body/Upper/ArmR[1]/ForearmR
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 34
      - serializedVersion: 2
        time: 0.25
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 34
      - serializedVersion: 2
        time: 0.33333334
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 34
      - serializedVersion: 2
        time: 0.5
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 34
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: localEulerAnglesRaw.y
    path: Body/Upper/ArmR[1]/ForearmR
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: 20
        inSlope: 177.716
        outSlope: 177.716
        tangentMode: 34
      - serializedVersion: 2
        time: 0.25
        value: 64.429
        inSlope: -327.71594
        outSlope: -327.71594
        tangentMode: 34
      - serializedVersion: 2
        time: 0.33333334
        value: -5
        inSlope: -341.57394
        outSlope: -341.57394
        tangentMode: 34
      - serializedVersion: 2
        time: 0.5
        value: 20
        inSlope: 150.00002
        outSlope: 150.00002
        tangentMode: 34
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: localEulerAnglesRaw.z
    path: Body/Upper/ArmR[1]/ForearmR
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 34
      - serializedVersion: 2
        time: 0.25
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 34
      - serializedVersion: 2
        time: 0.33333334
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 34
      - serializedVersion: 2
        time: 0.5
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 34
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: localEulerAnglesRaw.x
    path: Body/Upper/ArmR[1]/ForearmR/HandR
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 34
      - serializedVersion: 2
        time: 0.25
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 34
      - serializedVersion: 2
        time: 0.33333334
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 34
      - serializedVersion: 2
        time: 0.5
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 34
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: localEulerAnglesRaw.y
    path: Body/Upper/ArmR[1]/ForearmR/HandR
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: -30
        inSlope: -60
        outSlope: -60
        tangentMode: 34
      - serializedVersion: 2
        time: 0.25
        value: -45
        inSlope: -179.99998
        outSlope: -179.99998
        tangentMode: 34
      - serializedVersion: 2
        time: 0.33333334
        value: -70
        inSlope: -29.999977
        outSlope: -29.999977
        tangentMode: 34
      - serializedVersion: 2
        time: 0.5
        value: -30
        inSlope: 240.00002
        outSlope: 240.00002
        tangentMode: 34
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: localEulerAnglesRaw.z
    path: Body/Upper/ArmR[1]/ForearmR/HandR
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 34
      - serializedVersion: 2
        time: 0.5
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 34
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_IsActive
    path: Body/Upper/ArmR[1]/ForearmR/HandR/ArmL
    classID: 1
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: 0
        inSlope: Infinity
        outSlope: Infinity
        tangentMode: 103
      - serializedVersion: 2
        time: 0.5
        value: 0
        inSlope: Infinity
        outSlope: Infinity
        tangentMode: 103
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_IsActive
    path: Body/Upper/ArmR[1]/ForearmR/HandR/Finger
    classID: 1
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
      - serializedVersion: 2
        time: 0.25
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
      - serializedVersion: 2
        time: 0.33333334
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
      - serializedVersion: 2
        time: 0.5
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: localEulerAnglesRaw.x
    path: Body/Upper/Head
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
      - serializedVersion: 2
        time: 0.25
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
      - serializedVersion: 2
        time: 0.33333334
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
      - serializedVersion: 2
        time: 0.5
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: localEulerAnglesRaw.y
    path: Body/Upper/Head
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
      - serializedVersion: 2
        time: 0.25
        value: -5
        inSlope: 0
        outSlope: 0
        tangentMode: 136
      - serializedVersion: 2
        time: 0.33333334
        value: 5
        inSlope: 0
        outSlope: 0
        tangentMode: 136
      - serializedVersion: 2
        time: 0.5
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: localEulerAnglesRaw.z
    path: Body/Upper/Head
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
      - serializedVersion: 2
        time: 0.25
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
      - serializedVersion: 2
        time: 0.33333334
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
      - serializedVersion: 2
        time: 0.5
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: localEulerAnglesRaw.x
    path: Body/Upper/ArmL/ForearmL[1]/HandL
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
      - serializedVersion: 2
        time: 0.25
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
      - serializedVersion: 2
        time: 0.33333334
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
      - serializedVersion: 2
        time: 0.5
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 136
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: localEulerAnglesRaw.y
    path: Body/Upper/ArmL/ForearmL[1]/HandL
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: 10
        inSlope: 0
        outSlope: 0
        tangentMode: 136
      - serializedVersion: 2
        time: 0.25
        value: 0
        inSlope: -120
        outSlope: -120
        tangentMode: 136
      - serializedVersion: 2
        time: 0.33333334
        value: -40
        inSlope: 0
        outSlope: 0
        tangentMode: 136
      - serializedVersion: 2
        time: 0.5
        value: 10
        inSlope: 0
        outSlope: 0
        tangentMode: 136
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: localEulerAnglesRaw.z
    path: Body/Upper/ArmL/ForearmL[1]/HandL
    classID: 4
    script: {fileID: 0}
  m_EulerEditorCurves:
  - curve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalEulerAngles.z
    path: Body/Upper/ArmL/ForearmL[1]/HandL
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalEulerAngles.y
    path: Body/Upper/ArmL/ForearmL[1]/HandL
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalEulerAngles.x
    path: Body/Upper/ArmL/ForearmL[1]/HandL
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalEulerAngles.z
    path: Body/Upper
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalEulerAngles.y
    path: Body/Upper
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalEulerAngles.x
    path: Body/Upper
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalEulerAngles.z
    path: Body/Upper/ArmR[1]/ForearmR/HandR
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalEulerAngles.y
    path: Body/Upper/ArmR[1]/ForearmR/HandR
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalEulerAngles.x
    path: Body/Upper/ArmR[1]/ForearmR/HandR
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalEulerAngles.z
    path: Body/Upper/ArmL
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalEulerAngles.y
    path: Body/Upper/ArmL
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalEulerAngles.x
    path: Body/Upper/ArmL
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalEulerAngles.z
    path: Body/Upper/ArmR[1]
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalEulerAngles.y
    path: Body/Upper/ArmR[1]
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalEulerAngles.x
    path: Body/Upper/ArmR[1]
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalEulerAngles.z
    path: Body/Upper/ArmR[1]/ForearmR
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalEulerAngles.y
    path: Body/Upper/ArmR[1]/ForearmR
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalEulerAngles.x
    path: Body/Upper/ArmR[1]/ForearmR
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalEulerAngles.z
    path: Body/Upper/Head
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalEulerAngles.y
    path: Body/Upper/Head
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalEulerAngles.x
    path: Body/Upper/Head
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalEulerAngles.x
    path: Body/Upper/ArmL/ForearmL[1]
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalEulerAngles.y
    path: Body/Upper/ArmL/ForearmL[1]
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalEulerAngles.z
    path: Body/Upper/ArmL/ForearmL[1]
    classID: 4
    script: {fileID: 0}
  m_HasGenericRootTransform: 0
  m_HasMotionFloatCurves: 0
  m_GenerateMotionCurves: 0
  m_Events:
  - time: 0.25
    functionName: SetExpression
    data: Angry
    objectReferenceParameter: {fileID: 0}
    floatParameter: 0
    intParameter: 0
    messageOptions: 0
  - time: 0.33333334
    functionName: CustomEvent
    data: Hit
    objectReferenceParameter: {fileID: 0}
    floatParameter: 0
    intParameter: 0
    messageOptions: 0
  - time: 0.5
    functionName: SetExpression
    data: Default
    objectReferenceParameter: {fileID: 0}
    floatParameter: 0
    intParameter: 0
    messageOptions: 0

using System.Collections.Generic;

namespace StateScript
{
    /// <summary>
    /// Node that executes multiple actions simultaneously
    /// Completes when all actions are finished
    /// </summary>
    public class StateActionNode : StateNode
    {
        public List<StateAction> Actions { get; set; } = new List<StateAction>();
        
        // Runtime tracking for simultaneous execution
        private List<float> _actionTimers = new List<float>();
        private List<bool> _actionStarted = new List<bool>();
        
        public override void OnEnter(StateFlow stateFlow, IStateContext context)
        {
            Status = StateNodeStatus.Running;
            
            // Initialize timers for each action
            _actionTimers.Clear();
            _actionStarted.Clear();
            
            for (int i = 0; i < Actions.Count; i++)
            {
                _actionTimers.Add(0f);
                _actionStarted.Add(false);
                Actions[i].Reset();
            }
        }
        
        public override void OnUpdate(StateFlow stateFlow, IStateContext context, float deltaTime)
        {
            if (Actions.Count == 0)
            {
                Status = StateNodeStatus.Completed;
                return;
            }
            
            bool allCompleted = true;
            
            for (int i = 0; i < Actions.Count; i++)
            {
                var action = Actions[i];
                _actionTimers[i] += deltaTime;
                
                // Start action after delay
                if (!_actionStarted[i] && _actionTimers[i] >= action.Delay)
                {
                    _actionStarted[i] = true;
                    action.Status = StateActionStatus.Running;
                    action.OnEnter(stateFlow, context);
                }
                
                // Update running actions
                if (action.Status == StateActionStatus.Running)
                {
                    action.OnUpdate(stateFlow, context, deltaTime);
                    
                    // Check for completion (duration = 0 means instant completion)
                    if (action.Duration == 0f || _actionTimers[i] >= action.Delay + action.Duration)
                    {
                        action.OnExit(stateFlow, context);
                        action.Status = StateActionStatus.Completed;
                    }
                }
                
                if (action.Status != StateActionStatus.Completed)
                    allCompleted = false;
            }
            
            if (allCompleted)
                Status = StateNodeStatus.Completed;
        }
        
        public override void OnExit(StateFlow stateFlow, IStateContext context)
        {
            // Clean up any running actions
            for (int i = 0; i < Actions.Count; i++)
            {
                if (Actions[i].Status == StateActionStatus.Running)
                {
                    Actions[i].OnExit(stateFlow, context);
                    Actions[i].Status = StateActionStatus.Completed;
                }
            }
        }
        
        protected override void InitializePorts()
        {
            Property.Ports.Add(new StatePort("Input", PortType.Input, Property.Id));
            Property.Ports.Add(new StatePort("Output", PortType.Output, Property.Id));
        }
        
        public override void Reset()
        {
            base.Reset();
            foreach (var action in Actions)
            {
                action.Reset();
            }
            _actionTimers.Clear();
            _actionStarted.Clear();
        }
        
        public override void Serialize(ISerializer serializer)
        {
            base.Serialize(serializer);
            serializer.WriteInt("actionCount", Actions.Count);
            
            for (int i = 0; i < Actions.Count; i++)
            {
                serializer.WriteObject($"action_{i}", Actions[i]);
            }
        }
        
        public override void Deserialize(ISerializer serializer)
        {
            base.Deserialize(serializer);
            var actionCount = serializer.ReadInt("actionCount");
            
            Actions.Clear();
            for (int i = 0; i < actionCount; i++)
            {
                // This will need special handling for polymorphic deserialization
                var actionData = serializer.GetSubSerializer($"action_{i}");
                var actionType = actionData.ReadString("actionType");
                var action = StateActionFactory.CreateAction(actionType);
                action.Deserialize(actionData);
                Actions.Add(action);
            }
        }
    }
}

using UnityEngine;
using UnityEditor;
using System.IO;
using StateScript;

namespace StateScript.Editor
{
    /// <summary>
    /// Context menu items for StateFlow assets
    /// </summary>
    public static class StateFlowContextMenu
    {
        [MenuItem("Assets/Create/StateScript/StateFlow", false, 81)]
        public static void CreateStateFlowAsset()
        {
            // Get the selected folder path
            string path = AssetDatabase.GetAssetPath(Selection.activeObject);
            if (string.IsNullOrEmpty(path))
            {
                path = "Assets";
            }
            else if (Path.GetExtension(path) != "")
            {
                path = path.Replace(Path.GetFileName(AssetDatabase.GetAssetPath(Selection.activeObject)), "");
            }
            
            // Generate unique filename with .stateflow extension
            string fileName = "New StateFlow.stateflow";
            string fullPath = Path.Combine(path, fileName);
            int counter = 1;

            while (File.Exists(fullPath))
            {
                fileName = $"New StateFlow {counter}.stateflow";
                fullPath = Path.Combine(path, fileName);
                counter++;
            }
            
            // Create new StateFlow asset
            var newAsset = new StateFlowAsset(Path.GetFileNameWithoutExtension(fileName));
            newAsset.SaveToFile(fullPath);
            
            // Refresh and select the new asset
            AssetDatabase.Refresh();
            var textAsset = AssetDatabase.LoadAssetAtPath<TextAsset>(fullPath);
            Selection.activeObject = textAsset;
            EditorGUIUtility.PingObject(textAsset);
            
            Debug.Log($"Created new StateFlow: {fullPath}");
        }
        
        [MenuItem("Assets/Open StateFlow Editor", true)]
        public static bool ValidateOpenStateFlowEditor()
        {
            if (Selection.activeObject == null)
                return false;

            var path = AssetDatabase.GetAssetPath(Selection.activeObject);

            // Check if it's a StateFlow file (.stateflow) or JSON file
            if (!path.EndsWith(".stateflow") && !path.EndsWith(".json"))
                return false;

            // For .stateflow files, always return true
            if (path.EndsWith(".stateflow"))
                return true;

            // For .json files, check content to see if it's a StateFlow
            var textAsset = Selection.activeObject as TextAsset;
            if (textAsset == null)
                return false;

            return IsStateFlowAsset(textAsset.text, path);
        }
        
        [MenuItem("Assets/Open StateFlow Editor", false, 20)]
        public static void OpenStateFlowEditor()
        {
            if (Selection.activeObject == null)
                return;
            
            var path = AssetDatabase.GetAssetPath(Selection.activeObject);
            var fullPath = Path.Combine(Application.dataPath.Replace("Assets", ""), path);
            
            try
            {
                var asset = StateFlowAsset.LoadFromFile(fullPath);
                StateFlowEditorWindow.OpenWindow(asset);
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Failed to open StateFlow: {e.Message}");
                EditorUtility.DisplayDialog("Error", 
                    $"Failed to open StateFlow:\n{e.Message}", "OK");
            }
        }
        
        [MenuItem("Window/StateScript/Create New StateFlow", false, 1)]
        public static void CreateNewStateFlowFromMenu()
        {
            var asset = new StateFlowAsset("New StateFlow");
            StateFlowEditorWindow.OpenWindow(asset);
        }
        
        /// <summary>
        /// Check if a JSON string represents a StateFlow asset
        /// Uses hybrid approach: content detection + file naming convention
        /// </summary>
        private static bool IsStateFlowAsset(string jsonContent, string filePath)
        {
            // First, try content-based detection
            if (!string.IsNullOrEmpty(jsonContent))
            {
                // Check for StateFlow-specific structure
                if (jsonContent.Contains("\"nodes\"") &&
                    jsonContent.Contains("\"connections\"") &&
                    jsonContent.Contains("\"variables\""))
                {
                    return true;
                }
            }

            // For empty/new files, check naming convention
            var fileName = Path.GetFileNameWithoutExtension(filePath);
            return fileName.Contains("StateFlow") ||
                   fileName.StartsWith("New StateFlow") ||
                   fileName.EndsWith("StateFlow");
        }
        
        /// <summary>
        /// Validate and convert existing JSON files to StateFlow format
        /// </summary>
        [MenuItem("Assets/StateScript/Convert to StateFlow", true)]
        public static bool ValidateConvertToStateFlow()
        {
            if (Selection.activeObject == null)
                return false;

            var path = AssetDatabase.GetAssetPath(Selection.activeObject);
            if (!path.EndsWith(".json"))
                return false;

            var textAsset = Selection.activeObject as TextAsset;
            if (textAsset == null)
                return false;

            // Only show if it's a JSON file but NOT already a StateFlow
            return !IsStateFlowAsset(textAsset.text, path);
        }
        
        [MenuItem("Assets/StateScript/Convert to StateFlow", false, 21)]
        public static void ConvertToStateFlow()
        {
            if (Selection.activeObject == null)
                return;

            var path = AssetDatabase.GetAssetPath(Selection.activeObject);
            var fullPath = Path.Combine(Application.dataPath.Replace("Assets", ""), path);

            // Suggest renaming to .stateflow extension
            var newPath = path.Replace(".json", ".stateflow");
            var newFullPath = Path.Combine(Application.dataPath.Replace("Assets", ""), newPath);

            if (EditorUtility.DisplayDialog("Convert to StateFlow",
                $"This will convert the JSON file to StateFlow format and rename it to use .stateflow extension.\n\nFrom: {Path.GetFileName(path)}\nTo: {Path.GetFileName(newPath)}\n\nAre you sure?",
                "Convert", "Cancel"))
            {
                try
                {
                    var asset = new StateFlowAsset(Path.GetFileNameWithoutExtension(path));
                    asset.SaveToFile(newFullPath);

                    // Delete the old file
                    File.Delete(fullPath);

                    AssetDatabase.Refresh();
                    Debug.Log($"Converted {path} to StateFlow format: {newPath}");

                    // Open in editor
                    StateFlowEditorWindow.OpenWindow(asset);
                }
                catch (System.Exception e)
                {
                    Debug.LogError($"Failed to convert to StateFlow: {e.Message}");
                    EditorUtility.DisplayDialog("Error",
                        $"Failed to convert to StateFlow:\n{e.Message}", "OK");
                }
            }
        }
        
        /// <summary>
        /// Validate StateFlow files for common issues
        /// </summary>
        [MenuItem("Assets/StateScript/Validate StateFlow", true)]
        public static bool ValidateStateFlowValidation()
        {
            return ValidateOpenStateFlowEditor(); // Same validation as opening
        }
        
        [MenuItem("Assets/StateScript/Validate StateFlow", false, 22)]
        public static void ValidateStateFlow()
        {
            if (Selection.activeObject == null)
                return;
            
            var path = AssetDatabase.GetAssetPath(Selection.activeObject);
            var fullPath = Path.Combine(Application.dataPath.Replace("Assets", ""), path);
            
            try
            {
                var asset = StateFlowAsset.LoadFromFile(fullPath);
                
                if (asset.Validate(out var errors))
                {
                    EditorUtility.DisplayDialog("Validation Success", 
                        $"StateFlow '{asset.AssetName}' is valid!", "OK");
                }
                else
                {
                    var errorMessage = $"StateFlow '{asset.AssetName}' has the following issues:\n\n" + 
                                     string.Join("\n", errors);
                    EditorUtility.DisplayDialog("Validation Errors", errorMessage, "OK");
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Failed to validate StateFlow: {e.Message}");
                EditorUtility.DisplayDialog("Error", 
                    $"Failed to validate StateFlow:\n{e.Message}", "OK");
            }
        }
    }
}
